"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for PhieuNhapDieuChinhGiaHangMua model.
"""

from typing import Any, Dict, List, Optional, Tuple  # noqa: F401

from django.db import transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401

from django_ledger.models import (  # noqa: F401,
    ChiTietPhieuNhapDieuChinhGiaHangMuaModel,
    PhieuNhapDieuChinhGiaHangMuaModel,
    ThuePhieuNhapDieuChinhGiaHangMuaModel,
)
from django_ledger.repositories.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_dieu_chinh_gia_hang_mua import (  # noqa: F401,
    ChiTietPhieuNhapDieuChinhGiaHangMuaRepository,
    PhieuNhapDieuChinhGiaHangMuaRepository,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>pDieuChinhGiaHangMuaRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,
from django_ledger.utils_new.debt_management.cong_no_creation import (  # noqa: F401,
    CongNoCreation,
)


class PhieuNhapDieuChinhGiaHangMuaService(BaseService):
    """
    Service class for handling PhieuNhapDieuChinhGiaHangMua business logic.
    Implements the Service pattern for PhieuNhapDieuChinhGiaHangMua.

    ✅ ENHANCED: Integrated with Unified Accounting Service
    ERP Expert - 20 năm kinh nghiệm
    """

    # ✅ PREDEFINED CONFIGURATION: Purchase price adjustment accounting mappings
    # Multiple journal types based on ma_ngv logic with enhanced conditional logic
    PRICE_ADJUSTMENT_ACCOUNTING_CONFIG = [
        # ma_ngv = 1: GIẢM (Decrease)
        {
            'journal_type': 'CNGIAM',  # Công nợ giảm
            'debit_account_field': 'tk',  # Header account - DEBIT
            'credit_account_field': 'tk_vt',  # Detail material account - CREDIT
            'debit_account_source': 'header',  # Lấy debit từ header
            'credit_account_source': 'detail',  # Lấy credit từ detail
            'amount_field': 'tien',  # Amount
            'detail_source': 'chi_tiet_list',  # Related name to detail
            'detail_conditions': {  # ✅ ENHANCED: Conditional logic
                'tien': {'gt': 0},  # tien > 0
                'tk_vt': {'is_not_null': True},  # tk_vt IS NOT NULL
            },
            'canCreate': True,  # Default: conditional creation
        },
        {
            'journal_type': 'THUEGIAM',  # Thuế giảm
            'debit_account_field': 'tk',  # Header account - DEBIT
            'credit_account_field': 'tk_thue_no',  # Detail tax debit account - CREDIT
            'debit_account_source': 'header',  # Lấy debit từ header
            'credit_account_source': 'detail',  # Lấy credit từ detail
            'amount_field': 'thue',  # Tax amount
            'detail_source': 'chi_tiet_thue_list',  # Related name to tax detail
            'detail_conditions': {  # ✅ ENHANCED: Conditional logic
                'thue': {'gt': 0},  # thue > 0
                'tk_thue_no': {'is_not_null': True},  # tk_thue_no IS NOT NULL
            },
            'canCreate': True,  # Default: conditional creation
        },
        # ma_ngv = 2: TĂNG (Increase)
        {
            'journal_type': 'CNTANG',  # Công nợ tăng
            'debit_account_field': 'tk_vt',  # Detail material account - DEBIT
            'credit_account_field': 'tk',  # Header account - CREDIT
            'debit_account_source': 'detail',  # Lấy debit từ detail
            'credit_account_source': 'header',  # Lấy credit từ header
            'amount_field': 'tien',  # Amount
            'detail_source': 'chi_tiet_list',  # Related name to detail
            'detail_conditions': {  # ✅ ENHANCED: Conditional logic
                'tien': {'gt': 0},  # tien > 0
                'tk_vt': {'is_not_null': True},  # tk_vt IS NOT NULL
            },
            'canCreate': True,  # Default: conditional creation
        },
        {
            'journal_type': 'THUETANG',  # Thuế tăng
            'debit_account_field': 'tk_thue_no',  # Detail tax debit account - DEBIT
            'credit_account_field': 'tk',  # Header account - CREDIT
            'debit_account_source': 'detail',  # Lấy debit từ detail
            'credit_account_source': 'header',  # Lấy credit từ header
            'amount_field': 'thue',  # Tax amount
            'detail_source': 'chi_tiet_thue_list',  # Related name to tax detail
            'detail_conditions': {  # ✅ ENHANCED: Conditional logic
                'thue': {'gt': 0},  # thue > 0
                'tk_thue_no': {'is_not_null': True},  # tk_thue_no IS NOT NULL
            },
            'canCreate': True,  # Default: conditional creation
        },
    ]

    def __init__(self):  # noqa: C901
        """
        Initialize the service.
        """
        super().__init__()
        self.repository = PhieuNhapDieuChinhGiaHangMuaRepository()
        self.chi_tiet_repository = ChiTietPhieuNhapDieuChinhGiaHangMuaRepository()
        self.thue_repository = ThuePhieuNhapDieuChinhGiaHangMuaRepository()

        # ✅ ĐƠN GIẢN: Khởi tạo unified accounting service
        self._cong_no_service = CongNoCreation()

    def _determine_accounting_mappings(
        self, phieu_nhap: PhieuNhapDieuChinhGiaHangMuaModel
    ) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định accounting mappings dựa trên ma_ngv và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Conditional journal types dựa trên ma_ngv (1=GIAM, 2=TANG)
        - Status-based creation (chỉ tạo khi status IN ('3', '5'))
        - Detail-level conditional creation (amount > 0 AND account IS NOT NULL)
        - Support flexible business rules cho future enhancements

        Parameters
        ----------
        phieu_nhap : PhieuNhapDieuChinhGiaHangMuaModel
            Phiếu nhập điều chỉnh giá để analyze

        Returns
        -------
        List[Dict[str, Any]]
            Danh sách accounting mappings với journal_type và canCreate được set theo business logic
        """
        mappings = self.PRICE_ADJUSTMENT_ACCOUNTING_CONFIG.copy()

        # ✅ BUSINESS LOGIC: Chỉ tạo bút toán khi status IN ('3', '5')
        if phieu_nhap.status not in ['3', '5']:
            # Return empty mappings - không tạo bút toán
            for mapping in mappings:
                mapping['canCreate'] = False

            return mappings

        # ✅ BUSINESS LOGIC: Determine journal types based on ma_ngv
        ma_ngv = getattr(phieu_nhap, 'ma_ngv', '1')  # Default to '1'

        if ma_ngv == '1':
            # GIẢM: CNGIAM, THUEGIAM
            journal_types = ['CNGIAM', 'THUEGIAM']
        elif ma_ngv == '2':
            # TĂNG: CNTANG, THUETANG
            journal_types = ['CNTANG', 'THUETANG']
        else:
            # Default fallback to GIẢM
            journal_types = ['CNGIAM', 'THUEGIAM']

        # ✅ BUSINESS LOGIC: Filter mappings by journal types
        for config in self.PRICE_ADJUSTMENT_ACCOUNTING_CONFIG:
            if config['journal_type'] in journal_types:
                mapping = config.copy()

                # ✅ CONDITIONAL CREATION: Will be validated at detail level
                # CongNoCreation will check amount > 0 AND account IS NOT NULL for each detail
                mapping['canCreate'] = True

                mappings.append(mapping)

        return mappings

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the base queryset for PhieuNhapDieuChinhGiaHangMuaModel.

        Returns
        -------
        QuerySet
            The base queryset
        """
        return self.repository.get_queryset()

    def list(  # noqa: C901
        self,
        entity_slug: str,
        search_query: str = None,
        status: str = None,
        from_date=None,
        to_date=None,
    ) -> QuerySet:
        """
        Get a list of PhieuNhapDieuChinhGiaHangMua instances

        Parameters
        ----------
        entity_slug : str
            The entity slug
        search_query : str, optional
            Search query to filter results, by default None
        status : str, optional
            Status filter, by default None
        from_date : date, optional
            Start date filter, by default None
        to_date : date, optional
            End date filter, by default None

        Returns
        -------
        QuerySet
            QuerySet of PhieuNhapDieuChinhGiaHangMua instances
        """
        # Get data from repository with filters
        return self.repository.list(
            entity_slug=entity_slug,
            search_query=search_query,
            status=status,
            from_date=from_date,
            to_date=to_date,
        )

    def get(
        self, entity_slug: str, uuid: str
    ) -> Optional[PhieuNhapDieuChinhGiaHangMuaModel]:  # noqa: C901
        """
        Get a PhieuNhapDieuChinhGiaHangMua instance by UUID

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : str
            The UUID of the PhieuNhapDieuChinhGiaHangMua instance

        Returns
        -------
        Optional[PhieuNhapDieuChinhGiaHangMuaModel]
            The PhieuNhapDieuChinhGiaHangMua instance, or None if not found
        """
        return self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

    @transaction.atomic
    @transaction.atomic
    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> PhieuNhapDieuChinhGiaHangMuaModel:  # noqa: C901
        """
        ✅ ENHANCED: Create a new PhieuNhapDieuChinhGiaHangMua instance with automatic accounting.

        Tự động tạo bút toán kế toán cho phiếu nhập điều chỉnh giá sử dụng unified services.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Atomic transaction cho cả accounting và document creation
        - Status-based business logic cho entry creation (3,5)
        - ma_ngv conditional logic cho journal types
        - Complete audit trail cho financial impacts
        - Transaction safety với automatic rollback

        Parameters
        ----------
        entity_slug : str
            The entity slug
        data : Dict[str, Any]
            The data for the new instance

        Returns
        -------
        PhieuNhapDieuChinhGiaHangMuaModel
            The created PhieuNhapDieuChinhGiaHangMua instance
        """
        # Create the instance using repository
        instance = self.repository.create(entity_slug=entity_slug, data=data)

        # ✅ UNIFIED ACCOUNTING: Tạo bút toán kế toán
        # Only create accounting if ledger is not already assigned to avoid UNIQUE constraint
        if not instance.ledger:
            try:
                mappings = self._determine_accounting_mappings(instance)
                self._cong_no_service.create_document_accounting_entries(
                    source_document=instance,
                    document_type="phiếu nhập điều chỉnh giá hàng mua",
                    account_mappings=mappings,
                )
            except Exception as e:
                # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                # to maintain data consistency between PhieuNhap and accounting entries
                raise Exception(
                    f"Failed to create accounting entry for PhieuNhapDieuChinhGia {instance.so_ct}: {str(e)}"
                ) from e
        # Note: If ledger already exists, skip accounting creation to avoid duplicates

        return instance

    @transaction.atomic
    @transaction.atomic
    def update(
        self, entity_slug: str, uuid: str, data: Dict[str, Any]
    ) -> PhieuNhapDieuChinhGiaHangMuaModel:  # noqa: C901
        """
        ✅ ENHANCED: Update an existing PhieuNhapDieuChinhGiaHangMua instance with automatic accounting.

        Tự động cập nhật bút toán kế toán cho phiếu nhập điều chỉnh giá sử dụng unified services.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : str
            The UUID of the instance to update
        data : Dict[str, Any]
            The data to update

        Returns
        -------
        PhieuNhapDieuChinhGiaHangMuaModel
            The updated PhieuNhapDieuChinhGiaHangMua instance

        Raises
        ------
        PhieuNhapDieuChinhGiaHangMuaModel.DoesNotExist
            If the instance does not exist
        """
        # Update the instance using repository
        instance = self.repository.update(entity_slug=entity_slug, uuid=uuid, data=data)

        # ✅ UNIFIED ACCOUNTING: Cập nhật bút toán kế toán
        # Only update accounting if ledger exists
        if instance.ledger:
            try:
                self._cong_no_service.update_document_accounting_entries(
                    source_document=instance,
                    document_type="phiếu nhập điều chỉnh giá hàng mua",
                    account_mappings=self._determine_accounting_mappings(instance),
                )
            except Exception as e:
                # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                # to maintain data consistency between PhieuNhap and accounting entries
                raise Exception(
                    f"Failed to update accounting entry for PhieuNhapDieuChinhGia {instance.so_ct}: {str(e)}"
                ) from e
        # Note: If no ledger exists, skip accounting update

        return instance

    def delete(self, entity_slug: str, uuid: str) -> None:  # noqa: C901
        """
        Delete a PhieuNhapDieuChinhGiaHangMua instance

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : str
            The UUID of the instance to delete

        Raises
        ------
        PhieuNhapDieuChinhGiaHangMuaModel.DoesNotExist
            If the instance does not exist
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not instance:
            return False
        if instance.ledger:
            self._cong_no_service.delete_document_accounting_entries(
                source_document=instance
            )
            return True
        else:
            return self.repository.delete(entity_slug=entity_slug, uuid=uuid)

    @transaction.atomic
    def create_with_details(  # noqa: C901
        self,
        entity_slug: str,
        data: Dict[str, Any],
        chi_tiet_data: List[Dict[str, Any]] = None,
        thue_data: List[Dict[str, Any]] = None,
    ) -> PhieuNhapDieuChinhGiaHangMuaModel:
        """
        Create a new PhieuNhapDieuChinhGiaHangMua instance with its details and tax records  # noqa: E501

        Parameters
        ----------
        entity_slug : str
            The entity slug
        data : Dict[str, Any]
            The data for the new PhieuNhapDieuChinhGiaHangMua instance
        chi_tiet_data : List[Dict[str, Any]], optional
            The data for the ChiTietPhieuNhapDieuChinhGiaHangMua instances
        thue_data : List[Dict[str, Any]], optional
            The data for the ThuePhieuNhapDieuChinhGiaHangMua instances

        Returns
        -------
        PhieuNhapDieuChinhGiaHangMuaModel
            The created PhieuNhapDieuChinhGiaHangMua instance
        """
        # Create the main instance (without accounting yet)
        instance = self.repository.create(entity_slug=entity_slug, data=data)

        # Create chi_tiet instances
        if chi_tiet_data:
            try:
                # Use the repository directly for better performance
                chi_tiet_repository = self.chi_tiet_repository
                # Process each chi_tiet item
                for item in chi_tiet_data:
                    chi_tiet_repository.create(phieu_nhap=instance, data=item)
            except Exception as e:
                # ⚠️ CRITICAL: Chi tiet creation failure should fail the entire transaction
                # to maintain data consistency between PhieuNhap and chi_tiet
                raise Exception(
                    f"Failed to create chi_tiet for PhieuNhapDieuChinhGia {instance.so_ct}: {str(e)}"
                ) from e

        # Create thue instances
        if thue_data:
            try:
                # Use the repository directly for better performance
                thue_repository = self.thue_repository
                # Process each thue item
                for item in thue_data:
                    thue_repository.create(phieu_nhap=instance, data=item)
            except Exception as e:
                # ⚠️ CRITICAL: Thue creation failure should fail the entire transaction
                # to maintain data consistency between PhieuNhap and thue
                raise Exception(
                    f"Failed to create thue for PhieuNhapDieuChinhGia {instance.so_ct}: {str(e)}"
                ) from e

        # ✅ UNIFIED ACCOUNTING: Tạo bút toán kế toán sau khi có đầy đủ chi tiết
        # Only create accounting if ledger is not already assigned to avoid UNIQUE constraint
        if not instance.ledger:
            try:
                self._cong_no_service.create_document_accounting_entries(
                    source_document=instance,
                    document_type="phiếu nhập điều chỉnh giá hàng mua",
                    account_mappings=self._determine_accounting_mappings(instance),
                )
            except Exception as e:
                # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                # to maintain data consistency between PhieuNhap and accounting entries
                raise Exception(
                    f"Failed to create accounting entry for PhieuNhapDieuChinhGia {instance.so_ct}: {str(e)}"
                ) from e
        # Note: If ledger already exists, skip accounting creation to avoid duplicates

        return instance

    @transaction.atomic
    def update_with_details(  # noqa: C901
        self,
        entity_slug: str,
        uuid: str,
        data: Dict[str, Any],
        chi_tiet_data: List[Dict[str, Any]] = None,
        thue_data: List[Dict[str, Any]] = None,
    ) -> PhieuNhapDieuChinhGiaHangMuaModel:
        """
        Update an existing PhieuNhapDieuChinhGiaHangMua instance with its details and tax records  # noqa: E501

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : str
            The UUID of the instance to update
        data : Dict[str, Any]
            The data for updating the PhieuNhapDieuChinhGiaHangMua instance
        chi_tiet_data : List[Dict[str, Any]], optional
            The data for updating or creating ChiTietPhieuNhapDieuChinhGiaHangMua instances  # noqa: E501
        thue_data : List[Dict[str, Any]], optional
            The data for updating or creating ThuePhieuNhapDieuChinhGiaHangMua instances

        Returns
        -------
        PhieuNhapDieuChinhGiaHangMuaModel
            The updated PhieuNhapDieuChinhGiaHangMua instance
        """
        # Update the main instance (without accounting yet)
        instance = self.repository.update(entity_slug=entity_slug, uuid=uuid, data=data)
        # Process chi_tiet instances
        if chi_tiet_data:
            # Use the repository directly for better performance
            chi_tiet_repository = self.chi_tiet_repository
            # Get existing chi_tiet records for this instance
            existing_chi_tiet = instance.chi_tiet_list.all()
            existing_chi_tiet_dict = {str(ct.uuid): ct for ct in existing_chi_tiet}
            # Track existing line numbers to avoid conflicts
            existing_lines = {ct.line for ct in existing_chi_tiet}
            processed_uuids = set()
            # Process each chi_tiet item
            for item in chi_tiet_data:
                item_uuid = item.get('uuid')
                if item_uuid and str(item_uuid) in existing_chi_tiet_dict:
                    # Update existing chi_tiet
                    _ = existing_chi_tiet_dict[str(item_uuid)]
                    chi_tiet_repository.update(
                        phieu_nhap_uuid=uuid, uuid=item_uuid, data=item
                    )
                    processed_uuids.add(str(item_uuid))
                else:
                    # Create new chi_tiet
                    # Ensure line number doesn't conflict
                    if 'line' not in item:
                        max_line = max(existing_lines) if existing_lines else 0
                        item['line'] = max_line + 1
                        existing_lines.add(item['line'])

                    chi_tiet_repository.create(phieu_nhap=instance, data=item)
            # Delete chi_tiet records that were not processed (removed)
            for uuid_str, chi_tiet_instance in existing_chi_tiet_dict.items():
                if uuid_str not in processed_uuids:
                    chi_tiet_repository.delete(
                        phieu_nhap_uuid=uuid, uuid=chi_tiet_instance.uuid
                    )

        # Process thue instances
        if thue_data:
            # Use the repository directly for better performance
            thue_repository = self.thue_repository
            # Get existing thue records for this instance
            existing_thue = instance.chi_tiet_thue_list.all()
            existing_thue_dict = {str(th.uuid): th for th in existing_thue}
            processed_uuids = set()
            # Process each thue item
            for item in thue_data:
                item_uuid = item.get('uuid')
                if item_uuid and str(item_uuid) in existing_thue_dict:
                    # Update existing thue
                    thue_repository.update(
                        phieu_nhap_uuid=uuid, uuid=item_uuid, data=item
                    )
                    processed_uuids.add(str(item_uuid))
                else:
                    # Create new thue
                    thue_repository.create(phieu_nhap=instance, data=item)
            # Delete thue records that were not processed (removed)
            for uuid_str, thue_instance in existing_thue_dict.items():
                if uuid_str not in processed_uuids:
                    thue_repository.delete(
                        phieu_nhap_uuid=uuid, uuid=thue_instance.uuid
                    )

        # ✅ UNIFIED ACCOUNTING: Cập nhật bút toán kế toán sau khi có đầy đủ chi tiết
        # Only update accounting if ledger exists
        if instance.ledger:
            try:
                self._cong_no_service.update_document_accounting_entries(
                    source_document=instance,
                    document_type="phiếu nhập điều chỉnh giá hàng mua",
                    account_mappings=self._determine_accounting_mappings(instance),
                )
            except Exception as e:
                # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                # to maintain data consistency between PhieuNhap and accounting entries
                raise Exception(
                    f"Failed to update accounting entry for PhieuNhapDieuChinhGia {instance.so_ct}: {str(e)}"
                ) from e
        # Note: If no ledger exists, skip accounting update

        return instance
