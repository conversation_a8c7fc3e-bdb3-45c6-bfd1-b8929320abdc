"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Chi Tiet Hoa Don Ban Hang (Sales Invoice Detail) repository implementation.
"""

from typing import Any, Dict, Optional, Union
from uuid import UUID

from django.db.models import QuerySet  # noqa: F401

from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import (
    ChiTietHoaDonBanHangModel,
    HoaDonBanHangModel,
)
from django_ledger.repositories.base import BaseRepository


class ChiTietHoaDonBanHangRepository(BaseRepository):
    """
    Repository class for handling ChiTietHoaDonBanHang model database operations.
    Implements the Repository pattern for ChiTietHoaDonBanHang.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the repository with the ChiTietHoaDonBanHangModel.
        """
        super().__init__(model_class=ChiTietHoaDonBanHangModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for ChiTietHoaDonBanHangModel.

        Returns
        -------
        QuerySet
            The base queryset for ChiTietHoaDonBanHangModel.
        """
        return self.model_class.objects.all().select_related(
            'hoa_don_ban_hang', 'ma_vt', 'dvt', 'ma_kho'
        )

    def get_by_hoa_don(self, hoa_don_id) -> QuerySet:  # noqa: C901
        """
        Retrieves ChiTietHoaDonBanHangModel instances for a specific HoaDonBanHangModel.

        Parameters
        ----------
        hoa_don_id : UUID
            The UUID of the HoaDonBanHangModel to filter by.

        Returns
        -------
        QuerySet
            A queryset of ChiTietHoaDonBanHangModel instances for the specified HoaDonBanHangModel.  # noqa: E501
        """
        return self.model_class.objects.for_hoa_don(hoa_don_id=hoa_don_id)

    def get_by_id(
        self, uuid: Union[str, UUID]
    ) -> Optional[ChiTietHoaDonBanHangModel]:  # noqa: C901
        """
        Retrieves a ChiTietHoaDonBanHangModel by its UUID.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietHoaDonBanHangModel to retrieve.

        Returns
        -------
        Optional[ChiTietHoaDonBanHangModel]
            The ChiTietHoaDonBanHangModel with the given UUID, or None if not found.
        """
        try:
            return self.get_queryset().get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def create(
        self, parent: HoaDonBanHangModel, data: Dict[str, Any]
    ) -> ChiTietHoaDonBanHangModel:
        """
        Creates a new ChiTietHoaDonBanHangModel instance.

        Parameters
        ----------
        parent : HoaDonBanHangModel
            The parent HoaDonBanHangModel instance.
        data : Dict[str, Any]
            The data for the new ChiTietHoaDonBanHangModel.

        Returns
        -------
        ChiTietHoaDonBanHangModel
            The created ChiTietHoaDonBanHangModel instance.
        """

        # Convert UUID strings to model instances
        data_copy = self.convert_uuids_to_model_instances(data)
        # Create the instance
        instance = self.model_class(hoa_don_ban_hang=parent, **data_copy)
        instance.save()

        return instance

    def update(
        self, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> ChiTietHoaDonBanHangModel:
        """
        Updates an existing ChiTietHoaDonBanHangModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietHoaDonBanHangModel to update.
        data : Dict[str, Any]
            The data to update.

        Returns
        -------
        ChiTietHoaDonBanHangModel
            The updated ChiTietHoaDonBanHangModel instance.
        """
        # Convert UUID strings to model instances
        data_copy = self.convert_uuids_to_model_instances(data)

        # Get the instance
        instance = self.get_by_id(uuid)
        if not instance:
            raise self.model_class.DoesNotExist(
                f"ChiTietHoaDonBanHang with uuid {uuid} does not exist"
            )

        # Update the instance
        for key, value in data_copy.items():
            setattr(instance, key, value)
        instance.save()

        return instance

    def delete(self, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a ChiTietHoaDonBanHangModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietHoaDonBanHangModel to delete.

        Returns
        -------
        bool
            True if the deletion was successful, False otherwise.
        """
        try:
            instance = self.get_by_id(uuid)
            if instance:
                instance.delete()
                return True
            return False
        except Exception:
            return False

    def convert_uuids_to_model_instances(self, data):
        """
        Override to exclude id_dh and line_dh fields from UUID conversion.
        These fields should remain as their original values (model instances or strings).
        """
        # Pop uuid before conversion to prevent it from being converted to a model instance
        uuid_val = data.pop('uuid', None)

        # Call parent conversion
        converted_data = super().convert_uuids_to_model_instances(data)

        # Add uuid back if it existed
        if uuid_val:
            converted_data['uuid'] = uuid_val

        return converted_data
