"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiTietHoaDonMuaHangTrongNuocModel, which represents the line items  # noqa: E501
in a Domestic Purchase Invoice.
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ChiTietHoaDonMuaHangTrongNuocModelQueryset(models.QuerySet):
    """
    A custom defined ChiTietHoaDonMuaHangTrongNuocModel QuerySet.
    """

    def for_hoa_don(self, hoa_don_id):  # noqa: C901
        """
        Returns line items for a specific invoice.

        Parameters
        ----------
        hoa_don_id: UUID
            The HoaDonMuaHangTrongNuocModel UUID used for filtering the QuerySet.

        Returns
        -------
        ChiTietHoaDonMuaHangTrongNuocModelQueryset
            A QuerySet of ChiTietHoaDonMuaHangTrongNuocModel with applied filters.
        """
        return self.filter(hoa_don_id=hoa_don_id)


class ChiTietHoaDonMuaHangTrongNuocModelManager(models.Manager):
    """
    A custom defined ChiTietHoaDonMuaHangTrongNuocModel Manager that will act as an interface to handle the  # noqa: E501
    ChiTietHoaDonMuaHangTrongNuocModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom ChiTietHoaDonMuaHangTrongNuocModelQueryset.
        """
        return ChiTietHoaDonMuaHangTrongNuocModelQueryset(self.model, using=self._db)


class ChiTietHoaDonMuaHangTrongNuocModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiTietHoaDonMuaHangTrongNuocModel database will inherit from.  # noqa: E501
    The ChiTietHoaDonMuaHangTrongNuocModel inherits functionality from the following MixIns:  # noqa: E501

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUIDField
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    hoa_don : ForeignKey
        The HoaDonMuaHangTrongNuocModel this line item belongs to.
    line : IntegerField
        Line number in the invoice.
    ma_vt : CharField
        Material code.
    dvt : CharField
        Unit of measure.
    ten_dvt : CharField
        Unit of measure name.
    ma_kho : CharField
        Warehouse code.
    ten_kho : CharField
        Warehouse name.
    ma_lo : CharField
        Lot code.
    ten_lo : CharField
        Lot name.
    so_luong : DecimalField
        Quantity.
    gia_nt0 : DecimalField
        Price in foreign currency.
    tien_nt0 : DecimalField
        Amount in foreign currency.
    cp_nt : DecimalField
        Cost in foreign currency.
    tl_ck : DecimalField
        Discount percentage.
    ck_nt : DecimalField
        Discount amount in foreign currency.
    """

    uuid = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    hoa_don = models.ForeignKey(
        "django_ledger.HoaDonMuaHangTrongNuocModel",
        on_delete=models.CASCADE,
        related_name="chi_tiet_hoa_don",
        verbose_name=_("Hóa đơn"),
    )

    # Line information
    line = models.IntegerField(verbose_name=_("Số dòng"))
    # Material information
    ma_vt = models.ForeignKey(
        "django_ledger.VatTuModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã vật tư"),
        related_name="chi_tiet_hoa_don_mua_hang_trong_nuoc",
        null=True,
        blank=True,
    )
    ten_vt0 = models.CharField(max_length=50, verbose_name=_("Mã vật tư"))
    dvt = models.ForeignKey(
        "django_ledger.DonViTinhModel",
        on_delete=models.CASCADE,
        verbose_name=_("Đơn vị tính"),
        related_name="chi_tiet_hoa_don_mua_hang_trong_nuoc",
        null=True,
        blank=True,
    )
    ten_dvt0 = models.CharField(max_length=20, verbose_name=_("Đơn vị tính"))

    # Warehouse information
    ma_kho = models.ForeignKey(
        "django_ledger.KhoHangModel",
        on_delete=models.CASCADE,
        verbose_name=_("Kho"),
        related_name="chi_tiet_hoa_don_mua_hang_trong_nuoc",
        null=True,
        blank=True,
    )

    # Lot information
    ma_lo = models.ForeignKey(
        "django_ledger.LoModel",
        on_delete=models.CASCADE,
        verbose_name=_("Lô"),
        related_name="chi_tiet_hoa_don_mua_hang_trong_nuoc",
        null=True,
        blank=True,
    )
    ten_lo = models.CharField(max_length=255, blank=True, verbose_name=_("Tên lô"))
    lo_yn = models.BooleanField(default=False, verbose_name=_("Lô Y/N"))

    # Location information
    ma_vi_tri = models.ForeignKey(
        "django_ledger.ViTriModel",
        on_delete=models.SET_NULL,
        verbose_name=_("Mã vị trí"),
        related_name="chi_tiet_hoa_don_mua_hang_trong_nuoc",
        null=True,
        blank=True,
    )
    vi_tri_yn = models.BooleanField(default=False, verbose_name=_("Vị trí Y/N"))

    # Additional fields
    he_so = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=1,
        verbose_name=_("Hệ số"),
    )
    qc_yn = models.BooleanField(default=False, verbose_name=_("QC Y/N"))
    px_dd = models.BooleanField(default=False, verbose_name=_("Phiếu xuất đã duyệt"))
    # Quantity and price information
    so_luong = models.DecimalField(
        max_digits=15, decimal_places=2, verbose_name=_("Số lượng")
    )
    gia_nt0 = models.DecimalField(
        max_digits=15, decimal_places=2, verbose_name=_("Giá VND")
    )
    tien_nt0 = models.DecimalField(
        max_digits=15, decimal_places=2, verbose_name=_("Tiền hàng VND")
    )
    tien0 = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Tiền hàng"),
        null=True,
        blank=True,
    )
    cp_nt = models.DecimalField(
        max_digits=15, decimal_places=2, verbose_name=_("Chi phí VND")
    )
    gia_ton = models.DecimalField(
        max_digits=15, decimal_places=2, verbose_name=_("Giá tồn")
    )
    tl_ck = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Tỷ lệ chiết khấu"),
        default=0,
    )
    ck_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Chiết khấu VND"),
        default=0,
    )
    ck = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Chiết khấu"),
        default=0,
    )
    tien_tck = models.DecimalField(
        max_digits=15, decimal_places=2, verbose_name=_("Tiền trước chiếc khấu")
    )
    tien_tck_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Tiền trước chiếc khấu ngoại tệ"),
    )
    thue_xuat = models.ForeignKey(
        "django_ledger.ThueSuatThueGTGTModel",
        on_delete=models.CASCADE,
        verbose_name=_("Thue xuat"),
        related_name="chi_tiet_hoa_don_mua_hang_trong_nuoc",
        null=True,
        blank=True,
    )
    ma_thue = models.ForeignKey(
        'django_ledger.TaxModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã thuế'),
        help_text=_('Tax code'),
        related_name='chi_tiet_hoa_don_mua_hang_trong_nuoc',
        null=True,
        blank=True,
    )
    thue_nt = models.DecimalField(
        max_digits=15, decimal_places=2, verbose_name=_("Thue VND")
    )
    tk_vt = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.CASCADE,
        related_name="chi_tiet_hoa_don_mua_hang_trong_nuoc",
        verbose_name=_("TK No"),
        null=True,
        blank=True,
    )

    tk_cpxt = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.CASCADE,
        related_name="chi_tiet_hoa_don_mua_hang_trong_nuoc_tk_cpxt",
        verbose_name=_("TK chi phí xuất"),
        null=True,
        blank=True,
    )
    # Additional account fields
    ma_nx = models.ForeignKey(
        "django_ledger.NhapXuatModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã nhập xuất"),
        related_name="chi_tiet_hoa_don_mua_hang_trong_nuoc",
        null=True,
        blank=True,
    )
    tk_du = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.CASCADE,
        related_name="chi_tiet_hoa_don_mua_hang_trong_nuoc_tk_du",
        verbose_name=_("Tài khoản dư"),
        null=True,
        blank=True,
    )

    ma_bp = models.ForeignKey(
        "django_ledger.BoPhanModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã bộ phận"),
        related_name="hoa_don_mua_hang_trong_nuoc_ma_bp",
        null=True,
        blank=True,
    )
    ma_vv = models.ForeignKey(
        "django_ledger.VuViecModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã vụ việc"),
        related_name="hoa_don_mua_hang_trong_nuoc_ma_vv",
        null=True,
        blank=True,
    )
    ma_hd = models.ForeignKey(
        "django_ledger.ContractModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã hợp đồng"),
        related_name="hoa_don_mua_hang_trong_nuoc_ma_hd",
        null=True,
        blank=True,
    )
    ma_dtt = models.ForeignKey(
        "django_ledger.DotThanhToanModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã đợt thanh toán"),
        related_name="hoa_don_mua_hang_trong_nuoc_ma_dtt",
        null=True,
        blank=True,
    )
    ma_ku = models.ForeignKey(
        "django_ledger.KheUocModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã khế ước"),
        related_name="hoa_don_mua_hang_trong_nuoc_ma_ku",
        null=True,
        blank=True,
    )
    ma_phi = models.ForeignKey(
        "django_ledger.PhiModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã phí"),
        related_name="hoa_don_mua_hang_trong_nuoc_ma_phi",
        null=True,
        blank=True,
    )
    ma_lsx = models.CharField(_("Mã lệnh sản xuất"), max_length=50, blank=True)
    ma_cp0 = models.ForeignKey(
        "django_ledger.ChiPhiKhongHopLeModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã chi phí không hợp lệ"),
        related_name="hoa_don_mua_hang_trong_nuoc_ma_cp0",
        null=True,
        blank=True,
    )
    ma_sp = models.ForeignKey(
        "django_ledger.VatTuModel",
        on_delete=models.SET_NULL,
        verbose_name=_("Mã sản phẩm"),
        related_name="chi_tiet_hoa_don_mua_hang_trong_nuoc_ma_sp",
        null=True,
        blank=True,
    )

    # Additional fields for purchase order reference (matching database schema)
    id_dh = models.CharField(
        max_length=36,
        verbose_name=_("ID đơn hàng"),
        help_text=_("Purchase order ID"),
        blank=True,
        null=True,
    )
    id_hd4 = models.CharField(
        max_length=36,
        verbose_name=_("ID HD4"),
        help_text=_("HD4 ID"),
        blank=True,
        null=True,
    )
    line_dh = models.CharField(
        max_length=36,
        verbose_name=_("Line đơn hàng"),
        help_text=_("Purchase order line UUID (ChiTietDonMuaHangModel.uuid)"),
        blank=True,
        null=True,
    )
    line_hd4 = models.IntegerField(
        verbose_name=_("Line HD4"),
        help_text=_("HD4 line number"),
        blank=True,
        null=True,
    )
    so_ct_dh = models.CharField(
        max_length=100,
        verbose_name=_("Số CT đơn hàng"),
        help_text=_("Purchase order document number"),
        blank=True,
        null=True,
    )
    so_ct_hd4 = models.CharField(
        max_length=100,
        verbose_name=_("Số CT HD4"),
        help_text=_("HD4 document number"),
        blank=True,
        null=True,
    )
    so_ct_pn = models.CharField(
        max_length=255,
        verbose_name=_("Số chứng từ phiếu nhập"),
        help_text=_("Receipt document number"),
        blank=True,
        null=True,
    )

    sl_cl = models.IntegerField(default=0, null=True, blank=True)
    objects = ChiTietHoaDonMuaHangTrongNuocModelManager()

    class Meta:
        abstract = True
        verbose_name = _("Chi tiết hóa đơn mua hàng trong nước")
        verbose_name_plural = _("Chi tiết hóa đơn mua hàng trong nước")
        indexes = [
            models.Index(fields=["hoa_don"]),
            models.Index(fields=["ma_vt"]),
            models.Index(fields=["ma_kho"]),
            models.Index(fields=["ma_nx"]),
            models.Index(fields=["ma_bp"]),
            models.Index(fields=["ma_vv"]),
            models.Index(fields=["ma_thue"]),
        ]
        ordering = ["line"]

    def __str__(self):  # noqa: C901
        return f"{self.hoa_don} - {self.line} - {self.ma_vt}"

    def save(self, *args, **kwargs):  # noqa: C901
        # Auto-calculate amount if not provided
        if self.so_luong is not None and self.gia_nt0 is not None:
            if not self.tien_nt0:
                self.tien_nt0 = self.so_luong * self.gia_nt0
        super().save(*args, **kwargs)


class ChiTietHoaDonMuaHangTrongNuocModel(ChiTietHoaDonMuaHangTrongNuocModelAbstract):
    """
    Base ChiTietHoaDonMuaHangTrongNuocModel from Abstract.
    """

    class Meta(ChiTietHoaDonMuaHangTrongNuocModelAbstract.Meta):
        abstract = False
        db_table = "chi_tiet_hoa_don_mua_hang_trong_nuoc"
