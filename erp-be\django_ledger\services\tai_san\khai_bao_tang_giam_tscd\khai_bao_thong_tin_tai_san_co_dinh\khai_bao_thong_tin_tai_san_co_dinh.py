"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the KhaiBaoThongTinTaiSanCoDinhService, which handles business logic  # noqa: E501
for the KhaiBaoThongTinTaiSanCoDinhModel.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import transaction  # noqa: F401,
from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models import (  # noqa: F401,
    KhaiBaoThongTinTaiSanCoDinhModel,
)
from django_ledger.repositories.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh import (  # noqa: F401,
    KhaiBaoThongTinTaiSanCoDinhRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,
from django_ledger.services.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh.chi_tiet_doi_tuong_hach_toan_tscd import (  # noqa: F401,
    ChiTietDoiTuongHachToanTSCDService,
)
from django_ledger.services.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh.chi_tiet_phu_tung_kem_theo_tscd import (  # noqa: F401,
    ChiTietPhuTungKemTheoTSCDService,
)


class KhaiBaoThongTinTaiSanCoDinhService(BaseService):
    """
    Service class for handling KhaiBaoThongTinTaiSanCoDinh (Fixed Asset Information Declaration) model business logic  # noqa: E501
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the service with the repository and child services.
        """
        self.repository = KhaiBaoThongTinTaiSanCoDinhRepository()
        self.chi_tiet_doi_tuong_hach_toan_service = (
            ChiTietDoiTuongHachToanTSCDService()
        )
        self.chi_tiet_phu_tung_kem_theo_service = (
            ChiTietPhuTungKemTheoTSCDService()
        )
        self._khai_bao_thoi_khau_hao_service = None
        super().__init__()

    @property
    def khai_bao_thoi_khau_hao_service(self):
        if self._khai_bao_thoi_khau_hao_service is None:
            from django_ledger.services.tai_san.dung_khau_hao_tscd.khai_bao_thoi_khau_hao_tscd import (
                KhaiBaoThoiKhauHaoTSCDService,
            )
            self._khai_bao_thoi_khau_hao_service = KhaiBaoThoiKhauHaoTSCDService()
        return self._khai_bao_thoi_khau_hao_service

    def get_by_id(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> Optional[KhaiBaoThongTinTaiSanCoDinhModel]:  # noqa: C901
        """
        Retrieves a KhaiBaoThongTinTaiSanCoDinhModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the KhaiBaoThongTinTaiSanCoDinhModel to retrieve.

        Returns
        -------
        Optional[KhaiBaoThongTinTaiSanCoDinhModel]
            The KhaiBaoThongTinTaiSanCoDinhModel with the given UUID, or None if not found.  # noqa: E501
        """
        return self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

    def list(self, entity_slug: str, **kwargs) -> QuerySet:  # noqa: C901
        """
        Lists KhaiBaoThongTinTaiSanCoDinhModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of KhaiBaoThongTinTaiSanCoDinhModel instances.
        """
        return self.repository.list(entity_slug=entity_slug, **kwargs)

    @transaction.atomic
    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> KhaiBaoThongTinTaiSanCoDinhModel:  # noqa: C901
        """
        Creates a new KhaiBaoThongTinTaiSanCoDinhModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new KhaiBaoThongTinTaiSanCoDinhModel.

        Returns
        -------
        KhaiBaoThongTinTaiSanCoDinhModel
            The created KhaiBaoThongTinTaiSanCoDinhModel instance.
        """
        return self.create_with_details(entity_slug=entity_slug, data=data)

    @transaction.atomic
    def update(  # noqa: C901
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[KhaiBaoThongTinTaiSanCoDinhModel]:
        """
        Updates an existing KhaiBaoThongTinTaiSanCoDinhModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the KhaiBaoThongTinTaiSanCoDinhModel to update.
        data : Dict[str, Any]
            The data to update the KhaiBaoThongTinTaiSanCoDinhModel with.

        Returns
        -------
        Optional[KhaiBaoThongTinTaiSanCoDinhModel]
            The updated KhaiBaoThongTinTaiSanCoDinhModel instance, or None if not found.
        """
        return self.update_with_details(entity_slug=entity_slug, uuid=uuid, data=data)

    @transaction.atomic
    def create_with_details(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> KhaiBaoThongTinTaiSanCoDinhModel:
        """
        Creates a new KhaiBaoThongTinTaiSanCoDinhModel instance with its details.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new KhaiBaoThongTinTaiSanCoDinhModel.

        Returns
        -------
        KhaiBaoThongTinTaiSanCoDinhModel
            The created KhaiBaoThongTinTaiSanCoDinhModel instance.
        """
        chi_tiet_doi_tuong_data = data.pop(
            'chi_tiet_doi_tuong_hach_toan_data', []
        )
        chi_tiet_phu_tung_data = data.pop(
            'chi_tiet_phu_tung_kem_theo_data', []
        )

        instance = self.repository.create(entity_slug=entity_slug, data=data)

        if chi_tiet_doi_tuong_data:
            self.chi_tiet_doi_tuong_hach_toan_service.bulk_create(
                parent=instance, data=chi_tiet_doi_tuong_data
            )

        if chi_tiet_phu_tung_data:
            self.chi_tiet_phu_tung_kem_theo_service.bulk_create(
                parent=instance, data=chi_tiet_phu_tung_data
            )

        try:
            khai_bao_thoi_khau_hao_tscd_data = {
                'ma_ts': instance.uuid,
                'ngay_kh1': None,
            }
            self.khai_bao_thoi_khau_hao_service.create(
                entity_slug=entity_slug,
                data=khai_bao_thoi_khau_hao_tscd_data
            )
        except Exception as e:
            raise e

        instance.refresh_from_db()
        return instance

    @transaction.atomic
    def update_with_details(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[KhaiBaoThongTinTaiSanCoDinhModel]:
        """
        Updates an existing KhaiBaoThongTinTaiSanCoDinhModel instance with its details.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the KhaiBaoThongTinTaiSanCoDinhModel to update.
        data : Dict[str, Any]
            The data for updating the KhaiBaoThongTinTaiSanCoDinhModel.

        Returns
        -------
        Optional[KhaiBaoThongTinTaiSanCoDinhModel]
            The updated KhaiBaoThongTinTaiSanCoDinhModel instance, or None if not found.
        """
        chi_tiet_doi_tuong_data = data.pop(
            'chi_tiet_doi_tuong_hach_toan_data', None
        )
        chi_tiet_phu_tung_data = data.pop(
            'chi_tiet_phu_tung_kem_theo_data', None
        )

        instance = self.repository.update(entity_slug=entity_slug, uuid=uuid, data=data)

        if instance:
            if chi_tiet_doi_tuong_data is not None:
                self.chi_tiet_doi_tuong_hach_toan_service.bulk_update(
                    parent=instance, data=chi_tiet_doi_tuong_data
                )

            if chi_tiet_phu_tung_data is not None:
                self.chi_tiet_phu_tung_kem_theo_service.bulk_update(
                    parent=instance, data=chi_tiet_phu_tung_data
                )

        instance.refresh_from_db()
        return instance

    @transaction.atomic
    def delete(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> bool:  # noqa: C901
        """
        Deletes a KhaiBaoThongTinTaiSanCoDinhModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the KhaiBaoThongTinTaiSanCoDinhModel to delete.

        Returns
        -------
        bool
            True if the KhaiBaoThongTinTaiSanCoDinhModel was deleted, False otherwise.
        """
        # Check if the record exists
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not instance:
            raise ValueError(f"Record with UUID {uuid} not found")

        return self.repository.delete(entity_slug=entity_slug, uuid=uuid)
