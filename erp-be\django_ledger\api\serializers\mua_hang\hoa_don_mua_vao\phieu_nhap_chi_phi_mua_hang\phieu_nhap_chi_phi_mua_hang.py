"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the PhieuNhapChiPhiMuaHangSerializer, which handles serialization
for the PhieuNhapChiPhiMuaHangModel.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers._utils.chung_tu_fields.chung_tu_fields import (  # noqa: F401,
    ChungTuSerializerMixin,
)
from django_ledger.api.serializers.han_thanh_toan import (  # noqa: F401,
    HanThanhToanModelSerializer,
)

from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang.chi_phi_chi_tiet_phieu_nhap_chi_phi_mua_hang import (  # noqa: F401
    ChiPhiChiTietPhieuNhapChiPhiMuahangSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang.chi_phi_phieu_nhap_chi_phi_mua_hang import (  # noqa: F401,
    ChiPhiPhieuNhapChiPhiMuaHangSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang.chi_tiet_phieu_nhap_chi_phi_mua_hang import (  # noqa: F401,
    ChiTietPhieuNhapChiPhiMuaHangSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang.thue_phieu_nhap_chi_phi_mua_hang import (  # noqa: F401,
    ThuePhieuNhapChiPhiMuaHangSerializer,
)
from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import (  # noqa: F401,
    PhieuNhapChiPhiMuaHangModel,
)


class PhieuNhapChiPhiMuaHangSerializer(ChungTuSerializerMixin, serializers.ModelSerializer):
    """
    Serializer for PhieuNhapChiPhiMuaHangModel.

    Inherits from ChungTuSerializerMixin to automatically get ChungTu fields:
    - i_so_ct: Document sequence number
    - ma_nk: Document series (QuyenChungTu UUID)
    - so_ct: Document number
    - ngay_ct: Document date
    - ngay_lct: Document creation date
    """

    chi_tiet_phieu_nhaps = ChiTietPhieuNhapChiPhiMuaHangSerializer(
        many=True, required=False
    )
    chi_phi_phieu_nhaps = ChiPhiPhieuNhapChiPhiMuaHangSerializer(
        many=True, required=False
    )
    chi_phi_chi_tiet_phieu_nhaps = ChiPhiChiTietPhieuNhapChiPhiMuahangSerializer(
        many=True, required=False
    )
    thue_phieu_nhaps = ThuePhieuNhapChiPhiMuaHangSerializer(many=True, required=False)
    # Reference data fields
    ma_kh_data = serializers.SerializerMethodField()
    tk_data = serializers.SerializerMethodField()
    ma_tt_data = serializers.SerializerMethodField()
    unit_id_data = serializers.SerializerMethodField()
    ma_nt_data = serializers.SerializerMethodField()
    # ChungTuMixIn provides: ma_nk_data, so_ct_data, chung_tu_data

    class Meta:
        model = PhieuNhapChiPhiMuaHangModel
        fields = "__all__"
        read_only_fields = ["uuid", "created", "updated", "entity_model"]

    def get_ma_kh_data(self, obj):  # noqa: C901
        """
        Returns the customer data for the ma_kh field.
        """
        if obj.ma_kh:
            return {
                'uuid': obj.ma_kh.uuid,
                'customer_code': obj.ma_kh.customer_code,
                'customer_name': obj.ma_kh.customer_name,
            }
        return None

    def get_tk_data(self, obj):  # noqa: C901
        """
        Returns the account data for the tk field.
        """
        if obj.tk:
            return {
                'uuid': obj.tk.uuid,
                'code': obj.tk.code,
                'name': obj.tk.name,
            }
        return None

    def get_ma_tt_data(self, obj):  # noqa: C901
        """
        Returns the payment term data for the ma_tt field.
        """
        if obj.ma_tt:
            return HanThanhToanModelSerializer(obj.ma_tt).data
        return None

    def get_unit_id_data(self, obj):  # noqa: C901
        """
        Returns the unit data for the unit_id field.
        """
        if obj.unit_id:
            return {
                'uuid': obj.unit_id.uuid,
                'slug': obj.unit_id.slug,
                'name': obj.unit_id.name,
            }
        return None

    def get_ma_nt_data(self, obj):  # noqa: C901
        """
        Returns the currency data for the ma_nt field.
        """
        if obj.ma_nt:
            return {
                'uuid': obj.ma_nt.uuid,
                'ma_nt': obj.ma_nt.ma_nt,
                'ten_nt': obj.ma_nt.ten_nt,
            }
        return None

    def get_chi_tiet_phieu_nhaps(self, obj):  # noqa: C901
        """
        Returns the chi_tiet_phieu_nhaps data.
        """
        qs = obj.chi_tiet_phieu_nhaps.filter(deleted__isnull=True)
        return ChiTietPhieuNhapChiPhiMuaHangSerializer(qs, many=True).data

    def get_chi_phi_phieu_nhaps(self, obj):  # noqa: C901
        """
        Returns the chi_phi_phieu_nhaps data.
        """
        qs = obj.chi_phi_phieu_nhaps.filter(deleted__isnull=True)
        return ChiPhiPhieuNhapChiPhiMuaHangSerializer(qs, many=True).data

    def get_chi_phi_chi_tiet_phieu_nhaps(self, obj):  # noqa: C901
        """
        Returns the chi_phi_chi_tiet_phieu_nhaps data.
        """
        qs = obj.chi_phi_chi_tiet_phieu_nhaps.filter(deleted__isnull=True)
        return ChiPhiChiTietPhieuNhapChiPhiMuahangSerializer(qs, many=True).data

    def get_thue_phieu_nhaps(self, obj):  # noqa: C901
        """
        Returns the thue_phieu_nhaps data.
        """
        qs = obj.thue_phieu_nhaps.filter(deleted__isnull=True)
        return ThuePhieuNhapChiPhiMuaHangSerializer(qs, many=True).data


    def to_representation(self, instance):
        """
        Serialize the object, filtering soft-deleted details.
        """
        representation = super().to_representation(instance)
        representation[
            'chi_tiet_phieu_nhaps'
        ] = self.get_chi_tiet_phieu_nhaps(instance)
        representation[
            'chi_phi_phieu_nhaps'
        ] = self.get_chi_phi_phieu_nhaps(instance)
        representation[
            'chi_phi_chi_tiet_phieu_nhaps'
        ] = self.get_chi_phi_chi_tiet_phieu_nhaps(instance)
        representation[
            'thue_phieu_nhaps'
        ] = self.get_thue_phieu_nhaps(instance)
        return representation
