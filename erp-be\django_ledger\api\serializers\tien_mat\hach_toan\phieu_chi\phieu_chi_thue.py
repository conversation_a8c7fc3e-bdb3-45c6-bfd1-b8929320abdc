"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for PhieuChiThue model.
"""

from datetime import datetime

from django.utils.translation import gettext_lazy as _  # noqa: F401
from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers.contract import ContractModelSerializer  # noqa: F401
from django_ledger.api.serializers.customer import (  # noqa: F401,
    CustomerModelSerializer,
)
from django_ledger.api.serializers.danh_muc import (  # noqa: F401,
    ChiPhiKhongHopLeSerializer,
)
from django_ledger.api.serializers.tinh_chat_thue import (  # noqa: F401
    TinhChatThueModelSerializer,
)
from django_ledger.api.serializers.vat_tu import VatTuSerializer  # noqa: F401,
from django_ledger.models import PhieuChiThueModel  # noqa: F401,

from django_ledger.api.serializers.account import AccountModelSerializer  # noqa: F401

from datetime import datetime


class PhieuChiThueSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuChiThue model.
    """

    # Explicit field definition for ngay_ct0
    ngay_ct0 = serializers.DateField(required=False, allow_null=True)

    # Read-only fields for related objects
    phieu_chi_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)
    ma_tc_thue_data = serializers.SerializerMethodField(read_only=True)

    tk_thue_no_data = serializers.SerializerMethodField(read_only=True)
    tk_du_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PhieuChiThueModel
        fields = [
            "uuid",
            "phieu_chi",
            "phieu_chi_data",
            "id",
            "line",
            "so_ct0",
            "so_ct2",
            "ngay_ct0",
            "ma_thue",
            "thue_suat",
            "ma_mau_ct",
            "ma_mau_bc",
            "ma_tc_thue",
            "ma_tc_thue_data",
            "ma_kh",
            "ma_kh_data",
            "ten_kh_thue",
            "dia_chi",
            "ma_so_thue",
            "ten_vt_thue",
            "t_tien_nt",
            "t_tien",
            "tk_thue_no",
            "tk_thue_no_data",
            "ten_tk_thue_no",
            "tk_du",
            "tk_du_data",
            "ten_tk_du",
            "t_thue_nt",
            "t_thue",
            "ma_kh9",
            "ten_kh9",
            "ma_tt",
            "ten_tt",
            "ghi_chu",
            "id_tt",
            "ma_bp",
            "ma_vv",
            "ma_hd",
            "ma_hd_data",
            "ma_dtt",
            "ma_ku",
            "ma_phi",
            "ma_sp",
            "ma_sp_data",
            "ma_lsx",
            "ma_cp0",
            "ma_cp0_data",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "phieu_chi",
            "phieu_chi_data",
            "ma_kh_data",
            "ma_hd_data",
            "ma_sp_data",
            "ma_cp0_data",
            "tk_thue_no_data",
            "tk_du_data",
            "ma_tc_thue_data",
            "created",
            "updated",
        ]
        extra_kwargs = {
            "ngay_ct0": {"required": False, "allow_null": True, "allow_blank": True},
        }

    def get_phieu_chi_data(self, obj):  # noqa: C901
        """
        Get payment voucher data.
        """
        if obj.phieu_chi:
            return {
                "uuid": obj.phieu_chi.uuid,
                "i_so_ct": obj.phieu_chi.i_so_ct,
                "dien_giai": obj.phieu_chi.dien_giai,
                "ngay_ct": obj.phieu_chi.ngay_ct,
            }
        return None

    def get_ma_kh_data(self, obj):  # noqa: C901
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_ma_hd_data(self, obj):  # noqa: C901
        """
        Get contract data.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_sp_data(self, obj):  # noqa: C901
        """
        Get product data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):  # noqa: C901
        """
        Get invalid expense data.
        """
        if obj.ma_cp0:
            return ChiPhiKhongHopLeSerializer(obj.ma_cp0).data
        return None

    def get_tk_thue_no_data(self, obj):  # noqa: C901
        """ """
        if obj.tk_thue_no:
            return AccountModelSerializer(obj.tk_thue_no).data
        return None

    def get_tk_du_data(self, obj):  # noqa: C901
        """ """
        if obj.tk_du:
            return AccountModelSerializer(obj.tk_du).data
        return None

    def get_ma_tc_thue_data(self, obj):  # noqa: C901
        """
        Get tax nature data.
        """
        if obj.ma_tc_thue:
            return TinhChatThueModelSerializer(obj.ma_tc_thue).data
        return None

    def to_internal_value(self, data):
        """Handle empty date fields for PhieuChiThue."""
        # Make a copy to avoid modifying original
        if isinstance(data, dict):
            data = data.copy()

            # Convert empty string to None for ngay_ct0
            if 'ngay_ct0' in data and (
                data['ngay_ct0'] == ""
                or data['ngay_ct0'] is None
                or data['ngay_ct0'] == "null"
            ):
                data['ngay_ct0'] = None

        return super().to_internal_value(data)

    def validate_ngay_ct0(self, value):
        """Validate ngay_ct0 - allow empty values."""
        if not value or value == "" or value is None:
            return None

        if isinstance(value, str):
            try:
                return datetime.strptime(value, '%Y-%m-%d').date()
            except ValueError:
                raise serializers.ValidationError(
                    "Ngày chứng từ gốc phải có định dạng YYYY-MM-DD"
                )
        return value


class PhieuChiThueListSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuChiThue list view.
    """

    # Read-only fields for related objects

    class Meta:
        model = PhieuChiThueModel
        fields = [
            "uuid",
            "phieu_chi",
            "line",
            "ma_thue",
            "thue_suat",
            "ma_kh",
            "ten_kh_thue",
            "t_tien_nt",
            "t_tien",
            "t_thue_nt",
            "t_thue",
            "ma_bp",
            "ma_vv",
            "ghi_chu",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "created",
            "updated",
        ]
