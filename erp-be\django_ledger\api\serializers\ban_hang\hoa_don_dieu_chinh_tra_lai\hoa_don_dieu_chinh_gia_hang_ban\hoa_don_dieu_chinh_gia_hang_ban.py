"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for HoaDonDieuChinhGiaHangBan (Price Adjustment Invoice) model.
"""

from uuid import UUID

from django.utils.translation import gettext_lazy as _  # noqa: F401
from rest_framework import serializers  # noqa: F401,

from django_ledger.api.serializers._utils.chung_tu_fields import ChungTuSerializerMixin
from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dieu_chinh_gia_hang_ban import (  # noqa: F401
    HoaDonDieuChinhGiaHangBanModel,
)


class HoaDonDieuChinhGiaHangBanModelSerializer(
    ChungTuSerializerMixin, serializers.ModelSerializer
):
    """
    Serializer for HoaDonDieuChinhGiaHangBan model.
    """

    ma_kh_data = serializers.SerializerMethodField()

    tk_data = serializers.SerializerMethodField()

    ma_tt_data = serializers.SerializerMethodField()

    unit_id_data = serializers.SerializerMethodField()
    ma_nt_data = serializers.SerializerMethodField()

    ma_kh9_data = serializers.SerializerMethodField()

    ma_dc_data = serializers.SerializerMethodField()

    ma_pttt_data = serializers.SerializerMethodField()

    chi_tiet = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = HoaDonDieuChinhGiaHangBanModel
        fields = [
            # Model fields
            'uuid',
            'deleted',
            'deleted_by_cascade',
            'created',
            'updated',
            'ma_ngv',
            'ma_so_thue',
            'ten_kh_thue',
            'ong_ba',
            'dia_chi',
            'e_mail',
            'dien_giai',
            'id',
            'id_progress',
            'xprogress',
            'xdatetime2',
            'so_ct2',
            'ty_gia',
            'status',
            'transfer_yn',
            'ma_tthddt',
            'ma_pttt',
            'so_ct_hddt',
            'ngay_ct_hddt',
            'so_ct2_hddt',
            'ma_mau_ct_hddt',
            'ten_vt_thue',
            'ma_dc',
            'ly_do_huy',
            'ly_do',
            't_so_luong',
            't_tien_nt2',
            't_tien2',
            't_thue_nt',
            't_thue',
            't_tt_nt',
            't_tt',
            'xfile',
            'created_by',
            'created_at',
            'chung_tu_item',
            'chung_tu',
            'entity_model',
            'ma_kh',
            'tk',
            'ma_tt',
            'unit_id',
            'ma_nt',
            'ma_kh9',
            'ledger',
            'i_so_ct',
            'ma_nk',
            'so_ct',
            'ngay_ct',
            'ngay_lct',
            # SerializerMethodFields
            'ma_kh_data',
            'tk_data',
            'ma_tt_data',
            'unit_id_data',
            'ma_nt_data',
            'ma_kh9_data',
            'ma_dc_data',
            'ma_pttt_data',
            'chi_tiet',
        ]
        read_only_fields = ["uuid", "created", "updated"]

    def get_ma_kh_data(self, obj):  # noqa: C901
        """
        Get ma_kh data.
        """
        if obj.ma_kh:
            return {
                "uuid": obj.ma_kh.uuid,
                "customer_code": obj.ma_kh.customer_code,
                "customer_name": obj.ma_kh.customer_name,
                "customer_number": obj.ma_kh.customer_number,
                "address": obj.ma_kh.address,
                "tax_code": obj.ma_kh.tax_code,
                "contact_person": obj.ma_kh.contact_person,
                "email": obj.ma_kh.email,
            }
        return None

    def get_tk_data(self, obj):  # noqa: C901
        """
        Get tk data.
        """
        if obj.tk:
            return {
                "uuid": obj.tk.uuid,
                "code": obj.tk.code,
                "name": obj.tk.name,
            }
        return None

    def get_ma_tt_data(self, obj):  # noqa: C901
        """
        Get ma_tt data.
        """
        if obj.ma_tt:
            return {
                "uuid": obj.ma_tt.uuid,
                "ma_tt": obj.ma_tt.ma_tt,
                "ten_tt": obj.ma_tt.ten_tt,
            }
        return None

    def get_unit_id_data(self, obj):  # noqa: C901
        """
        Get unit_id data.
        """
        if obj.unit_id:
            return {
                "uuid": obj.unit_id.uuid,
                "name": obj.unit_id.name,
                "slug": obj.unit_id.slug,
            }
        return None

    def get_ma_nt_data(self, obj):  # noqa: C901
        """
        Get ma_nt data.
        """
        if obj.ma_nt:
            return {
                "uuid": obj.ma_nt.uuid,
                "ma_nt": obj.ma_nt.ma_nt,
                "ten_nt": obj.ma_nt.ten_nt,
            }
        return None

    def get_ma_kh9_data(self, obj):  # noqa: C901
        """
        Get ma_kh9 data.
        """
        if obj.ma_kh9:
            return {
                "uuid": obj.ma_kh9.uuid,
                "customer_name": obj.ma_kh9.customer_name,
                "customer_number": obj.ma_kh9.customer_number,
            }
        return None

    def get_ma_dc_data(self, obj):  # noqa: C901
        """
        Get ma_dc data.
        """
        if obj.ma_dc:
            return {
                "uuid": obj.ma_dc.uuid,
                "ma_dc": obj.ma_dc.ma_dc,
                "ten_dc": obj.ma_dc.ten_dc,
            }
        return None

    def get_ma_pttt_data(self, obj):  # noqa: C901
        """
        Get ma_pttt data.
        """
        if obj.ma_pttt:
            return {
                "uuid": obj.ma_pttt.uuid,
                "ma_pttt": obj.ma_pttt.ma_pttt,
                "ten_pttt": obj.ma_pttt.ten_pttt,
            }
        return None

    def get_chi_tiet(self, obj):
        """Get chi tiet hoa don data using ChiTietHoaDonDieuChinhGiaHangBanModelSerializer."""
        try:
            from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dieu_chinh_gia_hang_ban import (
                ChiTietHoaDonDieuChinhGiaHangBanModel,
            )

            from .chi_tiet_hoa_don_dieu_chinh_gia_hang_ban import (
                ChiTietHoaDonDieuChinhGiaHangBanModelSerializer,
            )

            chi_tiet_queryset = ChiTietHoaDonDieuChinhGiaHangBanModel.objects.filter(
                hoa_don__uuid=obj.uuid
            ).order_by('line')
            serializer = ChiTietHoaDonDieuChinhGiaHangBanModelSerializer(
                chi_tiet_queryset, many=True
            )
            return serializer.data
        except Exception as e:
            import logging

            logger = logging.getLogger(__name__)
            logger.error(f"Error in get_chi_tiet for invoice {obj.uuid}: {str(e)}")
            return []


class HoaDonDieuChinhGiaHangBanModelCreateUpdateSerializer(
    ChungTuSerializerMixin, serializers.ModelSerializer
):
    """
    Serializer for creating and updating HoaDonDieuChinhGiaHangBan model.
    """

    # Explicit field definitions for ChungTu fields (properties)

    chi_tiet = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = HoaDonDieuChinhGiaHangBanModel
        fields = "__all__"
        read_only_fields = ["uuid", "created", "updated"]
        extra_kwargs = {
            'entity_model': {'required': False},
        }

    def validate(self, attrs):  # noqa: C901
        """
        Custom validation for HoaDonDieuChinhGiaHangBan data.
        """
        attrs = super().validate(attrs)

        # Validate required fields only for non-partial updates (create operations)
        if not self.partial:
            required_fields = ['ma_kh', 'tk']
            for field in required_fields:
                if field not in attrs or not attrs.get(field):
                    raise serializers.ValidationError(
                        {field: _('This field is required.')}
                    )

        # Validate chi_tiet array if present
        chi_tiet_data = attrs.get('chi_tiet', [])
        if chi_tiet_data:
            for i, item in enumerate(chi_tiet_data):
                # Validate required fields in chi_tiet
                required_chi_tiet_fields = ['ma_vt', 'ma_kho', 'tk_thue_no', 'tk_du']
                for field in required_chi_tiet_fields:
                    if field not in item or not item.get(field):
                        raise serializers.ValidationError(
                            {f'chi_tiet[{i}].{field}': _('This field is required.')}
                        )

                # Validate ma_vi_tri based on ma_kho.vi_tri_yn
                ma_kho_uuid = item.get('ma_kho')
                ma_vi_tri = item.get('ma_vi_tri')

                if ma_kho_uuid:
                    try:
                        from django_ledger.models.kho_hang import KhoHangModel

                        kho_hang = KhoHangModel.objects.get(uuid=ma_kho_uuid)

                        # Check if warehouse requires location management
                        if hasattr(kho_hang, 'vi_tri_yn') and kho_hang.vi_tri_yn:
                            if not ma_vi_tri:
                                raise serializers.ValidationError(
                                    {
                                        f'chi_tiet[{i}].ma_vi_tri': _(
                                            'Mã vị trí là bắt buộc khi kho hàng yêu cầu quản lý vị trí.'
                                        )
                                    }
                                )
                    except KhoHangModel.DoesNotExist:
                        raise serializers.ValidationError(
                            {f'chi_tiet[{i}].ma_kho': _('Kho hàng không tồn tại.')}
                        )
                    except Exception as e:
                        # Log the error and raise a validation error
                        import logging

                        logger = logging.getLogger(__name__)
                        logger.error(f"Error validating ma_vi_tri: {str(e)}")
                        raise serializers.ValidationError(
                            {
                                f'chi_tiet[{i}].ma_vi_tri': _(
                                    'Lỗi khi xác thực mã vị trí: {0}'
                                ).format(str(e))
                            }
                        )

        return attrs
