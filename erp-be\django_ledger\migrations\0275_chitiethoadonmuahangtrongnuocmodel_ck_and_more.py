# Generated by Django 4.2.10 on 2025-09-03 02:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('django_ledger', '0274_chitietbuttoandieuchinhgiamcongnomuahangmodel_tien_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='chitiethoadonmuahangtrongnuocmodel',
            name='ck',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Chiết khấu'),
        ),
        migrations.AddField(
            model_name='chitiethoadonmuahangtrongnuocmodel',
            name='tien_tck',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Tiền trước chiếc khấu'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='chitiethoadonmuahangtrongnuocmodel',
            name='tien_tck_nt',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Tiền trước chiếc khấu ngoại tệ'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='historicalhoadonmuahangtrongnuocmodel',
            name='ck_yn',
            field=models.BooleanField(default=False, verbose_name='Chiếc khấu Y/N'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='hoadonmuahangtrongnuocmodel',
            name='ck_yn',
            field=models.BooleanField(default=False, verbose_name='Chiếc khấu Y/N'),
            preserve_default=False,
        ),
    ]
