"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Chi Tiet Hoa Don Ban Hang (Sales Invoice Detail) view implementation.
"""

from django.shortcuts import get_object_or_404  # noqa: F401
from rest_framework import status, viewsets  # noqa: F401
from rest_framework.permissions import IsAuthenticated  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.serializers.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import (  # noqa: F401,
    ChiTietHoaDonBanHangCreateUpdateSerializer,
    ChiTietHoaDonBanHangSerializer,
)
from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import (  # noqa: F401,
    ChiTietHoaDonBanHangModel,
)
from django_ledger.models.entity import EntityModel  # noqa: F401,
from django_ledger.services.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import (  # noqa: F401,
    ChiTietHoaDonBanHangService,
)


class ChiTietHoaDonBanHangViewSet(viewsets.ModelViewSet):
    """
    ViewSet for ChiTietHoaDonBanHangModel.
    """

    queryset = ChiTietHoaDonBanHangModel.objects.all()
    serializer_class = ChiTietHoaDonBanHangSerializer  # noqa: F811
    permission_classes = [IsAuthenticated]
    service = ChiTietHoaDonBanHangService()
    lookup_field = 'uuid'

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for ChiTietHoaDonBanHangModel.
        Filters by entity_slug and hoa_don_id if provided in the URL.
        """
        hoa_don_id = self.kwargs.get('hoa_don_id')
        if hoa_don_id:
            return self.service.get_by_hoa_don(hoa_don_id=hoa_don_id)
        return self.queryset

    def get_serializer_context(self):  # noqa: C901
        """
        Add additional context to the serializer.
        """
        context = super().get_serializer_context()
        entity_slug = self.kwargs.get('entity_slug')
        if entity_slug:
            entity_model = get_object_or_404(EntityModel, slug=entity_slug)
            context['entity_model'] = entity_model
        return context

    def get_serializer_class(self):  # noqa: C901
        """
        Use create/update serializer for write operations, read serializer for read operations.
        """
        if self.action in ['create', 'update', 'partial_update']:
            return ChiTietHoaDonBanHangCreateUpdateSerializer
        return ChiTietHoaDonBanHangSerializer

    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        List ChiTietHoaDonBanHangModel instances.
        """
        queryset = self.get_queryset()
        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def retrieve(self, request, *args, **kwargs):  # noqa: C901
        """
        Retrieve a ChiTietHoaDonBanHangModel instance.
        """
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new ChiTietHoaDonBanHangModel instance.
        """
        hoa_don_id = self.kwargs.get('hoa_don_id')
        # With the nested URL structure, hoa_don_id should always be present
        if not hoa_don_id:
            return Response(
                {"error": "hoa_don_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get the HoaDonBanHangModel instance
        hoa_don = self.service.model_class._meta.get_field(
            'hoa_don_ban_hang'
        ).related_model.objects.get(uuid=hoa_don_id)
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        # Create the ChiTietHoaDonBanHangModel instance
        chi_tiet = self.service.create(
            hoa_don_ban_hang=hoa_don, **serializer.validated_data
        )

        result_serializer = self.get_serializer(chi_tiet)
        headers = self.get_success_headers(result_serializer.data)
        return Response(
            result_serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers,
        )

    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update an existing ChiTietHoaDonBanHangModel instance.
        """
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        # Update the ChiTietHoaDonBanHangModel instance
        chi_tiet = self.service.update(instance.uuid, **serializer.validated_data)
        result_serializer = self.get_serializer(chi_tiet)
        return Response(result_serializer.data)

    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a ChiTietHoaDonBanHangModel instance.
        """
        instance = self.get_object()
        self.service.delete(instance.uuid)
        return Response(status=status.HTTP_204_NO_CONTENT)

    def retrieve_direct(self, request, *args, **kwargs):  # noqa: C901
        """
        Retrieve a ChiTietHoaDonBanHangModel instance directly by uuid.
        This method allows fetching chi tiet without requiring hoa_don_uuid.
        """
        uuid = kwargs.get('uuid')
        instance = self.service.get_by_id(uuid)
        if instance is None:
            return Response(
                {"error": "Chi tiet hoa don ban hang not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def update_direct(self, request, *args, **kwargs):  # noqa: C901
        """
        Update a ChiTietHoaDonBanHangModel instance directly by uuid.
        """
        uuid = kwargs.get('uuid')
        instance = self.service.get_by_id(uuid)
        if instance is None:
            return Response(
                {"error": "Chi tiet hoa don ban hang not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        serializer = self.get_serializer(instance, data=request.data, partial=False)
        serializer.is_valid(raise_exception=True)
        chi_tiet = self.service.update(uuid=uuid, data=serializer.validated_data)
        result_serializer = self.get_serializer(chi_tiet)
        return Response(result_serializer.data)

    def partial_update_direct(self, request, *args, **kwargs):  # noqa: C901
        """
        Partially update a ChiTietHoaDonBanHangModel instance directly by uuid.
        """
        uuid = kwargs.get('uuid')
        instance = self.service.get_by_id(uuid)
        if instance is None:
            return Response(
                {"error": "Chi tiet hoa don ban hang not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        chi_tiet = self.service.update(uuid=uuid, data=serializer.validated_data)
        result_serializer = self.get_serializer(chi_tiet)
        return Response(result_serializer.data)

    def destroy_direct(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a ChiTietHoaDonBanHangModel instance directly by uuid.
        """
        uuid = kwargs.get('uuid')
        instance = self.service.get_by_id(uuid)
        if instance is None:
            return Response(
                {"error": "Chi tiet hoa don ban hang not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        self.service.delete(uuid)
        return Response(status=status.HTTP_204_NO_CONTENT)
