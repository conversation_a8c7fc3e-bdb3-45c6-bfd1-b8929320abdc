"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for HoaDonDieuChinhGiaHangBan (Price Adjustment Invoice) model.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import transaction  # noqa: F401,
from django.db.models import QuerySet  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dieu_chinh_gia_hang_ban import (  # noqa: F401,
    HoaDonDieuChinhGiaHangBanModel,
)
from django_ledger.repositories.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dieu_chinh_gia_hang_ban import (  # noqa: F401,
    HoaDonDieuChinhGiaHangBanRepository,
)
from django_ledger.services.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dieu_chinh_gia_hang_ban.chi_tiet_hoa_don_dieu_chinh_gia_hang_ban import (  # noqa: F401,
    ChiTietHoaDonDieuChinhGiaHangBanService,
)
from django_ledger.services.base import BaseService  # noqa: F401,
from django_ledger.utils_new.debt_management.cong_no_creation import (  # noqa: F401,
    CongNoCreation,
)


class HoaDonDieuChinhGiaHangBanService(BaseService):
    """
    Service class for HoaDonDieuChinhGiaHangBanModel.
    Provides business logic for price adjustment invoices.

    ✅ ENHANCED: Integrated with Unified Accounting Service
    ERP Expert - 20 năm kinh nghiệm
    """

    # ✅ PREDEFINED CONFIGURATION: Price adjustment accounting mappings
    # Multiple journal types based on ma_ngv logic with enhanced conditional logic
    PRICE_ADJUSTMENT_ACCOUNTING_CONFIG = [
        # ma_ngv = 1,5: GIẢM (Decrease)
        {
            'journal_type': 'CKGIAM',  # Chiết khấu giảm
            'debit_account_field': 'tk',  # Header account - DEBIT
            'credit_account_field': 'tk_ck',  # Detail discount account - CREDIT
            'debit_account_source': 'header',  # Lấy debit từ header
            'credit_account_source': 'detail',  # Lấy credit từ detail
            'amount_field': 'ck_nt',  # Discount amount
            'detail_source': 'chi_tiet',  # Related name to detail
            'detail_conditions': {  # ✅ ENHANCED: Conditional logic
                'ck_nt': {'gt': 0},  # ck > 0
                'tk_ck': {'is_not_null': True},  # tk_ck IS NOT NULL
            },
            'canCreate': True,  # Default: conditional creation
        },
        {
            'journal_type': 'DTGIAM',  # Doanh thu giảm
            'debit_account_field': 'tk_du',  # Detail balance account - DEBIT
            'credit_account_field': 'tk',  # Header account - CREDIT
            'debit_account_source': 'detail',  # Lấy debit từ detail
            'credit_account_source': 'header',  # Lấy credit từ header
            'amount_field': 'tien_nt2',  # Revenue amount
            'detail_source': 'chi_tiet',  # Related name to detail
            'detail_conditions': {  # ✅ ENHANCED: Conditional logic
                'tien_nt2': {'gt': 0},  # tien2 > 0
                'tk_du': {'is_not_null': True},  # tk_du IS NOT NULL
            },
            'canCreate': True,  # Default: conditional creation
        },
        {
            'journal_type': 'THUEGIAM',  # Thuế giảm
            'debit_account_field': 'tk_thue_no',  # Detail tax debit account - DEBIT
            'credit_account_field': 'tk',  # Header account - CREDIT
            'debit_account_source': 'detail',  # Lấy debit từ detail
            'credit_account_source': 'header',  # Lấy credit từ header
            'amount_field': 'thue_nt',  # Tax amount
            'detail_source': 'chi_tiet',  # Related name to detail
            'detail_conditions': {  # ✅ ENHANCED: Conditional logic
                'thue_nt': {'gt': 0},  # thue > 0
                'tk_thue_no': {'is_not_null': True},  # tk_thue_no IS NOT NULL
            },
            'canCreate': True,  # Default: conditional creation
        },
        # ma_ngv = 2: TĂNG (Increase)
        {
            'journal_type': 'CKTANG',  # Chiết khấu tăng
            'debit_account_field': 'tk_ck',  # Detail discount account - DEBIT
            'credit_account_field': 'tk',  # Header account - CREDIT
            'debit_account_source': 'detail',  # Lấy debit từ detail
            'credit_account_source': 'header',  # Lấy credit từ header
            'amount_field': 'ck_nt',  # Discount amount
            'detail_source': 'chi_tiet',  # Related name to detail
            'detail_conditions': {  # ✅ ENHANCED: Conditional logic
                'ck_nt': {'gt': 0},  # ck > 0
                'tk_ck': {'is_not_null': True},  # tk_ck IS NOT NULL
            },
            'canCreate': True,  # Default: conditional creation
        },
        {
            'journal_type': 'DTTANG',  # Doanh thu tăng
            'debit_account_field': 'tk',  # Header account - DEBIT
            'credit_account_field': 'tk_du',  # Detail balance account - CREDIT
            'debit_account_source': 'header',  # Lấy debit từ header
            'credit_account_source': 'detail',  # Lấy credit từ detail
            'amount_field': 'tien_nt2',  # Revenue amount
            'detail_source': 'chi_tiet',  # Related name to detail
            'detail_conditions': {  # ✅ ENHANCED: Conditional logic
                'tien_nt2': {'gt': 0},  # tien2 > 0
                'tk_du': {'is_not_null': True},  # tk_du IS NOT NULL
            },
            'canCreate': True,  # Default: conditional creation
        },
        {
            'journal_type': 'THUETANG',  # Thuế tăng
            'debit_account_field': 'tk',  # Header account - DEBIT
            'credit_account_field': 'tk_thue_no',  # Detail tax debit account - CREDIT
            'debit_account_source': 'header',  # Lấy debit từ header
            'credit_account_source': 'detail',  # Lấy credit từ detail
            'amount_field': 'thue_nt',  # Tax amount
            'detail_source': 'chi_tiet',  # Related name to detail
            'detail_conditions': {  # ✅ ENHANCED: Conditional logic
                'thue_nt': {'gt': 0},  # thue > 0
                'tk_thue_no': {'is_not_null': True},  # tk_thue_no IS NOT NULL
            },
            'canCreate': True,  # Default: conditional creation
        },
        # ma_ngv = 3: THUẾ GTGT GIẢM
        {
            'journal_type': 'THUEGTGTGIAM',  # Thuế GTGT giảm
            'debit_account_field': 'tk_thue_no',  # Detail tax debit account - DEBIT
            'credit_account_field': 'tk',  # Header account - CREDIT
            'debit_account_source': 'detail',  # Lấy debit từ detail
            'credit_account_source': 'header',  # Lấy credit từ header
            'amount_field': 'thue_nt',  # Tax amount
            'detail_source': 'chi_tiet',  # Related name to detail
            'detail_conditions': {  # ✅ ENHANCED: Conditional logic
                'thue_nt': {'gt': 0},  # thue > 0
                'tk_thue_no': {'is_not_null': True},  # tk_thue_no IS NOT NULL
            },
            'canCreate': True,  # Default: conditional creation
        },
        # ma_ngv = 4: THUẾ GTGT TĂNG
        {
            'journal_type': 'THUEGTGTTANG',  # Thuế GTGT tăng
            'debit_account_field': 'tk',  # Header account - DEBIT
            'credit_account_field': 'tk_thue_no',  # Detail tax debit account - CREDIT
            'debit_account_source': 'header',  # Lấy debit từ header
            'credit_account_source': 'detail',  # Lấy credit từ detail
            'amount_field': 'thue_nt',  # Tax amount
            'detail_source': 'chi_tiet',  # Related name to detail
            'detail_conditions': {  # ✅ ENHANCED: Conditional logic
                'thue_nt': {'gt': 0},  # thue > 0
                'tk_thue_no': {'is_not_null': True},  # tk_thue_no IS NOT NULL
            },
            'canCreate': True,  # Default: conditional creation
        },
    ]

    def __init__(self):  # noqa: C901
        """
        Initialize the service with the HoaDonDieuChinhGiaHangBanRepository and related services.  # noqa: E501
        """
        super().__init__()
        self.repository = HoaDonDieuChinhGiaHangBanRepository()
        self.chi_tiet_service = ChiTietHoaDonDieuChinhGiaHangBanService()

        # ✅ ĐƠN GIẢN: Khởi tạo unified accounting service
        self._cong_no_service = CongNoCreation()

    def _determine_accounting_mappings(
        self, hoa_don: HoaDonDieuChinhGiaHangBanModel
    ) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định accounting mappings dựa trên ma_ngv và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Conditional journal types dựa trên ma_ngv (1,5=GIAM, 2=TANG, 3=THUEGTGTGIAM, 4=THUEGTGTTANG)
        - Status-based creation (chỉ tạo khi status IN ('3', '5'))
        - Detail-level conditional creation (amount > 0 AND account IS NOT NULL)
        - Support flexible business rules cho future enhancements

        Parameters
        ----------
        hoa_don : HoaDonDieuChinhGiaHangBanModel
            Hóa đơn điều chỉnh giá để analyze

        Returns
        -------
        List[Dict[str, Any]]
            Danh sách accounting mappings với journal_type và canCreate được set theo business logic
        """
        mappings = []

        # ✅ BUSINESS LOGIC: Chỉ tạo bút toán khi status IN ('3', '5')
        if hoa_don.status not in ['3', '5']:
            # Return empty mappings - không tạo bút toán
            return mappings

        # ✅ BUSINESS LOGIC: Determine journal types based on ma_ngv
        ma_ngv = getattr(hoa_don, 'ma_ngv', '1')  # Default to '1'

        if ma_ngv in ['1', '5']:
            # GIẢM: CKGIAM, DTGIAM, THUEGIAM
            journal_types = ['CKGIAM', 'DTGIAM', 'THUEGIAM']
        elif ma_ngv == '2':
            # TĂNG: CKTANG, DTTANG, THUETANG
            journal_types = ['CKTANG', 'DTTANG', 'THUETANG']
        elif ma_ngv == '3':
            # THUẾ GTGT GIẢM
            journal_types = ['THUEGTGTGIAM']
        elif ma_ngv == '4':
            # THUẾ GTGT TĂNG
            journal_types = ['THUEGTGTTANG']
        else:
            # Default fallback to GIẢM
            journal_types = ['CKGIAM', 'DTGIAM', 'THUEGIAM']

        # ✅ BUSINESS LOGIC: Filter mappings by journal types
        for config in self.PRICE_ADJUSTMENT_ACCOUNTING_CONFIG:
            if config['journal_type'] in journal_types:
                mapping = config.copy()

                # ✅ CONDITIONAL CREATION: Will be validated at detail level
                # CongNoCreation will check amount > 0 AND account IS NOT NULL for each detail
                mapping['canCreate'] = True

                mappings.append(mapping)

        return mappings

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the base queryset for the model.

        Returns
        -------
        QuerySet
            Base queryset for the model.
        """
        return self.repository.get_queryset()

    def get_all(self, entity_slug: str) -> QuerySet:  # noqa: C901
        """
        Get all HoaDonDieuChinhGiaHangBan instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The slug of the entity to filter by.

        Returns
        -------
        QuerySet
            A queryset of HoaDonDieuChinhGiaHangBan instances for the specified entity.
        """
        return self.repository.get_by_entity_slug(entity_slug)

    def get_by_id(
        self, uuid: Union[str, UUID]
    ) -> HoaDonDieuChinhGiaHangBanModel:  # noqa: C901
        """
        Get a HoaDonDieuChinhGiaHangBan by its UUID.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the HoaDonDieuChinhGiaHangBan to retrieve.

        Returns
        -------
        HoaDonDieuChinhGiaHangBanModel
            The HoaDonDieuChinhGiaHangBan with the specified UUID.
        """
        return self.repository.get_by_id(uuid)

    @transaction.atomic
    def create(  # noqa: C901
        self,
        entity_slug: str,
        data: Dict[str, Any],
        chi_tiet_list: Optional[List[Dict[str, Any]]] = None,
    ) -> HoaDonDieuChinhGiaHangBanModel:
        """
        ✅ ENHANCED: Create a new HoaDonDieuChinhGiaHangBan instance with automatic accounting.

        Tự động tạo bút toán kế toán cho hóa đơn điều chỉnh giá sử dụng unified services.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Atomic transaction cho cả accounting và document creation
        - Status-based business logic cho entry creation (3,5)
        - ma_ngv conditional logic cho journal types
        - Complete audit trail cho financial impacts
        - Transaction safety với automatic rollback

        Parameters
        ----------
        data : Dict[str, Any]
            The data to create the HoaDonDieuChinhGiaHangBan with.
        chi_tiet_list : Optional[List[Dict[str, Any]]]
            A list of data dictionaries to create ChiTietHoaDonDieuChinhGiaHangBan instances with.  # noqa: E501

        Returns
        -------
        HoaDonDieuChinhGiaHangBanModel
            The created HoaDonDieuChinhGiaHangBan instance.
        """
        # Create the HoaDonDieuChinhGiaHangBan
        # ChungTuMixIn will handle so_ct logic automatically in save() method
        hoa_don = self.repository.create(entity_slug, data)
        # Create the ChiTietHoaDonDieuChinhGiaHangBan if provided
        if chi_tiet_list:
            try:
                # Create the chi_tiet instances
                self.chi_tiet_service.bulk_create(hoa_don, chi_tiet_list)

                # Update the totals on the hoa_don
                self._update_totals(hoa_don)
            except Exception as e:
                # ⚠️ CRITICAL: Chi tiet creation failure should fail the entire transaction
                # to maintain data consistency between HoaDon and chi_tiet
                raise Exception(
                    f"Failed to create chi_tiet for HoaDonDieuChinhGia {hoa_don.so_ct}: {str(e)}"
                ) from e

        # ✅ UNIFIED ACCOUNTING: Tạo bút toán kế toán
        # Only create accounting if ledger is not already assigned to avoid UNIQUE constraint
        if not hoa_don.ledger:
            try:
                self._cong_no_service.create_document_accounting_entries(
                    source_document=hoa_don,
                    document_type="hóa đơn điều chỉnh giá hàng bán",
                    account_mappings=self._determine_accounting_mappings(hoa_don),
                )
            except Exception as e:
                # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                # to maintain data consistency between HoaDon and accounting entries
                raise Exception(
                    f"Failed to create accounting entry for HoaDonDieuChinhGia {hoa_don.so_ct}: {str(e)}"
                ) from e
        # Note: If ledger already exists, skip accounting creation to avoid duplicates

        return hoa_don

    @transaction.atomic
    def create_with_entity(  # noqa: C901
        self,
        entity_slug: str,
        data: Dict[str, Any],
        chi_tiet_list: Optional[List[Dict[str, Any]]] = None,
    ) -> HoaDonDieuChinhGiaHangBanModel:
        """
        ✅ ENHANCED: Create a new HoaDonDieuChinhGiaHangBan instance with entity and automatic accounting.

        Tự động tạo bút toán kế toán cho hóa đơn điều chỉnh giá sử dụng unified services.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data to create the HoaDonDieuChinhGiaHangBan with.
        chi_tiet_list : Optional[List[Dict[str, Any]]]
            A list of data dictionaries to create ChiTietHoaDonDieuChinhGiaHangBan instances with.  # noqa: E501

        Returns
        -------
        HoaDonDieuChinhGiaHangBanModel
            The created HoaDonDieuChinhGiaHangBan instance.
        """
        # Create the HoaDonDieuChinhGiaHangBan
        hoa_don = self.repository.create(entity_slug, data)

        # Create the ChiTietHoaDonDieuChinhGiaHangBan if provided
        if chi_tiet_list:
            try:
                # Create the chi_tiet instances
                self.chi_tiet_service.bulk_create(hoa_don, chi_tiet_list)

                # Update the totals on the hoa_don
                self._update_totals(hoa_don)
            except Exception as e:
                # ⚠️ CRITICAL: Chi tiet creation failure should fail the entire transaction
                # to maintain data consistency between HoaDon and chi_tiet
                raise Exception(
                    f"Failed to create chi_tiet for HoaDonDieuChinhGia {hoa_don.so_ct}: {str(e)}"
                ) from e

        # ✅ UNIFIED ACCOUNTING: Tạo bút toán kế toán
        # Only create accounting if ledger is not already assigned to avoid UNIQUE constraint
        if not hoa_don.ledger:
            try:
                self._cong_no_service.create_document_accounting_entries(
                    source_document=hoa_don,
                    document_type="hóa đơn điều chỉnh giá hàng bán",
                    account_mappings=self._determine_accounting_mappings(hoa_don),
                )
            except Exception as e:
                # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                # to maintain data consistency between HoaDon and accounting entries
                raise Exception(
                    f"Failed to create accounting entry for HoaDonDieuChinhGia {hoa_don.so_ct}: {str(e)}"
                ) from e
        # Note: If ledger already exists, skip accounting creation to avoid duplicates

        return hoa_don

    @transaction.atomic
    def update(
        self, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> HoaDonDieuChinhGiaHangBanModel:  # noqa: C901
        """
        ✅ ENHANCED: Update an existing HoaDonDieuChinhGiaHangBan instance with automatic accounting update.

        Tự động cập nhật bút toán kế toán cho hóa đơn điều chỉnh giá sử dụng unified services.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Atomic transaction cho cả accounting và document update
        - Status-based business logic cho entry update (3,5)
        - ma_ngv conditional logic cho journal types
        - Complete audit trail cho financial impacts
        - Transaction safety với automatic rollback

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the HoaDonDieuChinhGiaHangBan to update.
        data : Dict[str, Any]
            The data to update the HoaDonDieuChinhGiaHangBan with.

        Returns
        -------
        HoaDonDieuChinhGiaHangBanModel
            The updated HoaDonDieuChinhGiaHangBan instance.
        """
        # Update the instance using repository
        instance = self.repository.update(uuid, data)
        return instance

    @transaction.atomic
    def delete(self, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Delete a HoaDonDieuChinhGiaHangBan instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the HoaDonDieuChinhGiaHangBan to delete.

        Returns
        -------
        bool
            True if the HoaDonDieuChinhGiaHangBan was deleted, False otherwise.
        """
        instance = self.get_by_id(uuid=uuid)
        if not instance:
            return

        # If a ledger exists, delete its entries first.
        if instance.ledger:
            try:
                self._cong_no_service.delete_document_accounting_entries(
                    source_document=instance
                )
            except Exception as e:
                raise Exception(
                    f"Failed to delete accounting entries for {instance.so_ct}: {str(e)}"
                ) from e

        # The ledger's deletion will cascade and delete the main document.
        # If no ledger, delete the main document directly.
        if not instance.ledger:
            self.repository.delete(uuid=uuid)

    def search(self, entity_slug: str, query: str) -> QuerySet:  # noqa: C901
        """
        Search for HoaDonDieuChinhGiaHangBan instances by various fields.

        Parameters
        ----------
        entity_slug : str
            The slug of the entity to filter by.
        query : str
            The search query.

        Returns
        -------
        QuerySet
            A queryset of HoaDonDieuChinhGiaHangBan instances matching the search query.
        """
        return self.repository.search(entity_slug=entity_slug, query=query)

    def get_accounting_configuration(self) -> List[Dict[str, Any]]:
        """
        Lấy cấu hình kế toán cho hóa đơn điều chỉnh giá.

        Returns:
            List[Dict[str, Any]]: Danh sách mapping configuration
        """
        return self.PRICE_ADJUSTMENT_ACCOUNTING_CONFIG.copy()

    def _update_totals(
        self, hoa_don: HoaDonDieuChinhGiaHangBanModel
    ) -> HoaDonDieuChinhGiaHangBanModel:  # noqa: C901
        """
        Update the totals on a HoaDonDieuChinhGiaHangBan instance based on its details.

        Parameters
        ----------
        hoa_don : HoaDonDieuChinhGiaHangBanModel
            The HoaDonDieuChinhGiaHangBan instance to update.

        Returns
        -------
        HoaDonDieuChinhGiaHangBanModel
            The updated HoaDonDieuChinhGiaHangBan instance.
        """
        # Get all chi_tiet for this hoa_don
        chi_tiet_list = hoa_don.chi_tiet.all()
        # Calculate totals
        t_so_luong = sum(chi_tiet.so_luong for chi_tiet in chi_tiet_list)
        t_tien_nt2 = sum(chi_tiet.tien_nt2 for chi_tiet in chi_tiet_list)
        t_tien2 = sum(chi_tiet.tien2 for chi_tiet in chi_tiet_list)
        t_thue_nt = sum(chi_tiet.thue_nt for chi_tiet in chi_tiet_list)
        t_thue = sum(chi_tiet.thue for chi_tiet in chi_tiet_list)
        # Update the hoa_don
        hoa_don.t_so_luong = t_so_luong
        hoa_don.t_tien_nt2 = t_tien_nt2
        hoa_don.t_tien2 = t_tien2
        hoa_don.t_thue_nt = t_thue_nt
        hoa_don.t_thue = t_thue
        hoa_don.t_tt_nt = t_tien_nt2 + t_thue_nt
        hoa_don.t_tt = t_tien2 + t_thue
        # Save the changes
        hoa_don.save()

        return hoa_don

    @transaction.atomic
    def bulk_create(
        self, data_list: List[Dict[str, Any]]
    ) -> List[HoaDonDieuChinhGiaHangBanModel]:  # noqa: C901
        """
        Create multiple HoaDonDieuChinhGiaHangBan instances.

        Parameters
        ----------
        data_list : List[Dict[str, Any]]
            A list of data dictionaries to create HoaDonDieuChinhGiaHangBan instances with.  # noqa: E501

        Returns
        -------
        List[HoaDonDieuChinhGiaHangBanModel]
            A list of created HoaDonDieuChinhGiaHangBan instances.
        """
        created_instances = []
        for data in data_list:
            instance = self.repository.create(data)
            created_instances.append(instance)

        return created_instances

    @transaction.atomic
    def update_with_details(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> HoaDonDieuChinhGiaHangBanModel:  # noqa: C901
        """
        Update HoaDonDieuChinhGiaHangBan with chi_tiet details using bulk update pattern.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : Union[str, UUID]
            The UUID of the instance to update
        data : Dict[str, Any]
            The data for updating including chi_tiet

        Returns
        -------
        HoaDonDieuChinhGiaHangBanModel
            The updated HoaDonDieuChinhGiaHangBan instance

        Raises
        ------
        Exception
            If there's an error during the update process.
        """
        # Extract chi_tiet data
        chi_tiet_data = data.pop('chi_tiet', [])

        # Update the main invoice
        hoa_don = self.update(uuid, data)

        # Update invoice details if provided
        if chi_tiet_data:
            self.chi_tiet_service.bulk_update(parent=hoa_don, data_list=chi_tiet_data)

        # Refresh instance to get the latest details for accounting
        hoa_don = self.repository.get_by_id(uuid=hoa_don.uuid)

        # ✅ UNIFIED ACCOUNTING: Create or update accounting entries
        try:
            account_mappings = self._determine_accounting_mappings(hoa_don)
            if hoa_don.ledger:
                # UPDATE existing entries
                self._cong_no_service.update_document_accounting_entries(
                    source_document=hoa_don,
                    document_type="hóa đơn điều chỉnh giá hàng bán",
                    account_mappings=account_mappings,
                )
            else:
                # CREATE new entries if no ledger exists
                self._cong_no_service.create_document_accounting_entries(
                    source_document=hoa_don,
                    document_type="hóa đơn điều chỉnh giá hàng bán",
                    account_mappings=account_mappings,
                )
        except Exception as e:
            raise Exception(
                f"Failed to create/update accounting entry for HoaDonDieuChinhGia {hoa_don.so_ct}: {str(e)}"
            ) from e

        return hoa_don
