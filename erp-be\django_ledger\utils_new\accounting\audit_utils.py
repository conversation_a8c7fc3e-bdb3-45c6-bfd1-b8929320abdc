"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Audit Utilities for Financial Reports.
Expert ERP Implementation - 20+ years experience.

Provides high-performance utility functions to query AccountBalanceAuditModel
for financial reporting, ensuring accurate aggregation of balances from multiple
customers, vendors, or other related entities.
"""

import logging
from datetime import datetime, date, timedelta
from decimal import Decimal
from typing import List, Optional

from django.db.models import Sum, Subquery, OuterRef, Q, F, Exists

from django_ledger.models.accounts import AccountModel
from django_ledger.models.account_balance_audit import AccountBalanceAuditModel
from django_ledger.models.entity import EntityModel
from django_ledger.models.transactions.transactions import TransactionModel

logger = logging.getLogger(__name__)


def get_total_account_balance_on_date(
    entity_uuid: str,
    account_codes: List[str],
    on_date: date,
    customer_uuids: Optional[List[str]] = None,
    vendor_uuids: Optional[List[str]] = None
) -> Decimal:
    """
    Calculates the total balance for a list of account codes on a specific date.

    This function correctly aggregates balances from all underlying entities
    (customers, vendors) by finding the latest audit record for each one
    on or before the specified date and summing their balances.

    Args:
        entity_uuid (str): The UUID of the entity.
        account_codes (List[str]): A list of account codes to query.
        on_date (date): The specific date to get the balance for.
        customer_uuids (Optional[List[str]]): Optional list of customer UUIDs to filter by.
        vendor_uuids (Optional[List[str]]): Optional list of vendor UUIDs to filter by.

    Returns:
        Decimal: The total aggregated balance.
    """
    if not account_codes:
        return Decimal('0')

    try:
        # Build a Q object to filter for codes that start with any of the provided codes
        q_objects = Q()
        for code in account_codes:
            q_objects |= Q(code__startswith=code)

        # Get all relevant account UUIDs, including children
        account_qs = AccountModel.objects.filter(
            q_objects,
            coa_model__entity__uuid=entity_uuid
        )
        account_uuids = list(account_qs.values_list('uuid', flat=True))

        if not account_uuids:
            return Decimal('0')

        # Base queryset for audit records
        audit_qs = AccountBalanceAuditModel.objects.filter(
            entity_model__uuid=entity_uuid,
            account__uuid__in=account_uuids,
            timestamp__date__lte=on_date
        )

        # Apply optional customer/vendor filters
        if customer_uuids:
            audit_qs = audit_qs.filter(customer__uuid__in=customer_uuids)
        if vendor_uuids:
            audit_qs = audit_qs.filter(vendor__uuid__in=vendor_uuids)

        # Subquery to find the ID of the latest record for each account/customer/vendor combination.
        # This is the core of the aggregation logic.
        latest_audits_subquery = audit_qs.filter(
            account_id=OuterRef('account_id'),
            customer_id=OuterRef('customer_id'),
            vendor_id=OuterRef('vendor_id')
        ).order_by('-timestamp').values('id')[:1]

        # Filter the main queryset to only include these latest records
        latest_balances = audit_qs.filter(id__in=Subquery(latest_audits_subquery))

        # Aggregate the sum of 'balance_after' from these latest records
        total_balance = latest_balances.aggregate(
            total=Sum('balance_after')
        )['total'] or Decimal('0')

        return total_balance

    except Exception as e:
        logger.error(f"Error getting total balance for accounts {account_codes} on {on_date}: {e}", exc_info=True)
        return Decimal('0')


def get_account_balances_on_date_in_bulk(
    entity_uuid: str,
    account_codes: List[str],
    on_date: date,
    customer_uuids: Optional[List[str]] = None,
    vendor_uuids: Optional[List[str]] = None
) -> dict[str, Decimal]:
    """
    Calculates the balances for a list of account codes on a specific date in a single, efficient query.

    This function is optimized to avoid the N+1 problem by fetching all balances at once.
    It correctly aggregates balances from all underlying entities (customers, vendors).

    Args:
        entity_uuid (str): The UUID of the entity.
        account_codes (List[str]): A list of account codes to query.
        on_date (date): The specific date to get the balance for.
        customer_uuids (Optional[List[str]]): Optional list of customer UUIDs to filter by.
        vendor_uuids (Optional[List[str]]): Optional list of vendor UUIDs to filter by.

    Returns:
        Dict[str, Decimal]: A dictionary mapping each account code to its total aggregated balance.
    """
    if not account_codes:
        return {}

    try:
        q_objects = Q()
        for code in account_codes:
            q_objects |= Q(code__startswith=code)

        account_qs = AccountModel.objects.filter(
            q_objects,
            coa_model__entity__uuid=entity_uuid
        )
        # Create a map of UUID -> Code for later lookup
        account_uuid_to_code_map = {str(acc.uuid): acc.code for acc in account_qs}
        account_uuids = list(account_uuid_to_code_map.keys())

        if not account_uuids:
            return {}

        audit_qs = AccountBalanceAuditModel.objects.filter(
            entity_model__uuid=entity_uuid,
            account__uuid__in=account_uuids,
            timestamp__date__lte=on_date
        )

        if customer_uuids:
            audit_qs = audit_qs.filter(customer__uuid__in=customer_uuids)
        if vendor_uuids:
            audit_qs = audit_qs.filter(vendor__uuid__in=vendor_uuids)

        # Subquery to find the ID of the latest record for each group.
        latest_audits_subquery = audit_qs.filter(
            account_id=OuterRef('account_id'),
            customer_id=OuterRef('customer_id'),
            vendor_id=OuterRef('vendor_id')
        ).order_by('-timestamp').values('uuid')[:1]

        # Filter to get only the latest balances for each group.
        latest_balances_qs = audit_qs.filter(uuid__in=Subquery(latest_audits_subquery))

        # Group by account and sum the balances of the latest records.
        balance_results = latest_balances_qs.values('account__uuid').annotate(
            total=Sum('balance_after')
        )

        # Initialize final balances with 0 for all requested parent codes.
        final_balances = {code: Decimal('0') for code in account_codes}
        # Create a map from child account codes to their requested parent codes for efficient lookup.
        child_to_parent_map = {}
        for child_uuid, child_code in account_uuid_to_code_map.items():
            parent_code = next((p for p in account_codes if child_code.startswith(p)), None)
            if parent_code:
                child_to_parent_map[child_uuid] = parent_code

        # Aggregate results into the final dictionary.
        for result in balance_results:
            account_uuid = str(result['account__uuid'])
            parent_code = child_to_parent_map.get(account_uuid)
            if parent_code:
                final_balances[parent_code] += result['total'] or Decimal('0')

        return final_balances

    except Exception as e:
        logger.error(f"Error in get_account_balances_on_date_in_bulk: {e}", exc_info=True)
        return {}


def get_account_movements_for_period_in_bulk(
    entity_uuid: str,
    account_codes: List[str],
    from_date: date,
    to_date: date,
    customer_uuids: Optional[List[str]] = None,
    vendor_uuids: Optional[List[str]] = None
) -> dict[str, Decimal]:
    """
    Calculates the total movement for a list of accounts over a period in a single bulk query.

    This function is optimized to avoid the N+1 problem by fetching all movements at once.

    Args:
        entity_uuid (str): The UUID of the entity.
        account_codes (List[str]): A list of account codes to query.
        from_date (date): The start date of the period.
        to_date (date): The end date of the period.
        customer_uuids (Optional[List[str]]): Optional list of customer UUIDs to filter by.
        vendor_uuids (Optional[List[str]]): Optional list of vendor UUIDs to filter by.

    Returns:
        Dict[str, Decimal]: A dictionary mapping each account code to its total movement.
    """
    if not account_codes:
        return {}

    try:
        q_objects = Q()
        for code in account_codes:
            q_objects |= Q(code__startswith=code)

        account_qs = AccountModel.objects.filter(
            q_objects,
            coa_model__entity__uuid=entity_uuid
        )
        account_uuid_to_code_map = {str(acc.uuid): acc.code for acc in account_qs}
        account_uuids = list(account_uuid_to_code_map.keys())

        if not account_uuids:
            return {}

        audit_qs = AccountBalanceAuditModel.objects.filter(
            entity_model__uuid=entity_uuid,
            account__uuid__in=account_uuids,
            timestamp__date__gte=from_date,
            timestamp__date__lte=to_date
        )

        if customer_uuids:
            audit_qs = audit_qs.filter(customer__uuid__in=customer_uuids)
        if vendor_uuids:
            audit_qs = audit_qs.filter(vendor__uuid__in=vendor_uuids)

        movement_results = audit_qs.values('account__uuid').annotate(
            total=Sum('change_amount')
        )

        final_movements = {code: Decimal('0') for code in account_codes}
        child_to_parent_map = {}
        for child_uuid, child_code in account_uuid_to_code_map.items():
            parent_code = next((p for p in account_codes if child_code.startswith(p)), None)
            if parent_code:
                child_to_parent_map[child_uuid] = parent_code

        for result in movement_results:
            account_uuid = str(result['account__uuid'])
            parent_code = child_to_parent_map.get(account_uuid)
            if parent_code:
                final_movements[parent_code] += result['total'] or Decimal('0')

        return final_movements

    except Exception as e:
        logger.error(f"Error in get_account_movements_for_period_in_bulk: {e}", exc_info=True)
        return {}

def get_total_movement_for_period(
    entity_uuid: str,
    account_codes: List[str],
    from_date: date,
    to_date: date,
    customer_uuids: Optional[List[str]] = None,
    vendor_uuids: Optional[List[str]] = None,
    opposite_account_codes: Optional[List[str]] = None
) -> Decimal:
    """
    Calculates the total movement (sum of 'change_amount') for a list of accounts over a period.

    This is used to get the total change for income statement accounts like Revenue or COGS.

    If 'opposite_account_codes' is provided, the query will only include movements from transactions
    that are part of a journal entry containing a transaction with an account in the opposite list.

    Args:
        entity_uuid (str): The UUID of the entity.
        account_codes (List[str]): A list of account codes to query.
        from_date (date): The start date of the period.
        to_date (date): The end date of the period.
        customer_uuids (Optional[List[str]]): Optional list of customer UUIDs to filter by.
        vendor_uuids (Optional[List[str]]): Optional list of vendor UUIDs to filter by.
        opposite_account_codes (Optional[List[str]]): Optional list of opposite account codes for validation.

    Returns:
        Decimal: The total aggregated movement amount.
    """
    if not account_codes:
        return Decimal('0')

    try:
        q_objects = Q()
        for code in account_codes:
            q_objects |= Q(code__startswith=code)

        account_qs = AccountModel.objects.filter(
            q_objects,
            coa_model__entity__uuid=entity_uuid
        )
        account_uuids = list(account_qs.values_list('uuid', flat=True))

        if not account_uuids:
            return Decimal('0')

        audit_qs = AccountBalanceAuditModel.objects.filter(
            entity_model__uuid=entity_uuid,
            account__uuid__in=account_uuids,
            timestamp__date__gte=from_date,
            timestamp__date__lte=to_date
        ).select_related('transaction__journal_entry')

        if customer_uuids:
            audit_qs = audit_qs.filter(customer__uuid__in=customer_uuids)
        if vendor_uuids:
            audit_qs = audit_qs.filter(vendor__uuid__in=vendor_uuids)

        if opposite_account_codes:
            # Build a Q object for opposite account codes
            opposite_q_objects = Q()
            for code in opposite_account_codes:
                opposite_q_objects |= Q(code__startswith=code)

            # Get UUIDs of opposite accounts
            opposite_account_uuids = list(AccountModel.objects.filter(
                opposite_q_objects,
                coa_model__entity__uuid=entity_uuid
            ).values_list('uuid', flat=True))

            if not opposite_account_uuids:
                return Decimal('0') # No valid opposite accounts, so movement is zero

            # Filter audits where the associated journal entry has a transaction with an opposite account
            # This is the validation logic requested.
            audit_qs = audit_qs.filter(
                Exists(
                    TransactionModel.objects.filter(
                        journal_entry_id=OuterRef('transaction__journal_entry_id'),
                        account_id__in=opposite_account_uuids
                    )
                )
            )
            print("audit_qs",audit_qs)

        total_movement = audit_qs.aggregate(
            total=Sum('change_amount')
        )['total'] or Decimal('0')

        return total_movement

    except Exception as e:
        logger.error(f"Error getting total movement for accounts {account_codes}: {e}", exc_info=True)
        return Decimal('0')

