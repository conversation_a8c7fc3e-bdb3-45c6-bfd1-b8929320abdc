"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements mixins for document (chung tu) related functionality.
"""

import re
from datetime import datetime

from django.core.exceptions import ValidationError
from django.db import models, transaction
from django.utils.translation import gettext_lazy as _
from simple_history.models import HistoricalRecords


class ChungTuMixIn(models.Model):
    """
    Implements common document (chung tu) functionality using a foreign key to ChungTuItem.

    This mixin provides standardized document functionality that is commonly used in
    Vietnamese business documents such as invoices, purchase orders, etc.
    The actual document fields are now stored in ChungTuItem model.

    This mixin also includes historical tracking using django-simple-history,
    which automatically tracks all changes to models that inherit from this mixin.

    Attributes
    ----------
    chung_tu_item : ForeignKey
        Reference to the ChungTuItem that contains document fields.
    chung_tu : ForeignKey
        Reference to the main ChungTu model.
    history : HistoricalRecords
        Historical records manager for tracking changes to the model.
        Provides access to historical versions and change tracking.
    """

    chung_tu_item = models.ForeignKey(
        'django_ledger.ChungTuItemModel',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        verbose_name=_('Document Item'),
        related_name='%(class)s_documents',
    )
    chung_tu = models.ForeignKey(
        'django_ledger.ChungTu',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        verbose_name=_('Document Reference'),
        related_name='%(class)s_chung_tu',
    )

    # Historical records tracking
    history = HistoricalRecords(inherit=True)

    class Meta:
        abstract = True

    # Property methods to access document fields through chung_tu_item
    @property
    def i_so_ct(self):
        """Get document number sequence from chung_tu_item."""
        return (
            self.chung_tu_item.i_so_ct
            if self.chung_tu_item
            else getattr(self, '_i_so_ct_value', None)
        )

    @i_so_ct.setter
    def i_so_ct(self, value):
        """Set document number sequence in chung_tu_item."""
        self._i_so_ct_value = value
        if self.chung_tu_item:
            self.chung_tu_item.i_so_ct = value

    @property
    def ma_nk(self):
        """Get document series code from chung_tu_item."""
        return (
            self.chung_tu_item.ma_nk
            if self.chung_tu_item
            else getattr(self, '_ma_nk_value', None)
        )

    @ma_nk.setter
    def ma_nk(self, value):
        """Set document series code in chung_tu_item."""
        self._ma_nk_value = value
        if self.chung_tu_item:
            self.chung_tu_item.ma_nk = value

    @property
    def so_ct(self):
        """Get document number from chung_tu_item."""
        return (
            self.chung_tu_item.so_ct
            if self.chung_tu_item
            else getattr(self, '_so_ct_value', None)
        )

    @so_ct.setter
    def so_ct(self, value):
        """Set document number in chung_tu_item."""
        self._so_ct_value = value
        if self.chung_tu_item:
            self.chung_tu_item.so_ct = value

    @property
    def ngay_ct(self):
        """Get document date from chung_tu_item."""
        return (
            self.chung_tu_item.ngay_ct
            if self.chung_tu_item
            else getattr(self, '_ngay_ct_value', None)
        )

    @ngay_ct.setter
    def ngay_ct(self, value):
        """Set document date in chung_tu_item."""
        self._ngay_ct_value = value
        if self.chung_tu_item:
            self.chung_tu_item.ngay_ct = value

    @property
    def ngay_lct(self):
        """Get document creation date from chung_tu_item."""
        return (
            self.chung_tu_item.ngay_lct
            if self.chung_tu_item
            else getattr(self, '_ngay_lct_value', None)
        )

    @ngay_lct.setter
    def ngay_lct(self, value):
        """Set document creation date in chung_tu_item."""
        self._ngay_lct_value = value
        if self.chung_tu_item:
            self.chung_tu_item.ngay_lct = value

    def clean(self):
        """
        Perform model validation for document fields.
        """
        super().clean()
        # Add custom validation logic here if needed

    def _validate_so_ct_format(self):
        """
        Kiểm tra format của so_ct có đúng với template không
        - Check MM (tháng), YY (năm), # (số tự nhiên)
        - Check số lượng # tương ứng với số trong so_ct
        - Check phần số trong so_ct phải khớp với i_so_ct
        """
        if not self.chung_tu_item.so_ct or not self.chung_tu_item.ma_nk:
            return False

        template = self.chung_tu_item.ma_nk.so_ct_mau
        if not template:
            return False

        # Lấy ngày từ so_ct để validate MM, YY
        current_date = (
            self.chung_tu_item.ngay_ct
            if self.chung_tu_item.ngay_ct
            else datetime.now().date()
        )

        # Tìm phần số [######]
        number_pattern = r'\[#+\]'
        number_matches = re.findall(number_pattern, template)

        if number_matches:
            # Lấy số chữ số cần thiết
            digit_count = len(number_matches[0]) - 2  # Bỏ [ và ]

            # Tách prefix và suffix từ template
            parts = template.split(number_matches[0])
            if len(parts) == 2:
                prefix_template = parts[0]
                suffix_template = parts[1]

                # Tạo prefix và suffix thực tế từ ngày hiện tại
                prefix = prefix_template
                if '[MM]' in prefix:
                    prefix = prefix.replace('[MM]', f"{current_date.month:02d}")
                if '[YY]' in prefix:
                    prefix = prefix.replace('[YY]', f"{current_date.year % 100:02d}")
                if '[YYYY]' in prefix:
                    prefix = prefix.replace('[YYYY]', f"{current_date.year:04d}")

                suffix = suffix_template

                # Kiểm tra format so_ct
                if self.chung_tu_item.so_ct.startswith(
                    prefix
                ) and self.chung_tu_item.so_ct.endswith(suffix):
                    # Lấy phần số
                    start_pos = len(prefix)
                    end_pos = (
                        len(self.chung_tu_item.so_ct) - len(suffix)
                        if suffix
                        else len(self.chung_tu_item.so_ct)
                    )
                    number_part = self.chung_tu_item.so_ct[start_pos:end_pos]

                    # Kiểm tra phần số: đúng số lượng chữ số và là số tự nhiên
                    if len(number_part) == digit_count and number_part.isdigit():
                        # Kiểm tra phần số trong so_ct phải khớp với i_so_ct
                        number_in_so_ct = int(number_part)
                        if self.chung_tu_item.i_so_ct is not None:
                            return number_in_so_ct == self.chung_tu_item.i_so_ct
                        return True
                    return False
        else:
            # Không có placeholder số, so sánh trực tiếp với template đã thay thế
            expected = template
            if '[MM]' in expected:
                expected = expected.replace('[MM]', f"{current_date.month:02d}")
            if '[YY]' in expected:
                expected = expected.replace('[YY]', f"{current_date.year % 100:02d}")
            if '[YYYY]' in expected:
                expected = expected.replace('[YYYY]', f"{current_date.year:04d}")
            return self.chung_tu_item.so_ct == expected

        return False

    def _generate_so_ct_from_number(self, number):
        """
        Tạo so_ct từ số thứ tự theo template
        """
        if not self.chung_tu_item.ma_nk:
            return None

        template = self.chung_tu_item.ma_nk.so_ct_mau
        if not template:
            return None

        # Thay thế ngày tháng
        current_date = (
            self.chung_tu_item.ngay_ct
            if self.chung_tu_item.ngay_ct
            else datetime.now().date()
        )
        result = template
        if '[MM]' in result:
            result = result.replace('[MM]', f"{current_date.month:02d}")
        if '[YY]' in result:
            result = result.replace('[YY]', f"{current_date.year % 100:02d}")
        if '[YYYY]' in result:
            result = result.replace('[YYYY]', f"{current_date.year:04d}")

        # Thay thế phần số
        number_pattern = r'\[#+\]'
        number_matches = re.findall(number_pattern, result)

        if number_matches:
            digit_count = len(number_matches[0]) - 2  # Bỏ [ và ]
            formatted_number = f"{number:0{digit_count}d}"
            result = re.sub(number_pattern, formatted_number, result, count=1)

        return result

    def _get_date_key_for_constraint(self, kieu_trung_so):
        """
        Tính toán ngày ràng buộc dựa trên kiểu trùng số
        - Theo ngày ('2'): Lưu ngày đầy đủ (VD: 2025-11-15)
        - Theo tháng ('3'): Lưu ngày đầy đủ (VD: 2025-07-29) - lưu vào ngày chính xác được cung cấp
        - Theo năm ('4'): Lưu ngày đầu năm (VD: 2025-01-01)

        Parameters
        ----------
        kieu_trung_so : str
            Loại ràng buộc ('2': ngày, '3': tháng, '4': năm)

        Returns
        -------
        date
            Ngày ràng buộc phù hợp đã được chuẩn hóa
        """
        current_date = (
            self.chung_tu_item.ngay_ct
            if self.chung_tu_item.ngay_ct
            else datetime.now().date()
        )

        if kieu_trung_so == '2':  # Kiểm tra theo ngày - lưu ngày đầy đủ
            return current_date
        elif (
            kieu_trung_so == '3'
        ):  # Kiểm tra theo tháng - lưu ngày đầy đủ (ngày chính xác)
            return current_date
        elif kieu_trung_so == '4':  # Kiểm tra theo năm - lưu ngày đầu năm
            return current_date.replace(month=1, day=1)
        else:
            # Fallback - không nên xảy ra với logic hiện tại
            return current_date

    def _create_or_get_quyen_chung_tu_ngay(self, quyen_chung_tu, ngay_nk):
        """
        Tạo hoặc lấy record từ quyen_chung_tu_ngay

        Parameters
        ----------
        quyen_chung_tu : QuyenChungTu
            Quyền chứng từ
        ngay_nk : date
            Ngày ràng buộc

        Returns
        -------
        QuyenChungTuNgay
            Record từ bảng quyen_chung_tu_ngay
        """
        from django_ledger.models.quyen_chung_tu.quyen_chung_tu_ngay import (
            QuyenChungTuNgay,
        )

        ngay_record, _ = QuyenChungTuNgay.objects.get_or_create(
            quyen_chung_tu=quyen_chung_tu,
            ma_nk=quyen_chung_tu.ma_nk,
            ngay_nk=ngay_nk,
            defaults={'i_so_ct_ht': 0},
        )
        return ngay_record

    def _get_appropriate_i_so_ct_ht(self, quyen_chung_tu):
        """
        Lấy i_so_ct_ht phù hợp dựa trên kieu_trung_so

        Parameters
        ----------
        quyen_chung_tu : QuyenChungTu
            Quyền chứng từ

        Returns
        -------
        tuple
            (i_so_ct_ht, source_object) - source_object là quyen_chung_tu hoặc quyen_chung_tu_ngay
        """
        if quyen_chung_tu.kieu_trung_so == '1':
            # Không có ràng buộc - sử dụng quyen_chung_tu
            return quyen_chung_tu.i_so_ct_ht, quyen_chung_tu
        else:
            # Có ràng buộc theo thời gian - sử dụng quyen_chung_tu_ngay
            ngay_nk = self._get_date_key_for_constraint(quyen_chung_tu.kieu_trung_so)
            ngay_record = self._create_or_get_quyen_chung_tu_ngay(
                quyen_chung_tu, ngay_nk
            )
            return ngay_record.i_so_ct_ht, ngay_record

    def _update_appropriate_i_so_ct_ht(self, source_object, new_value):
        """
        Cập nhật i_so_ct_ht vào bảng phù hợp

        Parameters
        ----------
        source_object : QuyenChungTu hoặc QuyenChungTuNgay
            Object cần cập nhật
        new_value : int
            Giá trị mới cho i_so_ct_ht
        """
        source_object.i_so_ct_ht = new_value
        source_object.save()

    def _process_no_constraint(self, quyen_chung_tu):
        """
        Xử lý logic cho trường hợp không có ràng buộc (kieu_trung_so = '1')

        Parameters
        ----------
        quyen_chung_tu : QuyenChungTu
            Quyền chứng từ
        """
        current_i_so_ct_ht = quyen_chung_tu.i_so_ct_ht

        # Luôn kiểm tra trùng lặp trước
        if self._check_duplicate_i_so_ct(quyen_chung_tu, self.chung_tu_item.i_so_ct):
            raise ValidationError(
                f"Số chứng từ i_so_ct={self.chung_tu_item.i_so_ct} đã được sử dụng. "
                f"Vui lòng sử dụng số khác."
            )

        if self.chung_tu_item.i_so_ct > current_i_so_ct_ht:
            # i_so_ct lớn hơn current_i_so_ct_ht: số mới hợp lệ
            # Validate format so_ct với i_so_ct hiện tại
            if not self._validate_so_ct_format():
                expected_so_ct = self._generate_so_ct_from_number(
                    self.chung_tu_item.i_so_ct
                )
                raise ValidationError(
                    f"so_ct '{self.chung_tu_item.so_ct}' không khớp với i_so_ct={self.chung_tu_item.i_so_ct}. "
                    f"Expected: '{expected_so_ct}'"
                )
            # Cập nhật i_so_ct_ht = i_so_ct
            quyen_chung_tu.i_so_ct_ht = self.chung_tu_item.i_so_ct
            quyen_chung_tu.save()
        elif self.chung_tu_item.i_so_ct == current_i_so_ct_ht:
            # i_so_ct bằng current_i_so_ct_ht: validate format và cập nhật
            if not self._validate_so_ct_format():
                expected_so_ct = self._generate_so_ct_from_number(
                    self.chung_tu_item.i_so_ct
                )
                raise ValidationError(
                    f"so_ct '{self.chung_tu_item.so_ct}' không khớp với i_so_ct={self.chung_tu_item.i_so_ct}. "
                    f"Expected: '{expected_so_ct}'"
                )
            # Cập nhật i_so_ct_ht = i_so_ct
            quyen_chung_tu.i_so_ct_ht = self.chung_tu_item.i_so_ct
            quyen_chung_tu.save()
        else:
            # i_so_ct < i_so_ct_ht: Báo lỗi thay vì auto-increment
            raise ValidationError(
                f"Số chứng từ i_so_ct={self.chung_tu_item.i_so_ct} không hợp lệ. "
                f"Số chứng từ phải lớn hơn {current_i_so_ct_ht}. "
                f"Vui lòng sử dụng số từ {current_i_so_ct_ht + 1} trở lên."
            )

    def _check_duplicate_i_so_ct(self, quyen_chung_tu, i_so_ct):
        """
        Kiểm tra xem i_so_ct đã được sử dụng trong cùng kỳ ràng buộc chưa

        Parameters
        ----------
        quyen_chung_tu : QuyenChungTu
            Quyền chứng từ
        i_so_ct : int
            Số chứng từ cần kiểm tra

        Returns
        -------
        bool
            True nếu i_so_ct đã được sử dụng, False nếu chưa
        """

        from django_ledger.models.chung_tu import ChungTuItemModel

        # Lấy ngày ràng buộc dựa trên kieu_trung_so
        ngay_nk = self._get_date_key_for_constraint(quyen_chung_tu.kieu_trung_so)

        # Tạo filter dựa trên kieu_trung_so
        if quyen_chung_tu.kieu_trung_so == '1':  # Không có ràng buộc
            # Kiểm tra toàn bộ trong cùng ma_nk
            existing_docs = ChungTuItemModel.objects.filter(
                ma_nk=quyen_chung_tu, i_so_ct=i_so_ct
            )
        elif quyen_chung_tu.kieu_trung_so == '2':  # Kiểm tra theo ngày
            # Kiểm tra trong cùng ngày
            existing_docs = ChungTuItemModel.objects.filter(
                ma_nk=quyen_chung_tu, i_so_ct=i_so_ct, ngay_ct=ngay_nk
            )
        elif quyen_chung_tu.kieu_trung_so == '3':  # Kiểm tra theo tháng
            # Kiểm tra trong cùng tháng
            existing_docs = ChungTuItemModel.objects.filter(
                ma_nk=quyen_chung_tu,
                i_so_ct=i_so_ct,
                ngay_ct__year=ngay_nk.year,
                ngay_ct__month=ngay_nk.month,
            )
        elif quyen_chung_tu.kieu_trung_so == '4':  # Kiểm tra theo năm
            # Kiểm tra trong cùng năm
            existing_docs = ChungTuItemModel.objects.filter(
                ma_nk=quyen_chung_tu, i_so_ct=i_so_ct, ngay_ct__year=ngay_nk.year
            )
        else:
            # Fallback - không nên xảy ra
            return False

        # Check if any of the found documents have different i_so_ct than current record
        current_i_so_ct = (
            self.chung_tu_item.i_so_ct
            if hasattr(self, 'chung_tu_item') and self.chung_tu_item
            else i_so_ct
        )

        if hasattr(self, 'pk') and self.pk:
            current_model_name = self.__class__.__name__.lower()
            current_pk_str = str(self.pk)

            # Check each document to see if it's a different document with same i_so_ct
            for doc in existing_docs:
                # Get the model name from the content type
                doc_model_name = doc.content_type.model

                # If this document is NOT the current document AND has same i_so_ct, it's a duplicate
                if doc.i_so_ct == current_i_so_ct and not (
                    doc_model_name == current_model_name
                    and str(doc.object_id) == current_pk_str
                ):
                    return True

            # No duplicates found
            return False
        else:
            # No current PK (creating new), so any existing docs with same i_so_ct are duplicates
            return existing_docs.filter(i_so_ct=current_i_so_ct).exists()

    def _check_duplicate_i_so_ct_in_chung_tu(self, chung_tu, i_so_ct):
        """
        Kiểm tra xem i_so_ct đã được sử dụng trong cùng chung_tu chưa

        Parameters
        ----------
        chung_tu : ChungTu
            Chứng từ cần kiểm tra
        i_so_ct : int
            Số chứng từ cần kiểm tra

        Returns
        -------
        bool
            True nếu i_so_ct đã được sử dụng, False nếu chưa
        """
        # Find all ChungTuItemModel records that reference the same chung_tu
        # We need to find documents that have ChungTuItemModel records where:
        # 1. The document's chung_tu field points to the same chung_tu
        # 2. The i_so_ct matches

        from django_ledger.models.chung_tu import ChungTuItemModel

        # First, get all ChungTuItemModel records with the same i_so_ct
        existing_docs = ChungTuItemModel.objects.filter(i_so_ct=i_so_ct)

        # Filter to only include documents that belong to the same chung_tu
        # We need to check the content_object's chung_tu field
        matching_docs = []
        for doc_item in existing_docs:
            if doc_item.content_object and hasattr(doc_item.content_object, 'chung_tu'):
                if (
                    doc_item.content_object.chung_tu
                    and doc_item.content_object.chung_tu.pk == chung_tu.pk
                ):
                    matching_docs.append(doc_item)

        # Loại trừ chính document hiện tại nếu đang update
        if hasattr(self, 'pk') and self.pk:
            from django.contrib.contenttypes.models import ContentType

            current_content_type = ContentType.objects.get_for_model(self.__class__)
            current_pk_str = str(self.pk)

            matching_docs = [
                doc
                for doc in matching_docs
                if not (
                    doc.content_type == current_content_type
                    and doc.object_id == current_pk_str
                )
            ]

        return len(matching_docs) > 0

    def _process_chung_tu_document_numbers(self, chung_tu):
        """
        Xử lý logic cho trường hợp sử dụng chung_tu (không có ma_nk)

        Parameters
        ----------
        chung_tu : ChungTu
            Chứng từ
        """
        current_i_so_ct = chung_tu.i_so_ct
        request_i_so_ct = self.chung_tu_item.i_so_ct

        # Kiểm tra nếu đang update và i_so_ct không thay đổi thì bỏ qua duplicate check
        is_update = hasattr(self, 'pk') and self.pk is not None
        if is_update:
            # Lấy i_so_ct hiện tại của record này từ database
            try:
                current_record = self.__class__.objects.get(pk=self.pk)
                current_record_i_so_ct = (
                    current_record.chung_tu_item.i_so_ct
                    if current_record.chung_tu_item
                    else None
                )

                if current_record_i_so_ct == request_i_so_ct:
                    # Cùng số, không cần kiểm tra duplicate, cho phép update
                    return
            except self.__class__.DoesNotExist:
                # Record not found, treating as create
                pass

        # Check for duplicates
        if self._check_duplicate_i_so_ct_in_chung_tu(chung_tu, request_i_so_ct):
            # Có trùng lặp: báo lỗi
            raise ValidationError(
                f"Số chứng từ i_so_ct={request_i_so_ct} đã được sử dụng trong "
                f"cùng chứng từ. Vui lòng sử dụng số khác."
            )

        if self.chung_tu_item.i_so_ct > current_i_so_ct:
            # i_so_ct lớn hơn current: số mới hợp lệ, cập nhật i_so_ct trong chung_tu
            chung_tu.i_so_ct = self.chung_tu_item.i_so_ct
            chung_tu.save()
        elif self.chung_tu_item.i_so_ct <= current_i_so_ct:
            # i_so_ct <= current: chấp nhận số này nhưng không cập nhật current
            # (số này có thể là số cũ hơn hoặc bằng current)
            pass

    def _process_with_constraint(self, quyen_chung_tu):
        """
        Xử lý logic cho trường hợp có ràng buộc theo thời gian (kieu_trung_so != '1')

        Parameters
        ----------
        quyen_chung_tu : QuyenChungTu
            Quyền chứng từ
        """
        current_i_so_ct_ht, source_object = self._get_appropriate_i_so_ct_ht(
            quyen_chung_tu
        )

        # Luôn kiểm tra trùng lặp trước
        if self._check_duplicate_i_so_ct(quyen_chung_tu, self.chung_tu_item.i_so_ct):
            raise ValidationError(
                f"Số chứng từ i_so_ct={self.chung_tu_item.i_so_ct} đã được sử dụng trong "
                f"cùng kỳ ràng buộc. Vui lòng sử dụng số khác."
            )

        if self.chung_tu_item.i_so_ct > current_i_so_ct_ht:
            # i_so_ct lớn hơn current_i_so_ct_ht: số mới hợp lệ
            # Validate format so_ct với i_so_ct hiện tại
            if not self._validate_so_ct_format():
                expected_so_ct = self._generate_so_ct_from_number(
                    self.chung_tu_item.i_so_ct
                )
                raise ValidationError(
                    f"so_ct '{self.chung_tu_item.so_ct}' không khớp với i_so_ct={self.chung_tu_item.i_so_ct}. "
                    f"Expected: '{expected_so_ct}'"
                )
            # Cập nhật i_so_ct_ht = i_so_ct
            self._update_appropriate_i_so_ct_ht(
                source_object, self.chung_tu_item.i_so_ct
            )
        elif self.chung_tu_item.i_so_ct == current_i_so_ct_ht:
            # i_so_ct bằng current_i_so_ct_ht: validate format và cập nhật
            if not self._validate_so_ct_format():
                expected_so_ct = self._generate_so_ct_from_number(
                    self.chung_tu_item.i_so_ct
                )
                raise ValidationError(
                    f"so_ct '{self.chung_tu_item.so_ct}' không khớp với i_so_ct={self.chung_tu_item.i_so_ct}. "
                    f"Expected: '{expected_so_ct}'"
                )
            # Cập nhật i_so_ct_ht = i_so_ct
            self._update_appropriate_i_so_ct_ht(
                source_object, self.chung_tu_item.i_so_ct
            )
        else:
            # i_so_ct < i_so_ct_ht: Báo lỗi thay vì auto-increment
            constraint_type = {'2': 'ngày', '3': 'tháng', '4': 'năm'}.get(
                quyen_chung_tu.kieu_trung_so, 'kỳ'
            )

            raise ValidationError(
                f"Số chứng từ i_so_ct={self.chung_tu_item.i_so_ct} không hợp lệ. "
                f"Số chứng từ phải lớn hơn {current_i_so_ct_ht} trong cùng {constraint_type}. "
                f"Vui lòng sử dụng số từ {current_i_so_ct_ht + 1} trở lên."
            )

    def _process_document_numbers(self, quyen_chung_tu):
        """
        Xử lý logic chính cho so_ct, i_so_ct và i_so_ct_ht
        so_ct và i_so_ct là bắt buộc phải truyền vào
        """
        # Kiểm tra bắt buộc - sử dụng ChungTuItem values
        if not self.chung_tu_item.so_ct:
            raise ValidationError("so_ct là bắt buộc")

        if self.chung_tu_item.i_so_ct is None:
            raise ValidationError("i_so_ct là bắt buộc")

        # Xử lý theo kieu_trung_so
        if quyen_chung_tu.kieu_trung_so == '1':
            # Không có ràng buộc
            self._process_no_constraint(quyen_chung_tu)
        else:
            # Có ràng buộc theo thời gian
            self._process_with_constraint(quyen_chung_tu)

    def _detect_chung_tu_fields_changed(self, field_values):
        """
        Detect if any ChungTu fields have actually changed by comparing with database values.

        Parameters
        ----------
        field_values : dict
            Field values from API request

        Returns
        -------
        bool
            True if ChungTu fields changed, False otherwise
        """
        if not self.chung_tu_item:
            # No existing ChungTuItem, so any ChungTu fields are new
            return bool(field_values) or any(
                [
                    hasattr(self, '_i_so_ct_value'),
                    hasattr(self, '_ma_nk_value'),
                    hasattr(self, '_so_ct_value'),
                    hasattr(self, '_ngay_ct_value'),
                    hasattr(self, '_ngay_lct_value'),
                ]
            )

        # Get original values from database to compare
        try:
            from django_ledger.models.chung_tu import ChungTuItemModel

            original_chung_tu_item = ChungTuItemModel.objects.get(
                pk=self.chung_tu_item.pk
            )
        except:
            # If we can't get original, assume changed
            return True

        chung_tu_fields = ['i_so_ct', 'ma_nk', 'so_ct', 'ngay_ct', 'ngay_lct']

        for field in chung_tu_fields:
            # Get original value from database
            original_value = getattr(original_chung_tu_item, field, None)

            # Get current value from instance (after serializer set it)
            current_value = None

            # Check field_values from API request first
            if field in field_values:
                current_value = field_values[field]
            # Check temporary values set by property setters
            elif hasattr(self, f'_{field}_value'):
                current_value = getattr(self, f'_{field}_value')
            # Check instance property
            elif hasattr(self, field):
                current_value = getattr(self, field)
            # Check chung_tu_item current value
            else:
                current_value = getattr(self.chung_tu_item, field, None)

            # Compare values
            if field == 'ma_nk':
                # Handle UUID fields
                original_str = str(original_value.pk) if original_value else None
                current_str = (
                    str(current_value.pk)
                    if hasattr(current_value, 'pk')
                    else str(current_value) if current_value else None
                )
                if original_str != current_str:
                    return True
            else:
                if original_value != current_value:
                    return True

        return False

    def _sync_data_to_chung_tu_item(self, **field_values):
        """
        Sync data from instance or provided values to ChungTuItem.
        This method handles data transfer from API requests to ChungTuItem.
        """
        if not self.chung_tu_item:
            from django_ledger.models.chung_tu import ChungTuItemModel

            self.chung_tu_item = ChungTuItemModel()

        # Priority: field_values > temporary values > current ChungTuItem values

        # Handle i_so_ct
        if 'i_so_ct' in field_values:
            self.chung_tu_item.i_so_ct = field_values['i_so_ct']
        elif hasattr(self, '_i_so_ct_value'):
            self.chung_tu_item.i_so_ct = self._i_so_ct_value
        elif not self.chung_tu_item.i_so_ct:
            # If no value is set, this will be handled by document number processing
            pass

        # Handle ma_nk
        if 'ma_nk' in field_values:
            self.chung_tu_item.ma_nk = field_values['ma_nk']
        elif hasattr(self, '_ma_nk_value'):
            self.chung_tu_item.ma_nk = self._ma_nk_value

        # Handle so_ct
        if 'so_ct' in field_values:
            self.chung_tu_item.so_ct = field_values['so_ct']
        elif hasattr(self, '_so_ct_value'):
            self.chung_tu_item.so_ct = self._so_ct_value

        # Handle ngay_ct
        if 'ngay_ct' in field_values:
            self.chung_tu_item.ngay_ct = field_values['ngay_ct']
        elif hasattr(self, '_ngay_ct_value'):
            self.chung_tu_item.ngay_ct = self._ngay_ct_value

        # Handle ngay_lct
        if 'ngay_lct' in field_values:
            self.chung_tu_item.ngay_lct = field_values['ngay_lct']
        elif hasattr(self, '_ngay_lct_value'):
            self.chung_tu_item.ngay_lct = self._ngay_lct_value

    def save(self, *args, **kwargs):
        """
        Override save method to validate and auto-generate document number.
        """
        # Extract field values from kwargs if provided (for API requests)
        field_values = kwargs.pop('chung_tu_fields', {})

        # Check if this is an update operation and if ChungTu fields have changed
        skip_chung_tu_processing = kwargs.pop('skip_chung_tu_processing', False)
        is_update = self.pk is not None

        # Skip ChungTu validation if this is a soft delete operation
        deleted_field = getattr(self, 'deleted', None)
        force_delete = kwargs.get('force_delete', False)
        skip_flag = getattr(self, '_skip_chung_tu_for_delete', False)

        is_soft_delete = deleted_field is not None or force_delete or skip_flag

        if is_soft_delete:
            skip_chung_tu_processing = True
            if self.chung_tu_item:
                self.chung_tu_item._skip_validation = True

        # Skip ALL ChungTu processing during soft delete
        if is_soft_delete:
            super().save(*args, **kwargs)
            return

        # Prevent double processing - check if ChungTu processing already completed
        if hasattr(self, '_chung_tu_processed') and self._chung_tu_processed:
            super().save(*args, **kwargs)
            return

        # For updates, check if ChungTu fields actually changed
        chung_tu_fields_changed = False
        if is_update and not skip_chung_tu_processing:
            chung_tu_fields_changed = self._detect_chung_tu_fields_changed(field_values)

        # Create or update ChungTuItem if needed
        if not self.chung_tu_item:
            from django.contrib.contenttypes.models import ContentType

            from django_ledger.models.chung_tu import ChungTuItemModel

            self.chung_tu_item = ChungTuItemModel()

        # Sync data from temporary values to ChungTuItem
        # This ensures data set by property setters is copied to ChungTuItem
        if hasattr(self, '_i_so_ct_value'):
            self.chung_tu_item.i_so_ct = self._i_so_ct_value
        if hasattr(self, '_ma_nk_value'):
            self.chung_tu_item.ma_nk = self._ma_nk_value
        if hasattr(self, '_so_ct_value'):
            self.chung_tu_item.so_ct = self._so_ct_value
        if hasattr(self, '_ngay_ct_value'):
            self.chung_tu_item.ngay_ct = self._ngay_ct_value
        if hasattr(self, '_ngay_lct_value'):
            self.chung_tu_item.ngay_lct = self._ngay_lct_value

        # Also sync any additional field values passed in kwargs
        self._sync_data_to_chung_tu_item(**field_values)

        # Backward-compat: if the concrete model defines a real ma_nk field,
        # ensure it is propagated into chung_tu_item for correct processing.
        try:
            if (
                hasattr(self, 'ma_nk')
                and self.ma_nk
                and self.chung_tu_item
                and not getattr(self.chung_tu_item, 'ma_nk', None)
            ):
                self.chung_tu_item.ma_nk = self.ma_nk
        except Exception:
            pass

        # Set up the generic foreign key relationship
        if self.chung_tu_item and not self.chung_tu_item.content_object:
            from django.contrib.contenttypes.models import ContentType

            content_type = ContentType.objects.get_for_model(self.__class__)
            self.chung_tu_item.content_type = content_type
            # object_id will be set after the main object is saved

        # Skip ChungTu processing if this is an update and no ChungTu fields changed
        if is_update and not chung_tu_fields_changed and not skip_chung_tu_processing:
            self._sync_data_to_chung_tu_item(**field_values)
            if self.chung_tu_item:
                self.chung_tu_item.save()
            super().save(*args, **kwargs)
            if self.chung_tu_item and not self.chung_tu_item.object_id:
                self.chung_tu_item.object_id = self.pk
                self.chung_tu_item.save()
            self._chung_tu_processed = True
            return

        # Process document numbers
        ma_nk_value = self.chung_tu_item.ma_nk

        if not ma_nk_value:
            # Process using chung_tu when ma_nk is not provided
            if (
                hasattr(self, 'chung_tu')
                and self.chung_tu
                and self.chung_tu_item.i_so_ct is not None
            ):
                with transaction.atomic():
                    from django_ledger.models.chung_tu.chung_tu import ChungTu

                    chung_tu = ChungTu.objects.select_for_update().get(
                        pk=self.chung_tu.pk
                    )
                    self._process_chung_tu_document_numbers(chung_tu)

            if self.chung_tu_item:
                self.chung_tu_item.save()
            super().save(*args, **kwargs)
            if self.chung_tu_item and not self.chung_tu_item.object_id:
                self.chung_tu_item.object_id = self.pk
                self.chung_tu_item.save()
            self._chung_tu_processed = True
            return

        # Process using quyen_chung_tu
        with transaction.atomic():
            from django_ledger.models.quyen_chung_tu import QuyenChungTu

            # Sử dụng select_for_update() từ SafeDeleteManagerMixin để tránh lỗi DISTINCT với FOR UPDATE
            quyen_chung_tu = QuyenChungTu.objects.select_for_update().get(
                pk=ma_nk_value.pk
            )
            self._process_document_numbers(quyen_chung_tu)

            if self.chung_tu_item:
                self.chung_tu_item.save()

        super().save(*args, **kwargs)

        if self.chung_tu_item and not self.chung_tu_item.object_id:
            self.chung_tu_item.object_id = self.pk
            self.chung_tu_item.save()

        self._chung_tu_processed = True

    def delete(self, *args, **kwargs):
        """
        Override delete method to skip ChungTu validation during soft delete.
        """
        # Set flag to skip ChungTu processing during soft delete
        self._skip_chung_tu_for_delete = True
        return super().delete(*args, **kwargs)

    def __str__(self):
        """
        String representation of the document.
        """
        so_ct = self.so_ct if self.so_ct else "N/A"
        ngay_ct = self.ngay_ct if self.ngay_ct else "N/A"
        return f"{so_ct} - {ngay_ct}"
