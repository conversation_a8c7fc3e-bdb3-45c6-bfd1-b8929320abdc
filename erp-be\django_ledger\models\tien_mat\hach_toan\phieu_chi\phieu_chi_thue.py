"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuChiThue (Payment Voucher Tax) Model
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class PhieuChiThueModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the PhieuChiThueModel.
    """

    def for_phieu_chi(self, phieu_chi_id):  # noqa: C901
        """
        Returns tax details for a specific payment voucher.

        Parameters
        ----------
        phieu_chi_id: UUID
            The PhieuChiModel UUID used for filtering the QuerySet.

        Returns
        -------
        PhieuChiThueModelQueryset
            A QuerySet of PhieuChiThueModel with applied filters.
        """
        return self.filter(phieu_chi__uuid=phieu_chi_id)


class PhieuChiThueModelManager(Manager):
    """
    A custom defined PhieuChiThueModelManager that will act as an interface to handling the DB queries to the  # noqa: E501
    PhieuChiThueModel.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom QuerySet for the PhieuChiThueModel.
        """
        return PhieuChiThueModelQueryset(self.model, using=self._db)

    def for_phieu_chi(self, phieu_chi_id):  # noqa: C901
        """
        Returns tax details for a specific payment voucher.

        Parameters
        ----------
        phieu_chi_id: UUID
            The PhieuChiModel UUID used for filtering the QuerySet.

        Returns
        -------
        PhieuChiThueModelQueryset
            A QuerySet of PhieuChiThueModel with applied filters.
        """
        return self.get_queryset().for_phieu_chi(phieu_chi_id=phieu_chi_id)


class PhieuChiThueModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the PhieuChiThueModel database will inherit from.  # noqa: E501
    The PhieuChiThueModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501

    phieu_chi: PhieuChiModel
        Reference to the payment voucher.

    id: int
        ID of the tax detail.

    line: int
        Line number.

    so_ct0: str
        Original document number.

    so_ct2: str
        Document number 2.

    ngay_ct0: datetime
        Original document date.

    ma_thue: str
        Tax code.

    thue_suat: Decimal
        Tax rate.

    ma_mau_ct: str
        Document template code.

    ma_mau_bc: str
        Report template code.

    ma_tc_thue: str
        Tax nature code.

    ma_kh: str
        Customer code.

    ten_kh_thue: str
        Tax customer name.

    dia_chi: str
        Address.

    ma_so_thue: str
        Tax number.

    ten_vt_thue: str
        Tax item name.

    t_tien_nt: Decimal
        Total amount in foreign currency.

    t_tien: Decimal
        Total amount.

    tk_thue_no: str
        Tax debit account.

    ten_tk_thue_no: str
        Tax debit account name.

    tk_du: str
        Balance account.

    ten_tk_du: str
        Balance account name.

    t_thue_nt: Decimal
        Total tax in foreign currency.

    t_thue: Decimal
        Total tax.

    ma_kh9: str
        Customer code 9.

    ten_kh9: str
        Customer name 9.

    ma_tt: str
        Payment term code.

    ten_tt: str
        Payment term name.

    ghi_chu: str
        Note.

    id_tt: int
        Payment ID.

    ma_bp: BoPhanModel
        Department.

    ma_vv: VuViecModel
        Task.

    ma_hd: ContractModel
        Contract.

    ma_dtt: DotThanhToanModel
        Payment batch.

    ma_ku: KheUocModel
        Agreement.

    ma_phi: PhiModel
        Fee.

    ma_sp: VatTuModel
        Product.

    ma_lsx: str
        Production order code.

    ma_cp0: ChiPhiKhongHopLeModel
        Invalid expense.
    """

    uuid = models.UUIDField(default=uuid4, editable=False, primary_key=True)
    phieu_chi = models.ForeignKey(
        "django_ledger.PhieuChiModel",
        on_delete=models.CASCADE,
        related_name="thue",
        verbose_name=_("Phiếu chi"),
    )

    # Basic information
    id = models.IntegerField(null=True, blank=True, verbose_name=_("ID"))
    line = models.IntegerField(verbose_name=_("Dòng"))
    # Document information
    so_ct0 = models.CharField(
        max_length=50, null=True, blank=True, verbose_name=_("Số chứng từ gốc")
    )
    so_ct2 = models.CharField(
        max_length=50, null=True, blank=True, verbose_name=_("Số chứng từ 2")
    )
    ngay_ct0 = models.DateField(
        null=True, blank=True, verbose_name=_("Ngày chứng từ gốc")
    )
    # Tax information
    ma_thue = models.CharField(
        max_length=10, null=True, blank=True, verbose_name=_("Mã thuế")
    )
    thue_suat = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Thuế suất"),
    )

    # Template information
    ma_mau_ct = models.CharField(
        max_length=10, null=True, blank=True, verbose_name=_("Mã mẫu chứng từ")
    )
    ma_mau_bc = models.CharField(
        max_length=10, null=True, blank=True, verbose_name=_("Mã mẫu báo cáo")
    )
    ma_tc_thue = models.ForeignKey(
        "django_ledger.TinhChatThueModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã tính chất thuế"),
    )
    # Tax subject information
    ma_kh = models.ForeignKey(
        "django_ledger.CustomerModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã khách hàng"),
    )
    ten_kh_thue = models.CharField(
        max_length=100, null=True, blank=True, verbose_name=_("Tên khách hàng thuế")
    )
    dia_chi = models.CharField(
        max_length=255, null=True, blank=True, verbose_name=_("Địa chỉ")
    )
    ma_so_thue = models.CharField(
        max_length=20, null=True, blank=True, verbose_name=_("Mã số thuế")
    )
    ten_vt_thue = models.CharField(
        max_length=255, null=True, blank=True, verbose_name=_("Tên vật tư thuế")
    )
    # Amount information
    t_tien_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_("Tổng tiền ngoại tệ"),
        null=True,
        blank=True,
    )
    t_tien = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_("Tổng tiền"),
        null=True,
        blank=True,
    )

    # Account information
    tk_thue_no = models.ForeignKey(
        'django_ledger.AccountModel',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='phieu_chi_thue_tk_thue_no',
        verbose_name=_("Tài khoản thuế nợ"),
    )
    ten_tk_thue_no = models.CharField(
        max_length=100, null=True, blank=True, verbose_name=_("Tên tài khoản thuế nợ")
    )
    tk_du = models.ForeignKey(
        'django_ledger.AccountModel',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='phieu_chi_thue_tk_du',
        verbose_name=_("Tài khoản dư"),
    )
    ten_tk_du = models.CharField(
        max_length=100, null=True, blank=True, verbose_name=_("Tên tài khoản dư")
    )
    # Tax amount information
    t_thue_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_("Tổng thuế ngoại tệ"),
        null=True,
        blank=True,
    )
    t_thue = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_("Tổng thuế"),
        null=True,
        blank=True,
    )

    # Additional information
    ma_kh9 = models.CharField(
        max_length=50, null=True, blank=True, verbose_name=_("Mã khách hàng 9")
    )
    ten_kh9 = models.CharField(
        max_length=100, null=True, blank=True, verbose_name=_("Tên khách hàng 9")
    )
    ma_tt = models.CharField(
        max_length=10, null=True, blank=True, verbose_name=_("Mã thanh toán")
    )
    ten_tt = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=_("Tên điều khoản thanh toán"),
    )
    ghi_chu = models.CharField(
        max_length=255, null=True, blank=True, verbose_name=_("Ghi chú")
    )
    id_tt = models.IntegerField(null=True, blank=True, verbose_name=_("ID thanh toán"))
    # Analysis codes - Foreign Key relationships
    ma_bp = models.ForeignKey(
        "django_ledger.BoPhanModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã bộ phận"),
    )
    ma_vv = models.ForeignKey(
        "django_ledger.VuViecModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã vụ việc"),
    )
    ma_hd = models.ForeignKey(
        "django_ledger.ContractModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="phieu_chi_thue_ma_hd",
        verbose_name=_("Mã hợp đồng"),
    )
    ma_dtt = models.ForeignKey(
        "django_ledger.DotThanhToanModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã đợt thanh toán"),
    )
    ma_ku = models.ForeignKey(
        "django_ledger.KheUocModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã khế ước"),
    )
    ma_phi = models.ForeignKey(
        "django_ledger.PhiModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã phí"),
    )
    ma_sp = models.ForeignKey(
        "django_ledger.VatTuModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã sản phẩm"),
    )
    ma_lsx = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Mã lệnh sản xuất"),
    )
    ma_cp0 = models.ForeignKey(
        "django_ledger.ChiPhiKhongHopLeModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã chi phí"),
    )

    objects = PhieuChiThueModelManager.from_queryset(PhieuChiThueModelQueryset)()

    class Meta:
        abstract = True
        verbose_name = _("Thuế Phiếu Chi")
        verbose_name_plural = _("Thuế Phiếu Chi")
        indexes = [
            models.Index(fields=["phieu_chi"]),
            models.Index(fields=["ma_kh"]),
            models.Index(fields=["ma_thue"]),
        ]

    def __str__(self):  # noqa: C901
        return f"{self.phieu_chi} - {self.line}: {self.ma_thue or ''}"


class PhieuChiThueModel(PhieuChiThueModelAbstract):
    """
    Base PhieuChiThue Model Implementation
    """

    class Meta(PhieuChiThueModelAbstract.Meta):
        abstract = False
        db_table = "phieu_chi_thue"
