"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietPhuTungKemTheoTSCD (Fixed Asset Accessory Detail) Model
"""

import uuid as uuid_lib  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ChiTietPhuTungKemTheoTSCDModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the ChiTietPhuTungKemTheoTSCDModel.
    """

    pass


class ChiTietPhuTungKemTheoTSCDModelManager(Manager):
    """
    A custom defined Manager for the ChiTietPhuTungKemTheoTSCDModel.
    """

    def for_entity(self, entity_slug, user_model):  # noqa: C901
        """
        Returns a QuerySet of ChiTietPhuTungKemTheoTSCDModel for a specific EntityModel.
        """
        qs = self.get_queryset()
        return qs.filter(
            khai_bao_thong_tin_tai_san_co_dinh__entity_model__slug__exact=entity_slug,
            khai_bao_thong_tin_tai_san_co_dinh__entity_model__admin=user_model,
        )


class ChiTietPhuTungKemTheoTSCDModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiTietPhuTungKemTheoTSCDModel database will inherit from.  # noqa: E501
    The ChiTietPhuTungKemTheoTSCDModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid_lib.uuid4().  # noqa: E501

    khai_bao_thong_tin_tai_san_co_dinh : KhaiBaoThongTinTaiSanCoDinhModel
        The fixed asset information declaration that this detail belongs to.

    line : int
        The line number.

    ten_ptts : str
        The accessory name.

    dvt : DonViTinh
        The unit of measure.

    so_luong : decimal
        The quantity.

    gia_tri_nt : decimal
        The value in foreign currency.

    gia_tri : decimal
        The value.
    """

    uuid = models.UUIDField(
        primary_key=True,
        default=uuid_lib.uuid4,
        editable=False,
        verbose_name=_("UUID"),
    )
    khai_bao_thong_tin_tai_san_co_dinh = models.ForeignKey(
        'django_ledger.KhaiBaoThongTinTaiSanCoDinhModel',
        on_delete=models.CASCADE,
        verbose_name=_('Khai báo thông tin tài sản cố định'),
        related_name='chi_tiet_phu_tung_kem_theo',
    )
    line = models.IntegerField(null=True, blank=True, verbose_name=_("Dòng"))
    ten_ptts = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Tên phụ tùng tài sản"),
    )
    dvt = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Đơn vị tính"),
    )
   
    so_luong = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Số lượng"),
    )
    gia_tri_nt = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Giá trị NT"),
    )
    gia_tri = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Giá trị"),
    )

    objects = ChiTietPhuTungKemTheoTSCDModelManager.from_queryset(
        ChiTietPhuTungKemTheoTSCDModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _('Chi tiết phụ tùng kèm theo TSCĐ')
        verbose_name_plural = _('Chi tiết phụ tùng kèm theo TSCĐ')
        indexes = [
            models.Index(fields=['khai_bao_thong_tin_tai_san_co_dinh']),
            models.Index(fields=['line']),
        ]

    def __str__(self):  # noqa: C901
        return (
            f'{self.khai_bao_thong_tin_tai_san_co_dinh.ma_ts} - {self.ten_ptts}'
        )


class ChiTietPhuTungKemTheoTSCDModel(ChiTietPhuTungKemTheoTSCDModelAbstract):
    """
    Base Fixed Asset Accessory Detail Model Implementation
    """

    class Meta(ChiTietPhuTungKemTheoTSCDModelAbstract.Meta):
        abstract = False
        db_table = 'django_ledger_chi_tiet_phu_tung_kem_theo_tscd'
