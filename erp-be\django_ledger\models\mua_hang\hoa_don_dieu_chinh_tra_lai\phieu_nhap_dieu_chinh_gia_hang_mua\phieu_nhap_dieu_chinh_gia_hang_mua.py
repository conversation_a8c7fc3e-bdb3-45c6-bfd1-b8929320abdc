"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the PhieuNhapDieuChinhGiaHangMuaModel, which represents the purchase price adjustment receipt  # noqa: E501
in the system.
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models._mixins.chung_tu_mixins import ChungTuMixIn  # noqa: F401,
from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class PhieuNhapDieuChinhGiaHangMuaModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the PhieuNhapDieuChinhGiaHangMuaModel.
    """

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Returns PhieuNhapDieuChinhGiaHangMuaModel for a specific entity.

        Parameters
        ----------
        entity_slug: str
            The entity slug to filter by.

        Returns
        -------
        PhieuNhapDieuChinhGiaHangMuaModelQueryset
            A QuerySet of PhieuNhapDieuChinhGiaHangMuaModel with applied filters.
        """
        return self.filter(entity_model__slug=entity_slug)


class PhieuNhapDieuChinhGiaHangMuaModelManager(Manager):
    """
    A custom defined PhieuNhapDieuChinhGiaHangMuaModel Manager that will act as an interface to handle the  # noqa: E501
    PhieuNhapDieuChinhGiaHangMuaModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom PhieuNhapDieuChinhGiaHangMuaModelQueryset.
        """
        return PhieuNhapDieuChinhGiaHangMuaModelQueryset(self.model, using=self._db)

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Returns PhieuNhapDieuChinhGiaHangMuaModel for a specific entity.

        Parameters
        ----------
        entity_slug: str
            The entity slug to filter by.

        Returns
        -------
        PhieuNhapDieuChinhGiaHangMuaModelQueryset
            A QuerySet of PhieuNhapDieuChinhGiaHangMuaModel with applied filters.
        """
        return self.get_queryset().for_entity(entity_slug=entity_slug)


class PhieuNhapDieuChinhGiaHangMuaModelAbstract(ChungTuMixIn, CreateUpdateMixIn):
    """
    This is the main abstract class which the PhieuNhapDieuChinhGiaHangMuaModel database will inherit from.  # noqa: E501
    The PhieuNhapDieuChinhGiaHangMuaModel inherits functionality from the following MixIns:  # noqa: E501

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    entity_model : ForeignKey
        The EntityModel this record belongs to.
    ma_ngv : CharField
        Employee code.
    ma_kh : ForeignKey
        Customer code.
    tk : ForeignKey
        Account code (non-nullable).
    ma_tt : ForeignKey
        Payment term code.
    unit_id : ForeignKey
        Unit ID.
    ma_nt : ForeignKey
        Currency code.
    ma_bp : ForeignKey
        Department code.
    ma_vv : ForeignKey
        Case code.
    ma_hd : ForeignKey
        Contract code.
    ma_dtt : ForeignKey
        Payment batch code.
    ma_ku : ForeignKey
        Loan agreement code.
    ma_phi : ForeignKey
        Fee code.
    ma_sp : ForeignKey
        Product code.
    ma_cp0 : ForeignKey
        Invalid expense code.
    ma_nk : ForeignKey
        Document permission code.
    so_ct0 : ForeignKey
        Document number 0.
    so_ct2 : ForeignKey
        Document number 2.
    so_ct : ForeignKey
        Document number.
    dien_giai : TextField
        Description.
    xprogress : CharField
        Progress.
    i_so_ct : CharField
        Internal document number.
    xdatetime2 : CharField
        Datetime 2.
    status : CharField
        Status.
    xfile : CharField
        File attachment.
    id_progress : IntegerField
        Progress ID.
    ngay_ct : DateField
        Document date.
    ngay_lct : DateField
        Document creation date.
    ngay_ct0 : DateField
        Document date 0.
    ty_gia : DecimalField
        Exchange rate.
    t_so_luong : DecimalField
        Total quantity.
    t_tien_nt : DecimalField
        Total amount in foreign currency.
    t_tien : DecimalField
        Total amount.
    t_thue_nt : DecimalField
        Total tax in foreign currency.
    t_thue : DecimalField
        Total tax.
    t_tt_nt : DecimalField
        Total payment in foreign currency.
    t_tt : DecimalField
        Total payment.
    transfer_yn : BooleanField
        Transfer status.
    """

    uuid = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    # Entity reference (required)
    entity_model = models.ForeignKey(
        'django_ledger.EntityModel',
        on_delete=models.CASCADE,
        verbose_name=_('Entity'),
        help_text=_('The entity this purchase price adjustment receipt belongs to'),
    )

    # Foreign keys according to ERP conventions
    ma_ngv = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Mã nhân viên'),
        help_text=_('Employee code'),
    )

    ma_kh = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Mã khách hàng'),
        help_text=_('Customer code'),
    )

    tk = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('Tài khoản'),
        help_text=_('Account code (required)'),
    )

    ma_tt = models.ForeignKey(
        'django_ledger.HanThanhToanModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã thanh toán'),
        help_text=_('Payment term code'),
    )

    unit_id = models.ForeignKey(
        'django_ledger.EntityUnitModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Đơn vị'),
        help_text=_('Unit ID'),
    )

    ma_nt = models.ForeignKey(
        'django_ledger.NgoaiTeModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Mã ngoại tệ'),
        help_text=_('Currency code'),
    )

    ma_bp = models.ForeignKey(
        'django_ledger.BoPhanModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã bộ phận'),
        help_text=_('Department code'),
    )

    ma_vv = models.ForeignKey(
        'django_ledger.VuViecModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã vụ việc'),
        help_text=_('Case code'),
    )

    ma_hd = models.ForeignKey(
        'django_ledger.ContractModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã hợp đồng'),
        help_text=_('Contract code'),
    )

    ma_dtt = models.ForeignKey(
        'django_ledger.DotThanhToanModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã đợt thanh toán'),
        help_text=_('Payment batch code'),
    )

    ma_ku = models.ForeignKey(
        'django_ledger.KheUocModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã khe ước'),
        help_text=_('Loan agreement code'),
    )

    ma_phi = models.ForeignKey(
        'django_ledger.PhiModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã phí'),
        help_text=_('Fee code'),
    )

    ma_sp = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã sản phẩm'),
        help_text=_('Product/Material code'),
    )

    ma_cp0 = models.ForeignKey(
        'django_ledger.ChiPhiKhongHopLeModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã chi phí không hợp lệ'),
        help_text=_('Invalid expense code'),
    )

    ma_nk = models.ForeignKey(
        'django_ledger.QuyenChungTu',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã quyển chứng từ'),
        help_text=_('Document permission code'),
    )

    # i_so_ct, ma_nk and so_ct fields are provided by ChungTuMixIn
    # ngay_ct and ngay_lct fields are provided by ChungTuMixIn

    so_ct0 = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Số chứng từ 0'),
        help_text=_('Document number 0'),
    )

    so_ct2 = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Số chứng từ 2'),
        help_text=_('Document number 2'),
    )

    # Text fields
    dien_giai = models.TextField(
        blank=True,
        verbose_name=_('Diễn giải'),
        help_text=_('Description or explanation'),
    )

    xprogress = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_('Tiến trình'),
        help_text=_('Progress information'),
    )

    # ✅ REMOVED: i_so_ct field - handled by ChungTuMixIn property
    # i_so_ct field is managed by ChungTuMixIn through chung_tu_item relationship
    # This prevents conflict between model field and property

    xdatetime2 = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_('Thời gian 2'),
        help_text=_('Secondary datetime information'),
    )

    status = models.CharField(
        max_length=10,
        verbose_name=_('Trạng thái'),
        help_text=_('Status of the document'),
    )

    xfile = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_('File đính kèm'),
        help_text=_('Attached file path'),
    )

    # Integer fields
    id_progress = models.IntegerField(
        default=0,
        verbose_name=_('ID tiến trình'),
        help_text=_('Progress ID'),
    )

    # ✅ REMOVED: ngay_ct and ngay_lct fields - handled by ChungTuMixIn properties
    # ngay_ct and ngay_lct fields are managed by ChungTuMixIn through chung_tu_item relationship
    # This prevents conflict between model fields and properties

    # Date fields
    ngay_ct0 = models.DateField(
        verbose_name=_('Ngày chứng từ 0'), help_text=_('Document date 0')
    )

    # Decimal fields
    ty_gia = models.DecimalField(
        max_digits=15,
        decimal_places=6,
        default=1,
        verbose_name=_('Tỷ giá'),
        help_text=_('Exchange rate'),
    )

    t_so_luong = models.DecimalField(
        max_digits=15,
        decimal_places=3,
        verbose_name=_('Tổng số lượng'),
        help_text=_('Total quantity'),
    )

    t_tien_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng tiền ngoại tệ'),
        help_text=_('Total amount in foreign currency'),
    )

    t_tien = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng tiền'),
        help_text=_('Total amount'),
    )

    t_thue_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng thuế ngoại tệ'),
        help_text=_('Total tax in foreign currency'),
    )

    t_thue = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng thuế'),
        help_text=_('Total tax'),
    )

    t_tt_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng thanh toán ngoại tệ'),
        help_text=_('Total payment in foreign currency'),
    )

    t_tt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng thanh toán'),
        help_text=_('Total payment'),
    )

    # Boolean fields
    transfer_yn = models.BooleanField(
        default=False,
        verbose_name=_('Đã chuyển khoản'),
        help_text=_('Transfer status'),
    )

    # ✅ ENHANCED: Ledger relationship for 1:1 mapping with phieu nhap
    # Following pattern from HoaDonDieuChinhGiaHangBanModel for unified accounting
    ledger = models.OneToOneField(
        'django_ledger.LedgerModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_("Sổ cái"),
        help_text=_("Sổ cái được tạo cho phiếu nhập điều chỉnh giá này"),
        related_name="phieu_nhap_dieu_chinh_gia_hang_mua",
    )

    objects = PhieuNhapDieuChinhGiaHangMuaModelManager()

    class Meta:
        abstract = True
        verbose_name = _('Phiếu nhập điều chỉnh giá hàng mua')
        verbose_name_plural = _('Phiếu nhập điều chỉnh giá hàng mua')
        indexes = [
            models.Index(fields=['entity_model']),
            models.Index(fields=['ma_kh']),
            models.Index(fields=['tk']),
            models.Index(fields=['ma_nt']),
            models.Index(fields=['status']),
            models.Index(fields=['ledger']),
            # ma_nk, so_ct, ngay_ct indexes are handled by ChungTuMixIn
        ]
        ordering = [
            '-created'
        ]  # ✅ FIXED: Removed ngay_ct reference (handled by ChungTuMixIn)

    def __str__(self):  # noqa: C901
        # ✅ FIXED: Use property access for i_so_ct (handled by ChungTuMixIn)
        i_so_ct = getattr(self, 'i_so_ct', 'N/A')
        return f'{i_so_ct} - {self.dien_giai or "Phiếu nhập điều chỉnh giá"}'


class PhieuNhapDieuChinhGiaHangMuaModel(PhieuNhapDieuChinhGiaHangMuaModelAbstract):
    """
    Base PhieuNhapDieuChinhGiaHangMuaModel Implementation
    """

    class Meta(PhieuNhapDieuChinhGiaHangMuaModelAbstract.Meta):
        abstract = False
        db_table = 'phieu_nhap_dieu_chinh_gia_hang_mua'
