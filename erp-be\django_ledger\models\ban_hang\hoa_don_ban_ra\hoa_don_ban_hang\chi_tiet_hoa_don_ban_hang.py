"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Chi Tiet Hoa Don Ban Hang (Sales Invoice Detail) model implementation.
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ChiTietHoaDonBanHangModelQueryset(QuerySet):
    """
    A custom defined ChiTietHoaDonBanHangModel QuerySet.
    """

    def active(self):  # noqa: C901
        """
        Returns active ChiTietHoaDonBanHangModel instances.
        """
        return self.filter(status='1')

    def inactive(self):  # noqa: C901
        """
        Returns inactive ChiTietHoaDonBanHangModel instances.
        """
        return self.exclude(status='1')


class ChiTietHoaDonB<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(Manager):
    """
    A custom defined ChiTietHoaDonBanHangModel Manager that will act as an interface to handle the  # noqa: E501
    ChiTietHoaDonBanHangModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom ChiTietHoaDonBanHangModelQueryset.
        """
        return ChiTietHoaDonBanHangModelQueryset(self.model, using=self._db)

    def for_hoa_don(self, hoa_don_id):  # noqa: C901
        """
        Returns a QuerySet of ChiTietHoaDonBanHangModel associated with a specific HoaDonBanHangModel.  # noqa: E501

        Parameters
        ----------
        hoa_don_id: UUID
            The HoaDonBanHangModel UUID used for filtering the QuerySet.

        Returns
        -------
        ChiTietHoaDonBanHangModelQueryset
            A QuerySet of ChiTietHoaDonBanHangModel with applied filters.
        """
        qs = self.get_queryset()
        return qs.filter(hoa_don_ban_hang_id=hoa_don_id)


class ChiTietHoaDonBanHangModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiTietHoaDonBanHangModel database will inherit from.  # noqa: E501
    The ChiTietHoaDonBanHangModel inherits functionality from the CreateUpdateMixIn.

    Attributes
    ----------
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    hoa_don_ban_hang : HoaDonBanHangModel
        The HoaDonBanHangModel this detail belongs to.
    line : int
        Line number in the invoice.
    ma_vt : str
        Material code.
    ten_vt : str
        Material name.
    dvt : str
        Unit of measure.
    ma_kho : str
        Warehouse code.
    ma_lo : str
        Batch code.
    so_luong : float
        Quantity.
    gia_nt2 : float
        Price in foreign currency.
    gia2 : float
        Price.
    tien_nt2 : float
        Amount in foreign currency.
    tien2 : float
        Amount.
    ck_tl : float
        Discount percentage.
    ck_nt : float
        Discount amount in foreign currency.
    ck : float
        Discount amount.
    thue_suat : float
        Tax rate.
    thue_nt : float
        Tax amount in foreign currency.
    thue : float
        Tax amount.
    tt_nt : float
        Total amount in foreign currency.
    tt : float
        Total amount.
    ma_thue : str
        Tax code.
    tk_dt : str
        Revenue account.
    tk_gv : str
        Cost account.
    tk_vt : str
        Inventory account.
    tk_ck : str
        Discount account.
    tk_thue_no : str
        Tax payable account.
    status : str
        Status.
    """

    uuid = models.UUIDField(default=uuid4, editable=False, primary_key=True)
    hoa_don_ban_hang = models.ForeignKey(
        'django_ledger.HoaDonBanHangModel',
        on_delete=models.CASCADE,
        verbose_name=_('Hóa Đơn Bán Hàng'),
        related_name='chi_tiet',
    )

    # Line information
    line = models.IntegerField(null=True, blank=True)
    ma_vt = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã vật tư'),
        related_name='chi_tiet_hoa_don_ban_hang',
        null=True,
        blank=True,
    )
    ten_vt0 = models.CharField(max_length=255, null=True, blank=True)
    dvt = models.ForeignKey(
        'django_ledger.DonViTinhModel',
        on_delete=models.CASCADE,
        verbose_name=_('Đơn vị tính'),
        related_name='chi_tiet_hoa_don_ban_hang',
        null=True,
        blank=True,
    )
    ten_dvt = models.CharField(max_length=255, null=True, blank=True)
    ma_kho = models.ForeignKey(
        'django_ledger.KhoHangModel',
        on_delete=models.CASCADE,
        verbose_name=_('Kho'),
        related_name='chi_tiet_hoa_don_ban_hang',
        null=True,
        blank=True,
    )
    ten_kho = models.CharField(max_length=255, null=True, blank=True)
    ma_lo = models.ForeignKey(
        'django_ledger.LoModel',
        on_delete=models.CASCADE,
        verbose_name=_('Lô'),
        related_name='chi_tiet_hoa_don_ban_hang',
        null=True,
        blank=True,
    )
    ten_lo = models.CharField(max_length=255, null=True, blank=True)
    lo_yn = models.BooleanField(default=False)
    ma_vi_tri = models.ForeignKey(
        'django_ledger.ViTriKhoHangModel',
        on_delete=models.CASCADE,
        verbose_name=_('Vị trí kho hàng'),
        related_name='chi_tiet_hoa_don_ban_hang',
        null=True,
        blank=True,
    )
    ten_vi_tri = models.CharField(max_length=255, null=True, blank=True)
    vi_tri_yn = models.BooleanField(default=False)
    ma_lvt = models.CharField(max_length=50, null=True, blank=True)
    he_so = models.FloatField(null=True, blank=True)
    qc_yn = models.BooleanField(default=False)
    ct_km = models.BooleanField(default=False)
    ma_loai_gb = models.CharField(max_length=50, null=True, blank=True)
    v_dvt_goc = models.CharField(max_length=50, null=True, blank=True)
    v_sl_goc = models.FloatField(null=True, blank=True)
    so_luong = models.FloatField(null=True, blank=True)
    gia_nt1 = models.FloatField(null=True, blank=True)
    gia_nt2 = models.FloatField(null=True, blank=True)
    tien_nt2 = models.FloatField(null=True, blank=True)
    tl_ck = models.FloatField(null=True, blank=True)
    ck_nt = models.FloatField(null=True, blank=True)
    px_dd = models.BooleanField(default=False)
    gia_nt = models.FloatField(null=True, blank=True)
    tien_nt = models.FloatField(null=True, blank=True)
    ma_thue = models.ForeignKey(
        'django_ledger.TaxModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã thuế'),
        help_text=_('Tax reference'),
        related_name='chi_tiet_hoa_don_ban_hang_ma_thue',
    )
    thue_suat = models.FloatField(null=True, blank=True)
    tk_thue_co = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('TK thuế có'),
        related_name='chi_tiet_hoa_don_ban_hang_tk_thue_co',
        null=True,
        blank=True,
    )
    ten_tk_thue_co = models.CharField(max_length=255, null=True, blank=True)
    thue_nt = models.FloatField(null=True, blank=True)
    tk_dt = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('TK doanh thu'),
        related_name='chi_tiet_hoa_don_ban_hang_tk_dt',
        null=True,
        blank=True,
    )
    ten_tk_dt = models.CharField(max_length=255, null=True, blank=True)
    tk_gv = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('TK giá vốn'),
        related_name='chi_tiet_hoa_don_ban_hang_tk_gv',
        null=True,
        blank=True,
    )
    ten_tk_gv = models.CharField(max_length=255, null=True, blank=True)
    tk_vt = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('TK vật tư'),
        related_name='chi_tiet_hoa_don_ban_hang_tk_vt',
        null=True,
        blank=True,
    )
    ten_tk_vt = models.CharField(max_length=255, null=True, blank=True)
    tk_ck = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('TK chiết khấu'),
        related_name='chi_tiet_hoa_don_ban_hang_tk_ck',
        null=True,
        blank=True,
    )
    ten_tk_ck = models.CharField(max_length=255, null=True, blank=True)
    tk_km = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('TK khuyến mãi'),
        related_name='chi_tiet_hoa_don_ban_hang_tk_km',
        null=True,
        blank=True,
    )
    ten_tk_km = models.CharField(max_length=255, null=True, blank=True)
    ghi_chu = models.CharField(max_length=255, null=True, blank=True)
    ma_bp = models.ForeignKey(
        'django_ledger.BoPhanModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã bộ phận'),
        related_name='chi_tiet_hoa_don_ban_hang_ma_bp',
        null=True,
        blank=True,
    )
    ten_bp = models.CharField(max_length=50, null=True, blank=True)
    ma_vv = models.ForeignKey(
        'django_ledger.VuViecModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã vụ việc'),
        related_name='chi_tiet_hoa_don_ban_hang_ma_vv',
        null=True,
        blank=True,
    )
    ten_vv = models.CharField(max_length=50, null=True, blank=True)
    ma_hd = models.ForeignKey(
        'django_ledger.ContractModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã hợp đồng'),
        related_name='chi_tiet_hoa_don_ban_hang_ma_hd',
        null=True,
        blank=True,
    )
    ten_hd = models.CharField(max_length=50, null=True, blank=True)
    ma_dtt = models.ForeignKey(
        'django_ledger.DotThanhToanModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã đợt thanh toán'),
        related_name='chi_tiet_hoa_don_ban_hang_ma_dtt',
        null=True,
        blank=True,
    )
    ten_dtt = models.CharField(max_length=50, null=True, blank=True)
    ma_ku = models.ForeignKey(
        'django_ledger.KheUocModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã khế ước'),
        related_name='chi_tiet_hoa_don_ban_hang_ma_ku',
        null=True,
        blank=True,
    )
    ma_phi = models.ForeignKey(
        'django_ledger.PhiModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã phí'),
        related_name='chi_tiet_hoa_don_ban_hang_ma_phi',
        null=True,
        blank=True,
    )
    ten_phi = models.CharField(max_length=50, null=True, blank=True)
    ma_sp = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã sản phẩm'),
        related_name='chi_tiet_hoa_don_ban_hang_ma_sp',
        null=True,
        blank=True,
    )
    ten_sp = models.CharField(max_length=50, null=True, blank=True)
    ma_lsx = models.CharField(max_length=50, null=True, blank=True)
    ma_cp0 = models.ForeignKey(
        'django_ledger.ChiPhiKhongHopLeModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã công trình'),
        related_name='chi_tiet_hoa_don_ban_hang_ma_cp0',
        null=True,
        blank=True,
    )
    ten_cp0 = models.CharField(max_length=50, null=True, blank=True)
    gia = models.FloatField(null=True, blank=True)
    tien = models.FloatField(null=True, blank=True)
    gia1 = models.FloatField(null=True, blank=True)
    gia2 = models.FloatField(null=True, blank=True)
    tien2 = models.FloatField(null=True, blank=True)
    ck = models.FloatField(null=True, blank=True)
    thue = models.FloatField(null=True, blank=True)
    sl_px = models.IntegerField(null=True, blank=True)
    id_px = models.IntegerField(null=True, blank=True)
    line_px = models.IntegerField(null=True, blank=True)
    id_dh = models.CharField(
        max_length=36,
        verbose_name=_('ID đơn hàng'),
        help_text=_('UUID of DonHangModel'),
        blank=True,
        null=True,
    )
    line_dh = models.CharField(
        max_length=36,
        verbose_name=_('Line chi tiết đơn hàng'),
        help_text=_('UUID of ChiTietDonHangModel'),
        blank=True,
        null=True,
    )
    id_hd = models.IntegerField(null=True, blank=True)
    line_hd = models.IntegerField(null=True, blank=True)
    id_nhap = models.IntegerField(null=True, blank=True)
    line_nhap = models.IntegerField(null=True, blank=True)
    sl_cl = models.IntegerField(default=0, null=True, blank=True)
    objects = ChiTietHoaDonBanHangModelManager()

    class Meta:
        abstract = True
        verbose_name = _('Chi Tiết Hóa Đơn Bán Hàng')
        verbose_name_plural = _('Chi Tiết Hóa Đơn Bán Hàng')
        indexes = [
            models.Index(fields=['hoa_don_ban_hang']),
            models.Index(fields=['ma_vt']),
        ]
        ordering = ['line']

    def __str__(self):  # noqa: C901
        return f"{self.hoa_don_ban_hang} - {self.line} - {self.ma_vt} - {self.ten_vt0}"

    def clean(self):  # noqa: C901
        """
        Custom validation for ChiTietHoaDonBanHangModel.
        """
        super().clean()

        # Calculate amounts only if values are not None
        if self.so_luong and self.gia_nt2:
            self.tien_nt2 = self.so_luong * self.gia_nt2
        if self.so_luong and self.gia2:
            self.tien2 = self.so_luong * self.gia2
        # Calculate discount
        if self.tien_nt2 and self.tl_ck:
            self.ck_nt = self.tien_nt2 * (self.tl_ck / 100)
        if self.tien2 and self.tl_ck:
            self.ck = self.tien2 * (self.tl_ck / 100)
        # Calculate tax
        if self.tien_nt2 and self.ck_nt and self.thue_suat:
            self.thue_nt = (self.tien_nt2 - self.ck_nt) * (self.thue_suat / 100)
        if self.tien2 and self.ck and self.thue_suat:
            self.thue = (self.tien2 - self.ck) * (self.thue_suat / 100)
        # Calculate total
        if self.tien_nt2 and self.ck_nt and self.thue_nt:
            self.tt_nt = self.tien_nt2 - self.ck_nt + self.thue_nt
        if self.tien2 and self.ck and self.thue:
            self.tt = self.tien2 - self.ck + self.thue


class ChiTietHoaDonBanHangModel(ChiTietHoaDonBanHangModelAbstract):
    """
    Base ChiTietHoaDonBanHangModel from Abstract.
    """

    class Meta(ChiTietHoaDonBanHangModelAbstract.Meta):
        abstract = False
        db_table = "chi_tiet_hoa_don_ban_hang"
