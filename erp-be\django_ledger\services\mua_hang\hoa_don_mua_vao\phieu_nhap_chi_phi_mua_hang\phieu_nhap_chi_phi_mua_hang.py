"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the PhieuNhapChiPhiMuaHangService, which handles business logic
for the PhieuNhapChiPhiMuaHangModel.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID
from uuid import UUID  # noqa: F401

from django.db import transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import (  # noqa: F401,
    PhieuNhapChiPhiMuaHangModel,
)
from django_ledger.repositories.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import (  # noqa: F401,
    PhieuNhapChiPhiMuaHangRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,
from django_ledger.services.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang.chi_phi_chi_tiet_phieu_nhap_chi_phi_mua_hang import (  # noqa: F401,
    ChiPhiChiTietPhieuNhapChiPhiMuahangService,
)
from django_ledger.services.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang.chi_phi_phieu_nhap_chi_phi_mua_hang import (  # noqa: F401,
    ChiPhiPhieuNhapChiPhiMuaHangService,
)
from django_ledger.services.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang.chi_tiet_phieu_nhap_chi_phi_mua_hang import (  # noqa: F401,
    ChiTietPhieuNhapChiPhiMuaHangService,
)
from django_ledger.services.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang.thue_phieu_nhap_chi_phi_mua_hang import (  # noqa: F401,
    ThuePhieuNhapChiPhiMuaHangService,
)
from django_ledger.utils_new.debt_management.cong_no_creation import (  # noqa: F401,
    CongNoCreation,
)


class PhieuNhapChiPhiMuaHangService(BaseService):
    """
    Service class for PhieuNhapChiPhiMuaHangModel.
    Handles business logic for the model.

    ✅ ENHANCED: Integrated with Unified Accounting Service
    ERP Expert - 20 năm kinh nghiệm
    """

    # ✅ PREDEFINED CONFIGURATION: Purchase expense accounting mappings
    # CONGNO and THUE journal types with conditional logic
    PURCHASE_EXPENSE_ACCOUNTING_CONFIG = [
        # CONGNO: Chi phí mua hàng
        {
            'journal_type': 'CONGNO',
            'debit_account_field': 'tk_vt',  # Detail account - DEBIT
            'credit_account_field': 'tk',  # Header account - CREDIT
            'debit_account_source': 'detail',  # Lấy debit từ detail
            'credit_account_source': 'header',  # Lấy credit từ header
            'amount_field': 'cp_nt',  # Chi phí amount
            'detail_source': 'chi_tiet_phieu_nhaps',  # Related name to detail
            'detail_conditions': {  # ✅ ENHANCED: New format for CongNoCreation service
                'cp_nt': {'gt': 0},  # cp_nt > 0
                'tk_vt': {'is_not_null': True},  # tk_vt not null
            },
        },
        # THUE: Thuế
        {
            'journal_type': 'THUE',
            'debit_account_field': 'tk_thue_no',  # Detail tax account - DEBIT
            'credit_account_field': 'tk',  # Header account - CREDIT
            'debit_account_source': 'detail',  # Lấy debit từ detail
            'credit_account_source': 'header',  # Lấy credit từ header
            'amount_field': 't_thue_nt',  # Tax amount
            'detail_source': 'thue_phieu_nhaps',  # Related name to tax detail
            'detail_conditions': {  # ✅ ENHANCED: New format for CongNoCreation service
                't_thue_nt': {'gt': 0},  # thue > 0
                'tk_thue_no': {'is_not_null': True},  # tk_thue_no not null
            },
        },
    ]

    def __init__(self):  # noqa: C901
        """
        Initialize the service with the PhieuNhapChiPhiMuaHangRepository and related services.
        """
        super().__init__()
        self.repository = PhieuNhapChiPhiMuaHangRepository()
        self.chi_tiet_service = ChiTietPhieuNhapChiPhiMuaHangService()
        self.chi_phi_service = ChiPhiPhieuNhapChiPhiMuaHangService()
        self.chi_phi_chi_tiet_service = ChiPhiChiTietPhieuNhapChiPhiMuahangService()
        self.thue_service = ThuePhieuNhapChiPhiMuaHangService()

        # ✅ ĐƠN GIẢN: Khởi tạo unified accounting service
        self._cong_no_service = CongNoCreation()

    def _determine_accounting_mappings(
        self, phieu_nhap: PhieuNhapChiPhiMuaHangModel
    ) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định accounting mappings dựa trên status và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Conditional accounting entry creation dựa trên document status
        - CONGNO: cp_nt > 0, tk_vt not null
        - THUE: thue > 0, tk_thue_no not null
        - Status-based creation (chỉ tạo khi status IN ('3', '5'))
        - Support flexible business rules cho different scenarios

        Parameters
        ----------
        phieu_nhap : PhieuNhapChiPhiMuaHangModel
            Phiếu nhập chi phí mua hàng để analyze

        Returns
        -------
        List[Dict[str, Any]]
            Danh sách accounting mappings với canCreate được set theo business logic
        """
        # Get base configuration
        mappings = self.PURCHASE_EXPENSE_ACCOUNTING_CONFIG.copy()

        # ✅ BUSINESS LOGIC: Determine canCreate based on status and business rules
        # Business logic - tạo bút toán khi status = '3' hoặc status = '5'
        if hasattr(phieu_nhap, 'status'):
            status = getattr(phieu_nhap, 'status', '1')  # Default status

            if status in ['3', '5']:
                # Status 3 or 5 - tạo bút toán
                for mapping in mappings:
                    mapping['canCreate'] = True
            else:
                # Other status - không tạo bút toán
                for mapping in mappings:
                    mapping['canCreate'] = False
        else:
            # No status field - default behavior (không tạo)
            for mapping in mappings:
                mapping['canCreate'] = False

        return mappings

    def get_accounting_configuration(self) -> List[Dict[str, Any]]:
        """
        Lấy cấu hình kế toán cho phiếu nhập chi phí mua hàng.

        Returns:
            List[Dict[str, Any]]: Danh sách mapping configuration
        """
        return self.PURCHASE_EXPENSE_ACCOUNTING_CONFIG.copy()

    def get_by_id(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> Optional[PhieuNhapChiPhiMuaHangModel]:  # noqa: C901
        """
        Retrieves a PhieuNhapChiPhiMuaHangModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuNhapChiPhiMuaHangModel to retrieve.

        Returns
        -------
        Optional[PhieuNhapChiPhiMuaHangModel]
            The PhieuNhapChiPhiMuaHangModel with the given UUID, or None if not found.
        """
        return self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

    def get_with_details(
        self, entity_slug: str, uuid: UUID
    ) -> Optional[PhieuNhapChiPhiMuaHangModel]:
        """
        Get a PhieuNhapChiPhiMuaHang with its related details.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : UUID
            The UUID of the PhieuNhapChiPhiMuaHang

        Returns
        -------
        Optional[PhieuNhapChiPhiMuaHangModel]
            The PhieuNhapChiPhiMuaHang instance with related details or None if not found
        """
        try:
            # Get PhieuNhapChiPhiMuaHang with prefetched chi_tiet and thue
            phieu_nhap = (
                PhieuNhapChiPhiMuaHangModel.objects.filter(
                    entity_model__slug=entity_slug
                )
                .prefetch_related('chi_tiet_phieu_nhaps', 'thue_phieu_nhaps')
                .get(uuid=uuid)
            )

            # Add chi_tiet and thue as list for compatibility
            phieu_nhap.chi_tiet_phieu_nhaps_list = list(
                phieu_nhap.chi_tiet_phieu_nhaps.all()
            )
            phieu_nhap.thue_phieu_nhaps_list = list(phieu_nhap.thue_phieu_nhaps.all())

            return phieu_nhap

        except PhieuNhapChiPhiMuaHangModel.DoesNotExist:
            return None

    def list(self, entity_slug: str, **kwargs) -> QuerySet:  # noqa: C901
        """
        Lists PhieuNhapChiPhiMuaHangModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of PhieuNhapChiPhiMuaHangModel instances.
        """
        return self.repository.list(entity_slug=entity_slug, **kwargs)

    @transaction.atomic
    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> PhieuNhapChiPhiMuaHangModel:  # noqa: C901
        """
        ✅ ENHANCED: Creates a new PhieuNhapChiPhiMuaHangModel instance with automatic accounting.

        Tự động tạo bút toán kế toán cho phiếu nhập chi phí mua hàng sử dụng unified services.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Atomic transaction cho cả accounting và document creation
        - Status-based business logic cho entry creation (3,5)
        - CONGNO và THUE conditional logic
        - Complete audit trail cho financial impacts
        - Transaction safety với automatic rollback

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new PhieuNhapChiPhiMuaHangModel.

        Returns
        -------
        PhieuNhapChiPhiMuaHangModel
            The created PhieuNhapChiPhiMuaHangModel instance.
        """
        # Process related data
        chi_tiet_phieu_nhaps = data.pop('chi_tiet_phieu_nhaps', [])
        chi_phi_phieu_nhaps = data.pop('chi_phi_phieu_nhaps', [])
        chi_phi_chi_tiet_phieu_nhaps = data.pop('chi_phi_chi_tiet_phieu_nhaps', [])
        thue_phieu_nhaps = data.pop('thue_phieu_nhaps', [])

        # No need to convert UUIDs - Django REST Framework handles this automatically
        # Create the PhieuNhapChiPhiMuaHangModel instance
        instance = self.repository.create(entity_slug=entity_slug, data=data)
        # Process related data
        if chi_tiet_phieu_nhaps:
            for chi_tiet_data in chi_tiet_phieu_nhaps:
                self.chi_tiet_service.create(
                    parent=instance, data=chi_tiet_data
                )

        if chi_phi_phieu_nhaps:
            for chi_phi_data in chi_phi_phieu_nhaps:
                self.chi_phi_service.create(
                    parent=instance, data=chi_phi_data
                )

        if chi_phi_chi_tiet_phieu_nhaps:
            for chi_phi_chi_tiet_data in chi_phi_chi_tiet_phieu_nhaps:
                self.chi_phi_chi_tiet_service.create(
                    parent=instance,
                    data=chi_phi_chi_tiet_data,
                )

        if thue_phieu_nhaps:
            for thue_data in thue_phieu_nhaps:
                self.thue_service.create(parent=instance, data=thue_data)

        # ✅ UNIFIED ACCOUNTING: Tạo bút toán kế toán
        # Only create accounting if ledger is not already assigned to avoid UNIQUE constraint
        if not instance.ledger:
            try:
                self._cong_no_service.create_document_accounting_entries(
                    source_document=instance,
                    document_type="phiếu nhập chi phí mua hàng",
                    account_mappings=self._determine_accounting_mappings(instance),
                )
                # Refresh instance to get updated ledger field
                instance.refresh_from_db()
            except Exception as e:
                # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                # to maintain data consistency between PhieuNhap and accounting entries
                raise Exception(
                    f"Failed to create accounting entry for PhieuNhapChiPhi {instance.so_ct}: {str(e)}"
                ) from e
        # Note: If ledger already exists, skip accounting creation to avoid duplicates

        return instance

    @transaction.atomic
    def create_phieu_nhap(
        self,
        entity_model,
        phieu_nhap_data: Dict[str, Any],
        chi_tiet_data=None,
        chi_phi_data=None,
        chi_phi_chi_tiet_data=None,
        thue_data=None,
    ) -> PhieuNhapChiPhiMuaHangModel:  # noqa: C901
        """
        Creates a new PhieuNhapChiPhiMuaHangModel instance with optional related data.
        Similar to HoaDonBanHangService.create_invoice.

        Parameters
        ----------
        entity_model : EntityModel
            The entity model to associate with the PhieuNhapChiPhiMuaHangModel.
        phieu_nhap_data : Dict[str, Any]
            Data for the PhieuNhapChiPhiMuaHangModel.
        chi_tiet_data : list, optional
            List of dictionaries containing data for ChiTietPhieuNhapChiPhiMuaHangModel instances.
        chi_phi_data : list, optional
            List of dictionaries containing data for ChiPhiPhieuNhapChiPhiMuaHangModel instances.
        chi_phi_chi_tiet_data : list, optional
            List of dictionaries containing data for ChiPhiChiTietPhieuNhapChiPhiMuaHangModel instances.
        thue_data : list, optional
            List of dictionaries containing data for ThuePhieuNhapChiPhiMuaHangModel instances.

        Returns
        -------
        PhieuNhapChiPhiMuaHangModel
            The created PhieuNhapChiPhiMuaHangModel instance.
        """
        # No need to convert UUIDs - Django REST Framework handles this automatically

        # Create the PhieuNhapChiPhiMuaHangModel instance
        instance = self.repository.create_with_entity(
            entity_model=entity_model, data=phieu_nhap_data
        )

        # Process related data
        if chi_tiet_data:
            for chi_tiet_item in chi_tiet_data:
                # Filter only valid fields for ChiTietPhieuNhapChiPhiMuaHangModel
                valid_fields = {
                    'line',
                    'ma_vt',
                    'dvt',
                    'ten_dvt',
                    'ma_kho',
                    'ten_kho',
                    'he_so',
                    'so_luong',
                    'tien0',
                    'cp_nt',
                    'tk_vt',
                    'ten_tk_vt',
                    'ma_bp',
                    'id_hd',
                    'line_hd',
                }
                filtered_data = {
                    k: v for k, v in chi_tiet_item.items() if k in valid_fields
                }

                self.chi_tiet_service.create(
                    parent=instance, data=filtered_data
                )

        if chi_phi_data:
            for chi_phi_item in chi_phi_data:
                # Filter only valid fields for ChiPhiPhieuNhapChiPhiMuaHangModel
                valid_fields = {'line', 'ma_cp', 'tien_cp_nt', 'tien_cp'}
                filtered_data = {
                    k: v for k, v in chi_phi_item.items() if k in valid_fields
                }

                self.chi_phi_service.create(
                    parent=instance, data=filtered_data
                )

        if chi_phi_chi_tiet_data:
            for chi_phi_chi_tiet_item in chi_phi_chi_tiet_data:
                # Filter only valid fields for ChiPhiChiTietPhieuNhapChiPhiMuahangModel
                valid_fields = {
                    'line',
                    'ma_cp',
                    'ma_vt',
                    'tien_cp_nt',
                    'tien_cp',
                    'line_vt',
                }
                filtered_data = {
                    k: v for k, v in chi_phi_chi_tiet_item.items() if k in valid_fields
                }

                self.chi_phi_chi_tiet_service.create(
                    parent=instance,
                    data=filtered_data,
                )

        if thue_data:
            for thue_item in thue_data:
                # Filter only valid fields for ThuePhieuNhapChiPhiMuaHangModel
                valid_fields = {
                    'line',
                    'so_ct0',
                    'so_ct2',
                    'ngay_ct0',
                    'ma_thue',
                    'thue_suat',
                    'ma_mau_ct',
                    'ma_mau_bc',
                    'ma_tc_thue',
                    'ma_kh',
                    'ten_kh_thue',
                    'dia_chi',
                    'ma_so_thue',
                    'ten_vt_thue',
                    't_tien_nt',
                    't_tien',
                    'tk_thue_no',
                    'ten_tk_thue_no',
                    'tk_du',
                    'ten_tk_du',
                    't_thue_nt',
                    't_thue',
                    'ma_kh9',
                    'ten_kh9',
                    'ma_tt',
                    'ten_tt',
                    'ghi_chu',
                    'id_tt',
                    'ma_bp',
                    'ma_vv',
                    'ma_hd',
                    'ma_dtt',
                    'ma_ku',
                    'ma_phi',
                    'ma_sp',
                    'ma_lsx',
                    'ma_cp0',
                }
                filtered_data = {
                    k: v for k, v in thue_item.items() if k in valid_fields
                }

                self.thue_service.create(
                    parent=instance, data=filtered_data
                )

        # ✅ UNIFIED ACCOUNTING: Tạo bút toán kế toán
        # Only create accounting if ledger is not already assigned to avoid UNIQUE constraint
        if not instance.ledger:
            try:
                self._cong_no_service.create_document_accounting_entries(
                    source_document=instance,
                    document_type="phiếu nhập chi phí mua hàng",
                    account_mappings=self._determine_accounting_mappings(instance),
                )
                # Refresh instance to get updated ledger field
                instance.refresh_from_db()
            except Exception as e:
                # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                # to maintain data consistency between PhieuNhap and accounting entries
                raise Exception(
                    f"Failed to create accounting entry for PhieuNhapChiPhi {instance.so_ct}: {str(e)}"
                ) from e
        # Note: If ledger already exists, skip accounting creation to avoid duplicates

        return instance



    def get_by_supplier(
        self, supplier_id: str, entity_slug: str
    ) -> QuerySet:  # noqa: C901
        """
        Gets PhieuNhapChiPhiMuaHangModel instances for a specific supplier.

        Parameters
        ----------
        supplier_id : str
            The supplier UUID.
        entity_slug : str
            The entity slug.

        Returns
        -------
        PhieuNhapChiPhiMuaHangModelQuerySet
            QuerySet of PhieuNhapChiPhiMuaHangModel instances.
        """
        return self.repository.get_by_supplier(
            supplier_id=supplier_id, entity_slug=entity_slug
        )

    def get_by_status(self, status: str, entity_slug: str) -> QuerySet:  # noqa: C901
        """
        Gets PhieuNhapChiPhiMuaHangModel instances with a specific status.

        Parameters
        ----------
        status : str
            The status to filter by.
        entity_slug : str
            The entity slug.

        Returns
        -------
        PhieuNhapChiPhiMuaHangModelQuerySet
            QuerySet of PhieuNhapChiPhiMuaHangModel instances.
        """
        return self.repository.get_by_status(status=status, entity_slug=entity_slug)

    def get_by_date_range(
        self, start_date: str, end_date: str, entity_slug: str
    ) -> QuerySet:  # noqa: C901
        """
        Gets PhieuNhapChiPhiMuaHangModel instances within a date range.

        Parameters
        ----------
        start_date : str
            The start date (YYYY-MM-DD format).
        end_date : str
            The end date (YYYY-MM-DD format).
        entity_slug : str
            The entity slug.

        Returns
        -------
        PhieuNhapChiPhiMuaHangModelQuerySet
            QuerySet of PhieuNhapChiPhiMuaHangModel instances.
        """
        return self.repository.get_by_date_range(
            start_date=start_date, end_date=end_date, entity_slug=entity_slug
        )

    @transaction.atomic
    def update(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[PhieuNhapChiPhiMuaHangModel]:  # noqa: C901
        """
        ✅ ENHANCED: Updates an existing PhieuNhapChiPhiMuaHangModel instance with automatic accounting.

        Tự động cập nhật bút toán kế toán cho phiếu nhập chi phí mua hàng sử dụng unified services.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuNhapChiPhiMuaHangModel to update.
        data : Dict[str, Any]
            The data to update the PhieuNhapChiPhiMuaHangModel with.

        Returns
        -------
        Optional[PhieuNhapChiPhiMuaHangModel]
            The updated PhieuNhapChiPhiMuaHangModel instance, or None if not found.
        """
        return self.update_with_details(entity_slug=entity_slug, uuid=uuid, data=data)

    @transaction.atomic
    def update_with_details(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[PhieuNhapChiPhiMuaHangModel]:
        """
        ✅ ENHANCED: Updates an existing PhieuNhapChiPhiMuaHangModel instance with automatic accounting and bulk detail updates.
        """
        chi_tiet_phieu_nhaps = data.pop('chi_tiet_phieu_nhaps', None)
        chi_phi_phieu_nhaps = data.pop('chi_phi_phieu_nhaps', None)
        chi_phi_chi_tiet_phieu_nhaps = data.pop('chi_phi_chi_tiet_phieu_nhaps', None)
        thue_phieu_nhaps = data.pop('thue_phieu_nhaps', None)

        instance = self.repository.update(entity_slug=entity_slug, uuid=uuid, data=data)

        if instance:
            if chi_tiet_phieu_nhaps is not None:
                self.chi_tiet_service.bulk_update(parent=instance, data=chi_tiet_phieu_nhaps)

            if chi_phi_phieu_nhaps is not None:
                self.chi_phi_service.bulk_update(parent=instance, data=chi_phi_phieu_nhaps)

            if chi_phi_chi_tiet_phieu_nhaps is not None:
                self.chi_phi_chi_tiet_service.bulk_update(parent=instance, data=chi_phi_chi_tiet_phieu_nhaps)

            if thue_phieu_nhaps is not None:
                self.thue_service.bulk_update(parent=instance, data=thue_phieu_nhaps)

        if instance and instance.ledger:
            try:
                self._cong_no_service.update_document_accounting_entries(
                    source_document=instance,
                    document_type="phiếu nhập chi phí mua hàng",
                    account_mappings=self._determine_accounting_mappings(instance),
                )
            except Exception as e:
                raise Exception(
                    f"Failed to update accounting entry for PhieuNhapChiPhi {instance.so_ct}: {str(e)}"
                ) from e

        if instance:
            instance.refresh_from_db()
        return instance

    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a PhieuNhapChiPhiMuaHangModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuNhapChiPhiMuaHangModel to delete.

        Returns
        -------
        bool
            True if the PhieuNhapChiPhiMuaHangModel was deleted, False otherwise.
        """
        instance = self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not instance:
            return False

        # If a ledger exists, delete its entries first.
        if instance.ledger:
            try:
                self._cong_no_service.delete_document_accounting_entries(
                    source_document=instance
                )
                return True
            except Exception as e:
                raise Exception(
                    f"Failed to delete accounting entries for {instance.so_ct}: {str(e)}"
                ) from e
        else:
            # Proceed with deleting the main document.
            return self.repository.delete(entity_slug=entity_slug, uuid=uuid)
