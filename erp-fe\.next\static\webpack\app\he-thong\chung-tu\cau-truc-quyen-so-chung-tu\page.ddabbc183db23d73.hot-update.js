"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/constants/admin-url.ts":
/*!************************************!*\
  !*** ./src/constants/admin-url.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\nvar _process_env_NEXT_PUBLIC_API_URL;\nconst BASE_URL = \"\".concat(((_process_env_NEXT_PUBLIC_API_URL = \"https://erp.nguyenlieu3t.xyz/api\") === null || _process_env_NEXT_PUBLIC_API_URL === void 0 ? void 0 : _process_env_NEXT_PUBLIC_API_URL.replace(/\\/api$/, \"\")) || \"\", \"/thayminhtue\");\nconst ADMIN_URL = {\n    /* Hệ thống/Người sử dụng */ USER: \"\".concat(BASE_URL, \"/auth/user\"),\n    /* Hệ thống/Nhóm người sử dụng */ GROUPS_USER: \"\".concat(BASE_URL, \"/auth/group\"),\n    /* Công cụ/Khai báo thông tin công cụ dụng cụ */ CONG_CU_DUNG_CU: \"\".concat(BASE_URL, \"/django_ledger/khaibaothongtinccdcmodel\"),\n    /* Tài sản/Khai báo thông tin tài sản cố định */ TAI_SAN_CO_DINH: \"\".concat(BASE_URL, \"/django_ledger/khaibaothongtintaisancodinhmodel\"),\n    /* Danh mục/Vật tư, sản phẩm */ VAT_TU: \"\".concat(BASE_URL, \"/django_ledger/vattumodel\"),\n    /* Danh mục/Thuế/Thuế suất thuế GTGT */ THUE_SUAT_THUE_GTGT: \"\".concat(BASE_URL, \"/django_ledger/thuesuatthuegtgtmodel\"),\n    /* Danh mục/Thuế/Thuế suất thuế nhập khẩu */ THUE_SUAT_THUE_NHAP_KHAU: \"\".concat(BASE_URL, \"/django_ledger/thuesuatthuenhapkhauthuegtgtmodel\"),\n    /* Hệ thống/Chứng từ/Cấu trúc quyền số chứng từ */ QUYEN_CHUNG_TU: \"\".concat(BASE_URL, \"/django_ledger/quyenchungtu\")\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (ADMIN_URL);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/constants/admin-url.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/.pnpm/axios@1.8.4/node_modules/axios/lib/axios.js\");\n/* harmony import */ var axios_mock_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios-mock-adapter */ \"(app-pages-browser)/./node_modules/.pnpm/axios-mock-adapter@2.1.0_axios@1.8.4/node_modules/axios-mock-adapter/src/index.js\");\n/* harmony import */ var axios_mock_adapter__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(axios_mock_adapter__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n\n\n\n// PostHog will be imported dynamically to avoid SSR issues\nlet posthog = null;\nif (true) {\n    Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! posthog-js */ \"(app-pages-browser)/./node_modules/.pnpm/posthog-js@1.257.0/node_modules/posthog-js/dist/module.js\")).then((module)=>{\n        posthog = module.default;\n    });\n}\n// PostHog logging utilities\nconst logFailedRequest = (error, requestId)=>{\n    var _request_method;\n    // Only log to PostHog in production, not in local development\n    const isDevelopment = \"development\" === \"development\";\n    const isLocalhost =  true && (window.location.hostname === \"localhost\" || window.location.hostname === \"127.0.0.1\" || window.location.hostname.includes(\"localhost\"));\n    // Skip logging in development, localhost, or if PostHog not properly initialized\n    if (!posthog || !posthog.__loaded || isDevelopment || isLocalhost) return;\n    const request = error.config;\n    const response = error.response;\n    // Prepare detailed request information\n    const requestData = {\n        request_id: requestId,\n        timestamp: new Date().toISOString(),\n        method: request === null || request === void 0 ? void 0 : (_request_method = request.method) === null || _request_method === void 0 ? void 0 : _request_method.toUpperCase(),\n        url: request === null || request === void 0 ? void 0 : request.url,\n        full_url: (request === null || request === void 0 ? void 0 : request.baseURL) ? \"\".concat(request.baseURL).concat(request.url) : request === null || request === void 0 ? void 0 : request.url,\n        headers: request === null || request === void 0 ? void 0 : request.headers,\n        request_body: request === null || request === void 0 ? void 0 : request.data,\n        params: request === null || request === void 0 ? void 0 : request.params,\n        timeout: request === null || request === void 0 ? void 0 : request.timeout,\n        status_code: response === null || response === void 0 ? void 0 : response.status,\n        status_text: response === null || response === void 0 ? void 0 : response.statusText,\n        response_headers: response === null || response === void 0 ? void 0 : response.headers,\n        response_body: response === null || response === void 0 ? void 0 : response.data,\n        error_message: error.message,\n        error_code: error.code,\n        network_error: !response,\n        user_agent:  true ? navigator.userAgent : 0,\n        page_url:  true ? window.location.href : 0\n    };\n    // Generate curl command for debugging\n    const curlCommand = generateCurlCommand(request);\n    // Log to PostHog using custom event name (simpler and more reliable)\n    posthog.capture(\"api_request_failed\", {\n        ...requestData,\n        curl_command: curlCommand,\n        event_type: \"error\",\n        error_category: \"api_failure\",\n        debug_info: {\n            can_regenerate_with_curl: true,\n            suggested_action: \"Copy curl command and test in terminal or send to AI for analysis\"\n        }\n    });\n};\nconst generateCurlCommand = (config)=>{\n    var _config_method;\n    if (!config) return \"\";\n    const method = ((_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toUpperCase()) || \"GET\";\n    const url = config.baseURL ? \"\".concat(config.baseURL).concat(config.url) : config.url;\n    const headers = config.headers || {};\n    let curl = \"curl -X \".concat(method);\n    // Add headers\n    Object.entries(headers).forEach((param)=>{\n        let [key, value] = param;\n        if (key !== \"common\" && key !== \"delete\" && key !== \"get\" && key !== \"head\" && key !== \"post\" && key !== \"put\" && key !== \"patch\") {\n            curl += ' -H \"'.concat(key, \": \").concat(value, '\"');\n        }\n    });\n    // Add request body for POST/PUT/PATCH\n    if (config.data && [\n        \"POST\",\n        \"PUT\",\n        \"PATCH\"\n    ].includes(method)) {\n        const dataStr = typeof config.data === \"string\" ? config.data : JSON.stringify(config.data);\n        curl += \" -d '\".concat(dataStr, \"'\");\n    }\n    // Add URL with params\n    if (config.params) {\n        const params = new URLSearchParams(config.params).toString();\n        curl += ' \"'.concat(url, \"?\").concat(params, '\"');\n    } else {\n        curl += ' \"'.concat(url, '\"');\n    }\n    return curl;\n};\nconst generateRequestId = ()=>{\n    return \"req_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n};\n// Create base axios instance\nconst baseApi = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n    baseURL: \"https://erp.nguyenlieu3t.xyz/api\" || 0,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\nconsole.log(\"API URL:\", \"https://erp.nguyenlieu3t.xyz/api\" || 0);\nclass EnhancedAxiosInstance {\n    setupMockAdapter(_config) {\n        if (!this.mockAdapter) {\n            this.mockAdapter = new (axios_mock_adapter__WEBPACK_IMPORTED_MODULE_0___default())(this.instance, {\n                delayResponse: 300,\n                onNoMatch: \"passthrough\"\n            });\n        }\n        return this.mockAdapter;\n    }\n    createMockResponse(mockData, _url, _method) {\n        if (mockData && mockData.length > 0) {\n            const response = {\n                count: mockData.length,\n                next: null,\n                previous: null,\n                results: mockData\n            };\n            return response;\n        }\n        const emptyResponse = {\n            count: 0,\n            next: null,\n            previous: null,\n            results: []\n        };\n        return emptyResponse;\n    }\n    async get(url, config) {\n        if ((config === null || config === void 0 ? void 0 : config.mock) === true) {\n            const mock = this.setupMockAdapter(config);\n            const mockResponse = this.createMockResponse(config.mockData, url, \"GET\");\n            mock.onGet(url).reply(200, mockResponse);\n            try {\n                const { mock: _, mockData: __, ...cleanConfig } = config;\n                const response = await this.instance.get(url, cleanConfig);\n                mock.reset();\n                return response;\n            } catch (error) {\n                mock.reset();\n                throw error;\n            }\n        }\n        return this.instance.get(url, config);\n    }\n    async post(url, data, config) {\n        return this.instance.post(url, data, config);\n    }\n    async put(url, data, config) {\n        return this.instance.put(url, data, config);\n    }\n    async delete(url, config) {\n        return this.instance.delete(url, config);\n    }\n    async patch(url, data, config) {\n        return this.instance.patch(url, data, config);\n    }\n    get interceptors() {\n        return this.instance.interceptors;\n    }\n    resetMock() {\n        if (this.mockAdapter) {\n            this.mockAdapter.reset();\n        }\n    }\n    constructor(){\n        this.instance = baseApi;\n        this.mockAdapter = null;\n    }\n}\n// Create enhanced API instance\nconst api = new EnhancedAxiosInstance();\n// Add request interceptor to include auth token and request ID\napi.interceptors.request.use((config)=>{\n    // Add auth token\n    const token = localStorage.getItem(\"auth_token\");\n    if (token) {\n        config.headers[\"Authorization\"] = \"Token \".concat(token);\n    }\n    // Add unique request ID for tracking\n    const requestId = generateRequestId();\n    config.metadata = {\n        requestId,\n        startTime: Date.now()\n    };\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Add response interceptor to handle errors and log failures\napi.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    var _error_config_metadata, _error_config, _error_response;\n    // Get request ID from metadata\n    const requestId = ((_error_config = error.config) === null || _error_config === void 0 ? void 0 : (_error_config_metadata = _error_config.metadata) === null || _error_config_metadata === void 0 ? void 0 : _error_config_metadata.requestId) || generateRequestId();\n    // Log failed request to PostHog (PRODUCTION ONLY)\n    logFailedRequest(error, requestId);\n    // Handle auth errors\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n        // Clear token and redirect to login on auth error\n        localStorage.removeItem(\"auth_token\");\n        window.location.href = \"/xac-thuc\";\n    }\n    return Promise.reject(error);\n});\napi.interceptors.response.use((res)=>{\n    var _res_config_method, _res_config;\n    const data = res === null || res === void 0 ? void 0 : res.data;\n    const method = res === null || res === void 0 ? void 0 : (_res_config = res.config) === null || _res_config === void 0 ? void 0 : (_res_config_method = _res_config.method) === null || _res_config_method === void 0 ? void 0 : _res_config_method.toLowerCase();\n    const status = res === null || res === void 0 ? void 0 : res.status;\n    const shouldToastSuccess = method === \"put\" || method === \"delete\" || status === 201;\n    if (shouldToastSuccess) {\n        if (data === null || data === void 0 ? void 0 : data.message) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__.toast)({\n                title: \"Th\\xe0nh c\\xf4ng\",\n                description: typeof data.message === \"string\" ? data.message : \"Cập nhật th\\xe0nh c\\xf4ng\",\n                variant: \"default\",\n                duration: 3000\n            });\n        } else {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__.toast)({\n                title: \"Th\\xe0nh c\\xf4ng\",\n                variant: \"default\",\n                duration: 3000\n            });\n        }\n    }\n    return res;\n}, (error)=>{\n    var _error_response, _error_response1;\n    const status = error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status;\n    const data = error === null || error === void 0 ? void 0 : (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data;\n    let description = \"C\\xf3 lỗi xảy ra\";\n    if (status === 500) {\n        description = \"C\\xf3 lỗi xảy ra, vui l\\xf2ng li\\xean hệ với quản trị vi\\xean\";\n    } else if (status === 404) {\n        description = \"404 Not Found\";\n    } else if (data) {\n        if (typeof data === \"string\") {\n            description = data;\n        } else if (data.detail) {\n            description = data.detail;\n        } else if (data.message) {\n            // Handle case where data.message is an object (validation errors)\n            if (typeof data.message === \"object\" && data.message !== null) {\n                description = Object.entries(data.message).map((param)=>{\n                    let [field, messages] = param;\n                    if (Array.isArray(messages)) {\n                        return messages.map((msg)=>\"\".concat(field, \": \").concat(msg)).join(\"\\n\");\n                    }\n                    return \"\".concat(field, \": \").concat(messages);\n                }).join(\"\\n\");\n            } else {\n                description = data.message;\n            }\n        } else if (typeof data === \"object\") {\n            description = Object.entries(data).map((param)=>{\n                let [field, messages] = param;\n                if (Array.isArray(messages)) {\n                    return messages.map((msg)=>\"\".concat(field, \": \").concat(msg)).join(\"\\n\");\n                }\n                return \"\".concat(field, \": \").concat(messages);\n            }).join(\"\\n\");\n        }\n    }\n    (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__.toast)({\n        title: \"Lỗi\",\n        description,\n        variant: \"destructive\",\n        duration: 3000\n    });\n    return Promise.reject(error);\n});\n/* harmony default export */ __webpack_exports__[\"default\"] = (api);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});