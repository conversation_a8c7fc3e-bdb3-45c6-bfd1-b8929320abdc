/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-split@2.0.14_react@18.3.1";
exports.ids = ["vendor-chunks/react-split@2.0.14_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/react-split@2.0.14_react@18.3.1/node_modules/react-split/dist/react-split.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-split@2.0.14_react@18.3.1/node_modules/react-split/dist/react-split.js ***!
  \*********************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("/*! react-split - v2.0.14 */\n\n(function (global, factory) {\n     true ? module.exports = factory(__webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"), __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/index.js\"), __webpack_require__(/*! split.js */ \"(ssr)/./node_modules/.pnpm/split.js@1.6.5/node_modules/split.js/dist/split.js\")) :\n    0;\n}(this, (function (React, PropTypes, Split) { 'use strict';\n\n    function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }\n\n    var React__default = /*#__PURE__*/_interopDefaultLegacy(React);\n    var PropTypes__default = /*#__PURE__*/_interopDefaultLegacy(PropTypes);\n    var Split__default = /*#__PURE__*/_interopDefaultLegacy(Split);\n\n    function objectWithoutProperties (obj, exclude) { var target = {}; for (var k in obj) if (Object.prototype.hasOwnProperty.call(obj, k) && exclude.indexOf(k) === -1) target[k] = obj[k]; return target; }\n\n    var SplitWrapper = /*@__PURE__*/(function (superclass) {\n        function SplitWrapper () {\n            superclass.apply(this, arguments);\n        }\n\n        if ( superclass ) SplitWrapper.__proto__ = superclass;\n        SplitWrapper.prototype = Object.create( superclass && superclass.prototype );\n        SplitWrapper.prototype.constructor = SplitWrapper;\n\n        SplitWrapper.prototype.componentDidMount = function componentDidMount () {\n            var ref = this.props;\n            ref.children;\n            var gutter = ref.gutter;\n            var rest = objectWithoutProperties( ref, [\"children\", \"gutter\"] );\n            var options = rest;\n\n            options.gutter = function (index, direction) {\n                var gutterElement;\n\n                if (gutter) {\n                    gutterElement = gutter(index, direction);\n                } else {\n                    gutterElement = document.createElement('div');\n                    gutterElement.className = \"gutter gutter-\" + direction;\n                }\n\n                // eslint-disable-next-line no-underscore-dangle\n                gutterElement.__isSplitGutter = true;\n                return gutterElement\n            };\n\n            this.split = Split__default['default'](this.parent.children, options);\n        };\n\n        SplitWrapper.prototype.componentDidUpdate = function componentDidUpdate (prevProps) {\n            var this$1 = this;\n\n            var ref = this.props;\n            ref.children;\n            var minSize = ref.minSize;\n            var sizes = ref.sizes;\n            var collapsed = ref.collapsed;\n            var rest = objectWithoutProperties( ref, [\"children\", \"minSize\", \"sizes\", \"collapsed\"] );\n            var options = rest;\n            var prevMinSize = prevProps.minSize;\n            var prevSizes = prevProps.sizes;\n            var prevCollapsed = prevProps.collapsed;\n\n            var otherProps = [\n                'maxSize',\n                'expandToMin',\n                'gutterSize',\n                'gutterAlign',\n                'snapOffset',\n                'dragInterval',\n                'direction',\n                'cursor' ];\n\n            var needsRecreate = otherProps\n                // eslint-disable-next-line react/destructuring-assignment\n                .map(function (prop) { return this$1.props[prop] !== prevProps[prop]; })\n                .reduce(function (accum, same) { return accum || same; }, false);\n\n            // Compare minSize when both are arrays, when one is an array and when neither is an array\n            if (Array.isArray(minSize) && Array.isArray(prevMinSize)) {\n                var minSizeChanged = false;\n\n                minSize.forEach(function (minSizeI, i) {\n                    minSizeChanged = minSizeChanged || minSizeI !== prevMinSize[i];\n                });\n\n                needsRecreate = needsRecreate || minSizeChanged;\n            } else if (Array.isArray(minSize) || Array.isArray(prevMinSize)) {\n                needsRecreate = true;\n            } else {\n                needsRecreate = needsRecreate || minSize !== prevMinSize;\n            }\n\n            // Destroy and re-create split if options changed\n            if (needsRecreate) {\n                options.minSize = minSize;\n                options.sizes = sizes || this.split.getSizes();\n                this.split.destroy(true, true);\n                options.gutter = function (index, direction, pairB) { return pairB.previousSibling; };\n                this.split = Split__default['default'](\n                    Array.from(this.parent.children).filter(\n                        // eslint-disable-next-line no-underscore-dangle\n                        function (element) { return !element.__isSplitGutter; }\n                    ),\n                    options\n                );\n            } else if (sizes) {\n                // If only the size has changed, set the size. No need to do this if re-created.\n                var sizeChanged = false;\n\n                sizes.forEach(function (sizeI, i) {\n                    sizeChanged = sizeChanged || sizeI !== prevSizes[i];\n                });\n\n                if (sizeChanged) {\n                    // eslint-disable-next-line react/destructuring-assignment\n                    this.split.setSizes(this.props.sizes);\n                }\n            }\n\n            // Collapse after re-created or when collapsed changed.\n            if (\n                Number.isInteger(collapsed) &&\n                (collapsed !== prevCollapsed || needsRecreate)\n            ) {\n                this.split.collapse(collapsed);\n            }\n        };\n\n        SplitWrapper.prototype.componentWillUnmount = function componentWillUnmount () {\n            this.split.destroy();\n            delete this.split;\n        };\n\n        SplitWrapper.prototype.render = function render () {\n            var this$1 = this;\n\n            var ref = this.props;\n            ref.sizes;\n            ref.minSize;\n            ref.maxSize;\n            ref.expandToMin;\n            ref.gutterSize;\n            ref.gutterAlign;\n            ref.snapOffset;\n            ref.dragInterval;\n            ref.direction;\n            ref.cursor;\n            ref.gutter;\n            ref.elementStyle;\n            ref.gutterStyle;\n            ref.onDrag;\n            ref.onDragStart;\n            ref.onDragEnd;\n            ref.collapsed;\n            var children = ref.children;\n            var rest$1 = objectWithoutProperties( ref, [\"sizes\", \"minSize\", \"maxSize\", \"expandToMin\", \"gutterSize\", \"gutterAlign\", \"snapOffset\", \"dragInterval\", \"direction\", \"cursor\", \"gutter\", \"elementStyle\", \"gutterStyle\", \"onDrag\", \"onDragStart\", \"onDragEnd\", \"collapsed\", \"children\"] );\n            var rest = rest$1;\n\n            return (\n                React__default['default'].createElement( 'div', Object.assign({},\n                    { ref: function (parent) {\n                        this$1.parent = parent;\n                    } }, rest),\n                    children\n                )\n            )\n        };\n\n        return SplitWrapper;\n    }(React__default['default'].Component));\n\n    SplitWrapper.propTypes = {\n        sizes: PropTypes__default['default'].arrayOf(PropTypes__default['default'].number),\n        minSize: PropTypes__default['default'].oneOfType([\n            PropTypes__default['default'].number,\n            PropTypes__default['default'].arrayOf(PropTypes__default['default'].number) ]),\n        maxSize: PropTypes__default['default'].oneOfType([\n            PropTypes__default['default'].number,\n            PropTypes__default['default'].arrayOf(PropTypes__default['default'].number) ]),\n        expandToMin: PropTypes__default['default'].bool,\n        gutterSize: PropTypes__default['default'].number,\n        gutterAlign: PropTypes__default['default'].string,\n        snapOffset: PropTypes__default['default'].oneOfType([\n            PropTypes__default['default'].number,\n            PropTypes__default['default'].arrayOf(PropTypes__default['default'].number) ]),\n        dragInterval: PropTypes__default['default'].number,\n        direction: PropTypes__default['default'].string,\n        cursor: PropTypes__default['default'].string,\n        gutter: PropTypes__default['default'].func,\n        elementStyle: PropTypes__default['default'].func,\n        gutterStyle: PropTypes__default['default'].func,\n        onDrag: PropTypes__default['default'].func,\n        onDragStart: PropTypes__default['default'].func,\n        onDragEnd: PropTypes__default['default'].func,\n        collapsed: PropTypes__default['default'].number,\n        children: PropTypes__default['default'].arrayOf(PropTypes__default['default'].element),\n    };\n\n    SplitWrapper.defaultProps = {\n        sizes: undefined,\n        minSize: undefined,\n        maxSize: undefined,\n        expandToMin: undefined,\n        gutterSize: undefined,\n        gutterAlign: undefined,\n        snapOffset: undefined,\n        dragInterval: undefined,\n        direction: undefined,\n        cursor: undefined,\n        gutter: undefined,\n        elementStyle: undefined,\n        gutterStyle: undefined,\n        onDrag: undefined,\n        onDragStart: undefined,\n        onDragEnd: undefined,\n        collapsed: undefined,\n        children: undefined,\n    };\n\n    return SplitWrapper;\n\n})));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-split@2.0.14_react@18.3.1/node_modules/react-split/dist/react-split.js\n");

/***/ })

};
;