"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiPhiChiTietPhieuNhapChiPhiMuahangService, which handles business logic  # noqa: E501
for the ChiPhiChiTietPhieuNhapChiPhiMuahangModel.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import transaction
from django.db.models import QuerySet  # noqa: F401

from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import (
    ChiPhiChiTietPhieuNhapChiPhiMuahangModel,  # noqa: F401
    PhieuNhapChiPhiMuaHangModel,
)
from django_ledger.repositories.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import (
    ChiPhiChiTietPhieuNhapChiPhiMuahangRepository,  # noqa: F401
)
from django_ledger.services.base import BaseService  # noqa: F401


class ChiPhiChiTietPhieuNhapChiPhiMuahangService(BaseService):
    """
    Service class for ChiPhiChiTietPhieuNhapChiPhiMuahangModel.
    Handles business logic for the model.
    """

    def __init__(self):  # noqa: C901
        self.repository = ChiPhiChiTietPhieuNhapChiPhiMuahangRepository()

    def get_by_id(
        self, uuid: Union[str, UUID]
    ) -> Optional[ChiPhiChiTietPhieuNhapChiPhiMuahangModel]:  # noqa: C901
        """
        Retrieves a ChiPhiChiTietPhieuNhapChiPhiMuahangModel by its UUID.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiPhiChiTietPhieuNhapChiPhiMuahangModel to retrieve.

        Returns
        -------
        Optional[ChiPhiChiTietPhieuNhapChiPhiMuahangModel]
            The ChiPhiChiTietPhieuNhapChiPhiMuahangModel with the given UUID, or None if not found.  # noqa: E501
        """
        return self.repository.get_by_id(uuid=uuid)

    def list_for_phieu_nhap(
        self, phieu_nhap_id: Union[str, UUID], **kwargs
    ) -> QuerySet:  # noqa: C901
        """
        Lists ChiPhiChiTietPhieuNhapChiPhiMuahangModel instances for a specific purchase expense receipt.  # noqa: E501

        Parameters
        ----------
        phieu_nhap_id : Union[str, UUID]
            The UUID of the PhieuNhapChiPhiMuaHangModel.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of ChiPhiChiTietPhieuNhapChiPhiMuahangModel instances.
        """
        return self.repository.list_for_phieu_nhap(
            phieu_nhap_id=phieu_nhap_id, **kwargs
        )

    def create(
        self, parent: PhieuNhapChiPhiMuaHangModel, data: Dict[str, Any]
    ) -> ChiPhiChiTietPhieuNhapChiPhiMuahangModel:  # noqa: C901
        """
        Creates a new ChiPhiChiTietPhieuNhapChiPhiMuahangModel instance.

        Parameters
        ----------
        parent : PhieuNhapChiPhiMuaHangModel
            The parent PhieuNhapChiPhiMuaHangModel instance.
        data : Dict[str, Any]
            The data for the new ChiPhiChiTietPhieuNhapChiPhiMuahangModel.

        Returns
        -------
        ChiPhiChiTietPhieuNhapChiPhiMuahangModel
            The created ChiPhiChiTietPhieuNhapChiPhiMuahangModel instance.
        """
        return self.repository.create(phieu_nhap_id=parent.uuid, data=data)

    @transaction.atomic
    def bulk_create(
        self, parent: PhieuNhapChiPhiMuaHangModel, data: List[Dict[str, Any]]
    ) -> None:
        """
        Creates multiple ChiPhiChiTietPhieuNhapChiPhiMuahangModel instances.

        Parameters
        ----------
        parent : PhieuNhapChiPhiMuaHangModel
            The parent PhieuNhapChiPhiMuaHangModel instance.
        data : List[Dict[str, Any]]
            The list of data for creating the instances.
        """
        for item in data:
            self.create(parent=parent, data=item)

    @transaction.atomic
    def bulk_update(
        self, parent: PhieuNhapChiPhiMuaHangModel, data: List[Dict[str, Any]]
    ) -> None:
        """
        Updates, creates, or deletes multiple ChiPhiChiTietPhieuNhapChiPhiMuahangModel instances.

        Parameters
        ----------
        parent : PhieuNhapChiPhiMuaHangModel
            The parent PhieuNhapChiPhiMuaHangModel instance.
        data : List[Dict[str, Any]]
            The list of data for updating the instances.
        """
        existing_items = parent.chi_phi_chi_tiet_phieu_nhaps.all()
        existing_items_dict = {str(item.uuid): item for item in existing_items}
        updated_uuids = set()

        for item_data in data:
            item_uuid = item_data.get('uuid')
            if item_uuid and item_uuid in existing_items_dict:
                updated_uuids.add(item_uuid)
                self.update(uuid=item_uuid, data=item_data)
            else:
                self.create(parent=parent, data=item_data)

        for uuid_to_delete in set(existing_items_dict.keys()) - updated_uuids:
            self.delete(uuid=uuid_to_delete)

    def update(
        self, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[ChiPhiChiTietPhieuNhapChiPhiMuahangModel]:  # noqa: C901
        """
        Updates an existing ChiPhiChiTietPhieuNhapChiPhiMuahangModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiPhiChiTietPhieuNhapChiPhiMuahangModel to update.
        data : Dict[str, Any]
            The data to update the ChiPhiChiTietPhieuNhapChiPhiMuahangModel with.

        Returns
        -------
        Optional[ChiPhiChiTietPhieuNhapChiPhiMuahangModel]
            The updated ChiPhiChiTietPhieuNhapChiPhiMuahangModel instance, or None if not found.  # noqa: E501
        """
        return self.repository.update(uuid=uuid, data=data)

    def delete(self, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a ChiPhiChiTietPhieuNhapChiPhiMuahangModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiPhiChiTietPhieuNhapChiPhiMuahangModel to delete.

        Returns
        -------
        bool
            True if the ChiPhiChiTietPhieuNhapChiPhiMuahangModel was deleted, False otherwise.  # noqa: E501
        """
        return self.repository.delete(uuid=uuid)
