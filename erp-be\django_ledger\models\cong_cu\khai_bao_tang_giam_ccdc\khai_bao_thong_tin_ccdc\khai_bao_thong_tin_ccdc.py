"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoThongTinCCDC (Tool Information Declaration) model implementation.
"""

import uuid  # noqa: F401

from django.core.validators import MinValueValidator  # noqa: F401
from django.db import models  # noqa: F401
from django.db.models import Manager, Q, QuerySet  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class KhaiBaoThongTinCCDCModelQueryset(QuerySet):
    """
    A custom defined KhaiBaoThongTinCCDCModelQueryset that will act as an interface to handling the DB queries to the  # noqa: E501
    KhaiBaoThongTinCCDCModel.
    """

    def for_entity(self, entity_slug, user_model=None):  # noqa: F811,
        """
        Fetches a QuerySet of KhaiBaoThongTinCCDCModels for a specific entity.

        Parameters
        __________
        entity_slug: str
            The entity slug to filter by.
        user_model: UserModel
            The user model to filter by.
        """
        qs = self.filter(entity_model__slug__exact=entity_slug)
        if user_model:
            qs = qs.filter(
                Q(entity_model__admin=user_model)
                | Q(entity_model__managers__in=[user_model])
            )
        return qs


class KhaiBaoThongTinCCDCModelManager(Manager):
    """
    A custom defined KhaiBaoThongTinCCDCModelManager that will act as an interface to handling the DB queries to the  # noqa: E501
    KhaiBaoThongTinCCDCModel.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom KhaiBaoThongTinCCDCModelQueryset.
        """
        return KhaiBaoThongTinCCDCModelQueryset(self.model, using=self._db)

    def for_entity(self, entity_slug, user_model=None):  # noqa: F811,
        """
        Returns KhaiBaoThongTinCCDCModel for a specific entity.

        Parameters
        ----------
        entity_slug: str
            The entity slug to filter by.
        user_model: UserModel
            The user model to filter by.

        Returns
        -------
        KhaiBaoThongTinCCDCModelQueryset
            A QuerySet of KhaiBaoThongTinCCDCModel with applied filters.
        """
        return self.get_queryset().for_entity(
            entity_slug=entity_slug, user_model=user_model
        )


class KhaiBaoThongTinCCDCModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the KhaiBaoThongTinCCDCModel database will inherit from.  # noqa: E501
    The KhaiBaoThongTinCCDCModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        Primary key field generated with uuid.uuid4().

    entity_model: ForeignKey
        The EntityModel this record belongs to (non-nullable).

    ma_cc: str
        Tool code (max 50 chars).

    ten_cc: str
        Tool name (max 255 chars).

    ten_cc2: str
        Tool name 2 (max 255 chars, optional).

    ma_lcc: ForeignKey
        Tool category (LoaiTaiSanCongCuModel, nullable).

    ma_bp: ForeignKey
        Department (BoPhanSuDungCCDCModel, nullable).

    ngay_mua: date
        Purchase date.

    ngay_kh0: date
        Initial depreciation date.

    ngay_kh_kt: date
        Depreciation end date.

    ngay_ct: date
        Document date.

    so_ky_kh: decimal
        Number of depreciation periods.

    kieu_kh: bool
        Depreciation type (default False).

    so_luong: decimal
        Quantity (default 0).

    dvt: ForeignKey
        Unit of measure (DonViTinhModel, nullable).

    unit_id: ForeignKey
        Unit (EntityUnitModel, nullable).

    ma_nt: ForeignKey
        Currency (NgoaiTeModel, nullable).

    ty_gia: decimal
        Exchange rate (default 1).

    tk_cc: ForeignKey
        Tool account (AccountModel, non-nullable).

    tk_kh: ForeignKey
        Depreciation account (AccountModel, non-nullable).

    tk_cp: ForeignKey
        Expense account (AccountModel, non-nullable).

    nguyen_gia_nt: decimal
        Original value in foreign currency (default 0).

    gt_da_kh_nt: decimal
        Depreciated value in foreign currency (default 0).

    gt_cl_nt: decimal
        Remaining value in foreign currency (default 0).

    gt_kh_ky_nt: decimal
        Period depreciation in foreign currency (default 0).

    nguyen_gia: decimal
        Original value in VND (default 0).

    gt_da_kh: decimal
        Depreciated value in VND (default 0).

    gt_cl: decimal
        Remaining value in VND (default 0).

    gt_kh_ky: decimal
        Period depreciation in VND (default 0).

    status: str
        Status (max 2 chars, default "1").

    so_hieu_cc: str
        Tool serial number (max 50 chars, optional).

    so_ct: ForeignKey
        Document (ChungTu, nullable).

    nh_cc1: ForeignKey
        Tool group 1 (GroupModel, nullable).

    nh_cc2: ForeignKey
        Tool group 2 (GroupModel, nullable).

    nh_cc3: ForeignKey
        Tool group 3 (GroupModel, nullable).

    nuoc_sx: str
        Country of manufacture (max 100 chars, optional).

    nam_sx: int
        Year of manufacture (default 0, min 0).

    ghi_chu: str
        Notes (optional).
    """

    # Primary key field
    uuid = models.UUIDField(
        primary_key=True, default=uuid.uuid4, editable=False
    )
    # Entity relationship
    entity_model = models.ForeignKey(
        'django_ledger.EntityModel',
        on_delete=models.CASCADE,
        verbose_name=_('Entity'),
        help_text=_('The entity this record belongs to'),
    )

    # Mã và tên công cụ
    ma_cc = models.CharField(max_length=50, verbose_name=_('Mã công cụ'))
    ten_cc = models.CharField(max_length=255, verbose_name=_('Tên công cụ'))
    ten_cc2 = models.CharField(
        max_length=255, blank=True, null=True, verbose_name=_('Tên công cụ 2')
    )

    # Loại công cụ (Foreign Key)
    ma_lcc = models.ForeignKey(
        'django_ledger.LoaiTaiSanCongCuModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='khai_bao_thong_tin_ccdc_ma_lcc',
        verbose_name=_('Mã loại công cụ'),
    )

    # Bộ phận (Foreign Key)
    ma_bp = models.ForeignKey(
        'django_ledger.BoPhanSuDungCCDCModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='khai_bao_thong_tin_ccdc_ma_bp',
        verbose_name=_('Mã bộ phận'),
    )

    # Thông tin ngày tháng
    ngay_mua = models.DateField(verbose_name=_('Ngày mua'))
    ngay_kh0 = models.DateField(verbose_name=_('Ngày bắt đầu khấu hao'))
    ngay_kh_kt = models.DateField(verbose_name=_('Ngày kết thúc khấu hao'))
    ngay_ct = models.DateField(verbose_name=_('Ngày chứng từ'))
    # Thông tin khấu hao
    so_ky_kh = models.DecimalField(
        max_digits=10, decimal_places=2, verbose_name=_('Số kỳ khấu hao')
    )
    kieu_kh = models.BooleanField(
        default=False, verbose_name=_('Kiểu khấu hao')
    )
    # Số lượng và đơn vị tính
    so_luong = models.DecimalField(
        max_digits=15,
        decimal_places=3,
        default=0,
        verbose_name=_('Số lượng'),
    )
    dvt = models.ForeignKey(
        'django_ledger.DonViTinhModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='khai_bao_thong_tin_ccdc_dvt',
        verbose_name=_('Đơn vị tính'),
    )

    # Đơn vị (Foreign Key)
    unit_id = models.ForeignKey(
        'django_ledger.EntityUnitModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='khai_bao_thong_tin_ccdc_unit_id',
        verbose_name=_('ID đơn vị'),
    )

    # Ngoại tệ (Foreign Key)
    ma_nt = models.ForeignKey(
        'django_ledger.NgoaiTeModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='khai_bao_thong_tin_ccdc_ma_nt',
        verbose_name=_('Mã ngoại tệ'),
    )
    ty_gia = models.DecimalField(
        max_digits=15,
        decimal_places=6,
        default=1,
        verbose_name=_('Tỷ giá'),
    )

    # Tài khoản kế toán (Foreign Keys - non-nullable)
    tk_cc = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        related_name='khai_bao_thong_tin_ccdc_tk_cc',
        verbose_name=_('Tài khoản công cụ'),
    )
    tk_kh = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        related_name='khai_bao_thong_tin_ccdc_tk_kh',
        verbose_name=_('Tài khoản khấu hao'),
    )
    tk_cp = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        related_name='khai_bao_thong_tin_ccdc_tk_cp',
        verbose_name=_('Tài khoản chi phí'),
    )

    # Giá trị ngoại tệ
    nguyen_gia_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Nguyên giá (ngoại tệ)'),
    )
    gt_da_kh_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Giá trị đã khấu hao (ngoại tệ)'),
    )
    gt_cl_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Giá trị còn lại (ngoại tệ)'),
    )
    gt_kh_ky_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Giá trị khấu hao kỳ này (ngoại tệ)'),
    )

    # Giá trị VND
    nguyen_gia = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Nguyên giá'),
    )
    gt_da_kh = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Giá trị đã khấu hao'),
    )
    gt_cl = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Giá trị còn lại'),
    )
    gt_kh_ky = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Giá trị khấu hao kỳ này'),
    )

    # Thông tin bổ sung
    status = models.CharField(
        max_length=2, default="1", verbose_name=_('Trạng thái')
    )
    so_hieu_cc = models.CharField(
        max_length=50, blank=True, null=True, verbose_name=_('Số hiệu công cụ')
    )
    so_ct = models.ForeignKey(
        'django_ledger.ChungTu',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='khai_bao_thong_tin_ccdc_so_ct',
        verbose_name=_('Số chứng từ'),
    )

    # Thông tin nhóm công cụ (Foreign Keys to GroupModel)
    nh_cc1 = models.ForeignKey(
        'django_ledger.GroupModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='khai_bao_thong_tin_ccdc_nh_cc1',
        verbose_name=_('Nhóm công cụ 1'),
    )
    nh_cc2 = models.ForeignKey(
        'django_ledger.GroupModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='khai_bao_thong_tin_ccdc_nh_cc2',
        verbose_name=_('Nhóm công cụ 2'),
    )
    nh_cc3 = models.ForeignKey(
        'django_ledger.GroupModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='khai_bao_thong_tin_ccdc_nh_cc3',
        verbose_name=_('Nhóm công cụ 3'),
    )

    # Thông tin sản xuất
    nuoc_sx = models.CharField(
        max_length=100, blank=True, null=True, verbose_name=_('Nước sản xuất')
    )
    nam_sx = models.IntegerField(
        default=0,
        null=True,
        blank=True,
        validators=[MinValueValidator(0)],
        verbose_name=_('Năm sản xuất'),
    )

    # Ghi chú
    ghi_chu = models.TextField(blank=True, null=True, verbose_name=_('Ghi chú'))
    objects = KhaiBaoThongTinCCDCModelManager.from_queryset(
        KhaiBaoThongTinCCDCModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _('Khai Báo Thông Tin CCDC')
        verbose_name_plural = _('Khai Báo Thông Tin CCDC')
        indexes = [
            models.Index(fields=['entity_model']),
            models.Index(fields=['ma_cc']),
            models.Index(fields=['ma_lcc']),
            models.Index(fields=['ngay_ct']),
            models.Index(fields=['status']),
        ]

    def __str__(self):  # noqa: C901
        return f'{self.ma_cc}: {self.ten_cc}'


class KhaiBaoThongTinCCDCModel(KhaiBaoThongTinCCDCModelAbstract):
    """
    Base Tool Information Declaration Model Implementation
    """

    class Meta(KhaiBaoThongTinCCDCModelAbstract.Meta):
        abstract = False
        db_table = 'khai_bao_thong_tin_ccdc'
