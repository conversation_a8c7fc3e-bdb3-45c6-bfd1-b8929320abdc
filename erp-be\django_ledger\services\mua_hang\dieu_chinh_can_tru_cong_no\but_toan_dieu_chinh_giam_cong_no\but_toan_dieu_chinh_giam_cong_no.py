"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for ButToanDieuChinhGiamCongNoMuaHang and ChiTietButToanDieuChinhGiamCongNoMuaHang models.
"""

from typing import Any, Dict, List, Optional
from uuid import UUID

from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import QuerySet
from django.utils.translation import gettext_lazy as _

from django_ledger.models.mua_hang.dieu_chinh_can_tru_cong_no import (
    ButToanDieuChinhGiamCongNoMuaHangModel,
    ChiTietButToanDieuChinhGiamCongNoMuaHangModel,
)
from django_ledger.repositories.mua_hang.dieu_chinh_can_tru_cong_no.but_toan_dieu_chinh_giam_cong_no.but_toan_dieu_chinh_giam_cong_no import (
    ButToanDieuChinhGiamC<PERSON>NoRepository,
)
from django_ledger.services.base import BaseService
from django_ledger.utils_new.debt_management.cong_no_creation import (  # noqa: F401,
    CongNoCreation,
)


class ButToanDieuChinhGiamCongNoService(BaseService):
    """
    Service class for ButToanDieuChinhGiamCongNoMuaHangModel.
    Handles business logic for the debt adjustment accounting entry model.
    """

    # ✅ PREDEFINED CONFIGURATION: Bút toán điều chỉnh giảm công nợ accounting mappings
    SALES_INVOICE_ACCOUNTING_CONFIG = [
        {
            'journal_type': 'CHINCC',  # Thu từ khách hàng (ma_ngv=2)
            'debit_account_field': 'tk_no',  # Tài khoản nợ - DEBIT
            'credit_account_field': 'tk',  # Tài khoản có - CREDIT
            'debit_account_source': 'detail',  # Lấy debit account từ header
            'credit_account_source': 'header',  # Lấy credit account từ detail
            'amount_field': 'tien_nt',  # Thành tiền (detail)
            'detail_source': 'chi_tiet_items',  # Related name
            'detail_conditions': {
                'tien_nt': {'gt': 0},
                'tk_no': {'is_not_null': True},
            },
            'canCreate': True,  # Default: always create entry
        },
        {
            'journal_type': 'CHIHD',  # Thu từ hóa đơn (ma_ngv=1)
            'debit_account_field': 'tk_no',  # Tài khoản nợ - DEBIT
            'credit_account_field': 'tk',  # Tài khoản có - CREDIT
            'debit_account_source': 'detail',  # Lấy debit account từ header
            'credit_account_source': 'header',  # Lấy credit account từ detail
            'amount_field': 'tien_nt',  # Thành tiền (detail)
            'detail_source': 'chi_tiet_items',  # Related name
            'detail_conditions': {
                'tien_nt': {'gt': 0},
                'tk_no': {'is_not_null': True},
            },
            'canCreate': True,  # Default: always create entry
        },
    ]

    def __init__(self):
        """
        Initialize the service.
        """
        super().__init__()
        self.repository = ButToanDieuChinhGiamCongNoRepository()
        self._cong_no_service = CongNoCreation()

    def _determine_accounting_mappings(
        self, but_toan: ButToanDieuChinhGiamCongNoMuaHangModel
    ) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định accounting mappings dựa trên ma_ngv và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Conditional journal types dựa trên ma_ngv (1=CHIHD, 2=CHINCC)
        - Status-based creation (chỉ tạo khi status IN ('3', '5'))
        - Detail-level conditional creation (amount > 0 AND account IS NOT NULL)
        """
        mappings = self.get_accounting_configuration()
        status_valid = str(getattr(but_toan, 'status', '1')) in ['3', '5', '0']
        ma_ngv = getattr(but_toan, 'ma_ngv', '1')

        # Determine the journal type that should be active
        active_journal_type = 'CHINCC' if ma_ngv == '2' else 'CHIHD'

        # Set canCreate flag for all potential mappings
        for mapping in mappings:
            is_active_type = mapping['journal_type'] == active_journal_type
            mapping['canCreate'] = status_valid and is_active_type

        return mappings


    def get_accounting_configuration(self) -> List[Dict[str, Any]]:
        """
        Lấy cấu hình kế toán cho bút toán điều chỉnh giảm công nợ.
        Returns:
            List[Dict[str, Any]]: Danh sách mapping configuration
        """
        return self.SALES_INVOICE_ACCOUNTING_CONFIG.copy()

    def get_queryset(self, entity_slug: str) -> QuerySet:
        """
        Get the base queryset for the model.

        Parameters
        ----------
        entity_slug : str
            The entity slug.

        Returns
        -------
        QuerySet
            Base queryset for the model.
        """
        return self.repository.list(entity_slug=entity_slug)

    def list(self, entity_slug: str, **kwargs) -> QuerySet:
        """
        List all ButToanDieuChinhGiamCongNoMuaHangModel instances.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            QuerySet of ButToanDieuChinhGiamCongNoMuaHangModel instances.
        """
        return self.repository.list(entity_slug=entity_slug, **kwargs)

    def get_by_id(
        self, entity_slug: str, uuid: UUID
    ) -> Optional[ButToanDieuChinhGiamCongNoMuaHangModel]:
        """
        Get a ButToanDieuChinhGiamCongNoMuaHangModel instance by UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : UUID
            The UUID of the instance.

        Returns
        -------
        ButToanDieuChinhGiamCongNoMuaHangModel or None
            The instance if found, None otherwise.
        """
        return self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

    def get_with_details(self, entity_slug: str, uuid: UUID) -> Optional['ButToanDieuChinhGiamCongNoMuaHangModel']:
        """
        Get a ButToanDieuChinhGiamCongNoMuaHang with its related details.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : UUID
            The UUID of the ButToanDieuChinhGiamCongNoMuaHang

        Returns
        -------
        Optional[ButToanDieuChinhGiamCongNoMuaHangModel]
            The ButToanDieuChinhGiamCongNoMuaHang instance with related details or None if not found
        """
        try:
            # Get ButToanDieuChinhGiamCongNoMuaHang with prefetched chi_tiet_items
            but_toan = (
                self.repository.get_queryset()
                .filter(entity_model__slug=entity_slug, uuid=str(uuid))
                .prefetch_related('chi_tiet_items')
                .first()
            )

            if but_toan:
                # Add chi_tiet_items as list for compatibility
                but_toan.chi_tiet_items_list = list(but_toan.chi_tiet_items.all())

            return but_toan

        except Exception:
            return None

    @transaction.atomic
    def create(
        self,
        entity_slug: str,
        data: Dict[str, Any],
        child_data: List[Dict[str, Any]] = None,
    ) -> ButToanDieuChinhGiamCongNoMuaHangModel:
        """
        Create a new ButToanDieuChinhGiamCongNoMuaHangModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : dict
            The data to create the instance with.
        child_data : list, optional
            List of child item data.

        Returns
        -------
        ButToanDieuChinhGiamCongNoMuaHangModel
            The created instance.
        """
        # NGHIỆP VỤ: Validation đã được thực hiện ở serializer layer
        # Repository sẽ convert UUID strings thành model instances

        # Create the main instance
        instance = self.repository.create(entity_slug=entity_slug, data=data)

        # Create child items if provided
        if child_data:
            from django_ledger.services.mua_hang.dieu_chinh_can_tru_cong_no.but_toan_dieu_chinh_giam_cong_no.chi_tiet_but_toan_dieu_chinh_giam_cong_no import (
                ChiTietButToanDieuChinhGiamCongNoService,
            )

            child_service = ChiTietButToanDieuChinhGiamCongNoService()
            for i, child_item in enumerate(child_data, 1):
                child_item['but_toan'] = instance
                child_item['stt'] = child_item.get('stt', i)
                child_service.create(entity_slug=entity_slug, data=child_item)

        # Totals are saved from request data, no calculation needed
        # ✅ UNIFIED ACCOUNTING: Tạo bút toán kế toán
        # Only create accounting if ledger is not already assigned to avoid UNIQUE constraint
        if not instance.ledger:
            try:
                self._cong_no_service.create_document_accounting_entries(
                    source_document=instance,
                    document_type="Bút toán điều chỉnh giảm công nợ",
                    account_mappings=self._determine_accounting_mappings(instance),
                )
            except Exception as e:
                # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                # to maintain data consistency between ButToan and accounting entries
                raise Exception(
                    f"Failed to create accounting entry for ButToan {instance.so_ct}: {str(e)}"
                ) from e
                # Note: If ledger already exists, skip accounting creation to avoid duplicates

        return instance

    @transaction.atomic
    def update(
        self,
        entity_slug: str,
        uuid: UUID,
        data: Dict[str, Any],
        child_data: List[Dict[str, Any]] = None,
    ) -> Optional[ButToanDieuChinhGiamCongNoMuaHangModel]:
        """
        Update a ButToanDieuChinhGiamCongNoMuaHangModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : UUID
            The UUID of the instance to update.
        data : dict
            The data to update the instance with.
        child_data : list, optional
            List of child item data.

        Returns
        -------
        ButToanDieuChinhGiamCongNoMuaHangModel or None
            The updated instance if found, None otherwise.
        """
        # NGHIỆP VỤ: Validate business rules trước khi update
        if child_data is not None:
            self._validate_business_rules(entity_slug, data, child_data)

        instance = self.repository.update(entity_slug=entity_slug, uuid=uuid, data=data)

        if instance and child_data is not None:
            # Tạo savepoint để đảm bảo transaction safety
            sid = transaction.savepoint()
            try:
                # Backup old items trước khi xóa (để rollback nếu cần)
                old_items_data = list(instance.chi_tiet_items.values())

                # Delete existing child items
                instance.chi_tiet_items.all().delete()

                # Create new child items
                from django_ledger.services.mua_hang.dieu_chinh_can_tru_cong_no.but_toan_dieu_chinh_giam_cong_no.chi_tiet_but_toan_dieu_chinh_giam_cong_no import (
                    ChiTietButToanDieuChinhGiamCongNoService,
                )

                child_service = ChiTietButToanDieuChinhGiamCongNoService()
                for i, child_item in enumerate(child_data, 1):
                    child_item['but_toan'] = instance
                    child_item['stt'] = child_item.get('stt', i)
                    child_service.create(entity_slug=entity_slug, data=child_item)

                # Commit savepoint nếu thành công
                transaction.savepoint_commit(sid)
            except Exception as e:
                # Rollback savepoint nếu có lỗi
                transaction.savepoint_rollback(sid)
                raise ValidationError(f'Failed to update child items: {str(e)}')

            # Totals are saved from request data, no calculation needed

       # ✅ UNIFIED ACCOUNTING: Create or update accounting entries
        try:
            account_mappings = self._determine_accounting_mappings(instance)
            if instance.ledger:
                # UPDATE existing entries
                self._cong_no_service.update_document_accounting_entries(
                    source_document=instance,
                    document_type="bút toán điều chỉnh giảm công nợ",
                    account_mappings=account_mappings,
                )
            else:
                # CREATE new entries if no ledger exists
                self._cong_no_service.create_document_accounting_entries(
                    source_document=instance,
                    document_type="bút toán điều chỉnh giảm công nợ",
                    account_mappings=account_mappings,
                )
        except Exception as e:
            # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
            # to maintain data consistency between ButToan and accounting entries
            raise Exception(
                f"Failed to update accounting entry for ButToan {instance.so_ct}: {str(e)}"
            ) from e

        return instance

    def delete(self, entity_slug: str, uuid: UUID) -> bool:
        """
        Delete a ButToanDieuChinhGiamCongNoMuaHangModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : UUID
            The UUID of the instance to delete.

        Returns
        -------
        bool
            True if the instance was deleted, False otherwise.
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not instance:
            return

        # If a ledger exists, delete its entries first.
        if instance.ledger:
            try:
                self._cong_no_service.delete_document_accounting_entries(
                    source_document=instance
                )
            except Exception as e:
                raise Exception(
                    f"Failed to delete accounting entries for {instance.so_ct}: {str(e)}"
                ) from e

        # The ledger's deletion will cascade and delete the main document.
        # If no ledger, delete the main document directly.
        if not instance.ledger:
            self.repository.delete(entity_slug=entity_slug, uuid=uuid)


    # _calculate_totals method removed - totals are saved from request data

    def _validate_business_rules(
        self,
        entity_slug: str,
        data: Dict[str, Any],
        child_data: List[Dict[str, Any]] = None,
    ):
        """
        Validate business rules for debt adjustment entries.
        NGHIỆP VỤ: Kiểm tra các quy tắc nghiệp vụ kế toán

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            Main entry data.
        child_data : List[Dict[str, Any]], optional
            Child entries data.
        """
        # 1. NGHIỆP VỤ: Phải có ít nhất 1 chi tiết
        if not child_data:
            raise ValidationError(_('At least one detail entry is required'))

        # 2. NGHIỆP VỤ: Kiểm tra khách hàng thuộc entity (nếu có)
        if data.get('ma_kh'):
            try:
                from django_ledger.models.customer import CustomerModel

                # Handle both UUID string and model instance
                ma_kh_value = data['ma_kh']
                if hasattr(ma_kh_value, 'uuid'):
                    # It's already a model instance
                    customer = ma_kh_value
                else:
                    # It's a UUID string
                    customer = CustomerModel.objects.get(uuid=ma_kh_value)

                if customer.entity_model.slug != entity_slug:
                    raise ValidationError(_('Customer does not belong to this entity'))
            except CustomerModel.DoesNotExist:
                raise ValidationError(_('Invalid customer'))

        # 3. NGHIỆP VỤ: Kiểm tra tỷ giá cho ngoại tệ
        if data.get('ma_nt'):
            try:
                from django_ledger.models.danh_muc import NgoaiTeModel

                # Handle both UUID string and model instance
                ma_nt_value = data['ma_nt']
                if hasattr(ma_nt_value, 'uuid'):
                    # It's already a model instance
                    ngoai_te = ma_nt_value
                else:
                    # It's a UUID string
                    ngoai_te = NgoaiTeModel.objects.get(uuid=ma_nt_value)

                if ngoai_te.ma_nt != 'VND' and not data.get('ty_gia'):
                    raise ValidationError(
                        _('Exchange rate is required for foreign currency')
                    )
            except NgoaiTeModel.DoesNotExist:
                raise ValidationError(_('Invalid currency'))

        # Detailed validation removed - just save data as provided

        # Balance validation removed - just save data as provided
