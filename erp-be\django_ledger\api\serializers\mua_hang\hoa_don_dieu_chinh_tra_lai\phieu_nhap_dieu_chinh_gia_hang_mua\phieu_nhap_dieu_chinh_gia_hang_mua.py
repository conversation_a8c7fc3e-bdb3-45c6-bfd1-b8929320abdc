"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for PhieuNhapDieuChinhGiaHangMua model.
"""

from django.utils.translation import gettext_lazy as _  # noqa: F401
from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers._utils.chung_tu_fields import ChungTuSerializerMixin
from django_ledger.api.serializers.accounts import AccountModelSerializer  # noqa: F401
from django_ledger.api.serializers.customer import (  # noqa: F401,
    CustomerModelSerializer,
)
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer  # noqa: F401,
from django_ledger.api.serializers.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_dieu_chinh_gia_hang_mua.chi_tiet_phieu_nhap_dieu_chinh_gia_hang_mua import (  # noqa: F401,
    ChiTietPhieuNhapDieuChinhGiaHangMuaSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_dieu_chinh_gia_hang_mua.thue_phieu_nhap_dieu_chinh_gia_hang_mua import (  # noqa: F401,
    ThuePhieuNhapDieuChinhGiaHangMuaSerializer,
)
from django_ledger.api.serializers.organization import (  # noqa: F401,
    BoPhanModelSerializer,
)
from django_ledger.models import PhieuNhapDieuChinhGiaHangMuaModel  # noqa: F401,


class PhieuNhapDieuChinhGiaHangMuaSerializer(
    ChungTuSerializerMixin, serializers.ModelSerializer
):
    """
    Serializer for PhieuNhapDieuChinhGiaHangMua model.
    Uses ChungTuSerializerMixin to automatically provide ChungTu fields.
    """

    # Read-only fields for related objects
    ma_kh_data = serializers.SerializerMethodField(read_only=True)

    tk_data = serializers.SerializerMethodField(read_only=True)

    ma_nt_data = serializers.SerializerMethodField(read_only=True)

    ma_bp_data = serializers.SerializerMethodField(read_only=True)  # Child data
    chi_tiet_data = serializers.SerializerMethodField(read_only=True)

    thue_data = serializers.SerializerMethodField(read_only=True)
    # Write-only fields for child data
    chi_tiet = serializers.ListField(required=False, write_only=True)

    thue = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = PhieuNhapDieuChinhGiaHangMuaModel
        fields = [
            'uuid',
            'entity_model',
            # ChungTu fields from ChungTuMixIn
            'i_so_ct',
            'ma_nk',
            'so_ct',
            'ngay_ct',
            'ngay_lct',
            # 'chung_tu',  # ✅ REMOVED: Causes JSON serialization errors
            'ma_ngv',
            'ma_kh',
            'ma_kh_data',
            'tk',
            'tk_data',
            'ma_tt',
            'unit_id',
            'ma_nt',
            'ma_nt_data',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_hd',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_cp0',
            'so_ct0',
            'so_ct2',
            'dien_giai',
            'xprogress',
            'xdatetime2',
            'status',
            'xfile',
            'id_progress',
            'ngay_ct0',
            'ty_gia',
            't_so_luong',
            't_tien_nt',
            't_tien',
            't_thue_nt',
            't_thue',
            't_tt_nt',
            't_tt',
            'transfer_yn',
            'chi_tiet_data',
            'thue_data',
            'chi_tiet',
            'thue',
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'ma_kh_data',
            'tk_data',
            'ma_nt_data',
            'ma_bp_data',
            'chi_tiet_data',
            'thue_data',
            'created',
            'updated',
        ]

    def get_ma_kh_data(self, obj):  # noqa: C901
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_tk_data(self, obj):  # noqa: C901
        """
        Get account data.
        """
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    def get_ma_nt_data(self, obj):  # noqa: C901
        """
        Get currency data.
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_ma_bp_data(self, obj):  # noqa: C901
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_chi_tiet_data(self, obj):  # noqa: C901
        """
        Get detail records data.
        """
        chi_tiet = obj.chi_tiet_list.filter(deleted__isnull=True)
        return ChiTietPhieuNhapDieuChinhGiaHangMuaSerializer(chi_tiet, many=True).data

    def get_thue_data(self, obj):  # noqa: C901
        """
        Get tax records data.
        """
        thue = obj.chi_tiet_thue_list.all()
        return ThuePhieuNhapDieuChinhGiaHangMuaSerializer(thue, many=True).data

    def validate(self, attrs):  # noqa: C901
        """
        Validate the serializer data.
        """
        # Check if this is an update or create operation
        is_update = self.instance is not None
        # Validate required fields for create operations
        if not is_update:
            required_fields = [
                'tk',
                'i_so_ct',
                'ngay_ct',
                'ngay_lct',
                'ngay_ct0',
                'status',
            ]
            for field in required_fields:
                if field not in attrs:
                    raise serializers.ValidationError(
                        {field: _('This field is required.')}
                    )

        # ✅ REMOVED: i_so_ct uniqueness validation
        # i_so_ct is now handled by ChungTuMixIn through chung_tu_item relationship
        # Uniqueness validation is handled by ChungTuMixIn logic during save()
        # No need to validate at serializer level since it's not a direct model field

        # Validate decimal fields are positive
        decimal_fields = [
            'ty_gia',
            't_so_luong',
            't_tien_nt',
            't_tien',
            't_thue_nt',
            't_thue',
            't_tt_nt',
            't_tt',
        ]
        for field in decimal_fields:
            if field in attrs and attrs[field] is not None and attrs[field] < 0:
                raise serializers.ValidationError(
                    {field: _('This field must be positive.')}
                )

        # Validate date relationships
        if 'ngay_ct' in attrs and 'ngay_lct' in attrs:
            if attrs['ngay_ct'] > attrs['ngay_lct']:
                raise serializers.ValidationError(
                    {'ngay_ct': _('Document date cannot be later than creation date.')}
                )

        return attrs

    def validate_chi_tiet(self, value):  # noqa: C901
        """
        Validate chi_tiet data.
        """
        if not isinstance(value, list):
            raise serializers.ValidationError(_('Chi tiet must be a list.'))

        # Validate line numbers are unique
        lines = []
        for item in value:
            if 'line' in item:
                line = item['line']
                if line in lines:
                    raise serializers.ValidationError(_('Line numbers must be unique.'))
                lines.append(line)

        return value

    def validate_thue(self, value):  # noqa: C901
        """
        Validate thue data.
        """
        if not isinstance(value, list):
            raise serializers.ValidationError(_('Thue must be a list.'))

        # Validate line numbers are unique
        lines = []
        for item in value:
            if 'line' in item:
                line = item['line']
                if line in lines:
                    raise serializers.ValidationError(_('Line numbers must be unique.'))
                lines.append(line)

        return value

    # def to_representation(self, instance):
    #     """
    #     ✅ ENHANCED: Comprehensive safe serialization with proper object handling.
    #     """
    #     try:
    #         # Call ChungTuSerializerMixin's to_representation first
    #         data = super().to_representation(instance)

    #         # ✅ CRITICAL: Deep clean all object references recursively
    #         def clean_object_references(obj):
    #             """Recursively clean object references to prevent JSON serialization errors."""
    #             if obj is None:
    #                 return None
    #             elif isinstance(obj, (str, int, float, bool)):
    #                 return obj
    #             elif isinstance(obj, (list, tuple)):
    #                 return [clean_object_references(item) for item in obj]
    #             elif isinstance(obj, dict):
    #                 cleaned_dict = {}
    #                 for key, value in obj.items():
    #                     cleaned_dict[key] = clean_object_references(value)
    #                 return cleaned_dict
    #             elif hasattr(obj, 'uuid'):
    #                 return str(obj.uuid)
    #             elif hasattr(obj, 'pk'):
    #                 return str(obj.pk)
    #             elif hasattr(obj, '__dict__'):
    #                 # This is a model instance, convert to string
    #                 return str(obj)
    #             else:
    #                 # Fallback: convert to string
    #                 return str(obj)

    #         # Apply deep cleaning to all data
    #         cleaned_data = clean_object_references(data)

    #         # ✅ ENSURE: Result is a proper dictionary
    #         if not isinstance(cleaned_data, dict):
    #             cleaned_data = {
    #                 'uuid': str(instance.uuid),
    #                 'so_ct': str(getattr(instance, 'so_ct', '')),
    #                 'dien_giai': str(instance.dien_giai),
    #                 'error': 'Data structure issue resolved'
    #             }

    #         return cleaned_data

    #     except Exception:
    #         # Fallback: return minimal safe representation
    #         return {
    #             'uuid': str(instance.uuid) if hasattr(instance, 'uuid') else None,
    #             'so_ct': str(getattr(instance, 'so_ct', '')),
    #             'dien_giai': str(instance.dien_giai) if hasattr(instance, 'dien_giai') else None,
    #             'error': 'Serialization error occurred'
    #         }
