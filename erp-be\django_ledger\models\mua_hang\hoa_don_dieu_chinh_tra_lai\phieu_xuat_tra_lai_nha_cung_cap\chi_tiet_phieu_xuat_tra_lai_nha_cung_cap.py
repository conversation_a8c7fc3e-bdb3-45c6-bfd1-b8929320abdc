"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietPhieuXuatTraLaiNhaCungCap (Supplier Return Note Detail) Model
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ChiTietPhieuXuatTraLaiNhaCungCapModelQueryset(models.QuerySet):
    """
    A custom defined QuerySet for the ChiTietPhieuXuatTraLaiNhaCungCapModel.
    """

    pass


class ChiTietPhieuXuatTraLaiNhaCungCapModelManager(models.Manager):
    """
    A custom defined ChiTietPhieuXuatTraLaiNhaCungCapModelManager that will act as an interface to handling the DB queries to the  # noqa: E501
    ChiTietPhieuXuatTraL<PERSON>haCungCapModel.
    """

    pass


class ChiTietPhieuXuatTraLaiNhaCungCapModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiTietPhieuXuatTraLaiNhaCungCapModel database will inherit from.  # noqa: E501
    The ChiTietPhieuXuatTraLaiNhaCungCapModel inherits functionality from the following MixIns:  # noqa: E501

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501

    phieu_xuat_tra_lai: PhieuXuatTraLaiNhaCungCapModel
        The supplier return note this detail belongs to.

    line: int
        The line number.

    ma_vt: VatTuModel
        The item reference.

    dvt: DonViTinhModel
        The unit of measure reference.

    ma_kho: KhoHangModel
        The warehouse reference.

    ma_lo: LoModel
        The batch reference.

    lo_yn: bool
        Whether the item has a batch.

    ma_vi_tri: ViTriModel
        The location reference.

    vi_tri_yn: bool
        Whether the item has a location.

    he_so: decimal
        The coefficient.

    qc_yn: bool
        Whether quality control is required.

    so_luong: decimal
        The quantity.

    gia_nt: decimal
        The foreign currency price.

    tien_nt: decimal
        The foreign currency amount.

    thue_nt: decimal
        The foreign currency tax.

    tk_vt: AccountModel
        The item account reference.

    ma_bp: str
        The department code.

    ma_vv: str
        The case code.

    ma_hd: str
        The contract code.

    ma_dtt: str
        The aggregation object code.

    ma_ku: str
        The area code.

    ma_phi: str
        The fee code.

    ma_sp: str
        The product code.

    ma_lsx: str
        The production order code.

    ma_cp0: str
        The cost code.

    gia: decimal
        The price.

    tien: decimal
        The amount.

    thue: decimal
        The tax.
    """

    uuid = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    phieu_xuat_tra_lai = models.ForeignKey(
        'django_ledger.PhieuXuatTraLaiNhaCungCapModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet',
        verbose_name=_('Phiếu Xuất Trả Lại Nhà Cung Cấp'),
    )
    line = models.IntegerField(_("Số thứ tự"))
    ma_vt = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã vật tư'),
        related_name='chi_tiet_phieu_xuat_tra_lai_nha_cung_cap',
    )
    dvt = models.ForeignKey(
        'django_ledger.DonViTinhModel',
        on_delete=models.CASCADE,
        verbose_name=_('Đơn vị tính'),
        related_name='chi_tiet_phieu_xuat_tra_lai_nha_cung_cap',
    )
    ma_kho = models.ForeignKey(
        'django_ledger.KhoHangModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã kho'),
        related_name='chi_tiet_phieu_xuat_tra_lai_nha_cung_cap',
    )
    ma_lo = models.ForeignKey(
        'django_ledger.LoModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã lô'),
        related_name='chi_tiet_phieu_xuat_tra_lai_nha_cung_cap',
        null=True,
        blank=True,
    )
    lo_yn = models.BooleanField(_("Có lô"), default=False)
    ma_vi_tri = models.ForeignKey(
        'django_ledger.ViTriKhoHangModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã vị trí'),
        related_name='chi_tiet_phieu_xuat_tra_lai_nha_cung_cap',
        null=True,
        blank=True,
    )
    vi_tri_yn = models.BooleanField(_("Có vị trí"), default=False)
    he_so = models.DecimalField(_("Hệ số"), max_digits=18, decimal_places=2)
    qc_yn = models.BooleanField(_("Kiểm tra chất lượng"), default=False)
    so_luong = models.DecimalField(_("Số lượng"), max_digits=18, decimal_places=2)
    gia_nt = models.DecimalField(_("Giá ngoại tệ"), max_digits=18, decimal_places=2)
    tien_nt = models.DecimalField(_("Tiền ngoại tệ"), max_digits=18, decimal_places=2)
    thue_nt = models.DecimalField(_("Thuế ngoại tệ"), max_digits=18, decimal_places=2)
    tk_vt = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('Tài khoản vật tư'),
        related_name='chi_tiet_phieu_xuat_tra_lai_nha_cung_cap_tk_vt',
    )
    ma_bp = models.ForeignKey(
        'django_ledger.BoPhanModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã bộ phận'),
        related_name='chi_tiet_phieu_xuat_tra_lai_nha_cung_cap_ma_bp',
        null=True,
        blank=True,
    )
    ma_vv = models.ForeignKey(
        'django_ledger.VuViecModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã vụ việc'),
        related_name='chi_tiet_phieu_xuat_tra_lai_nha_cung_cap_ma_vv',
        null=True,
        blank=True,
    )
    ma_hd = models.ForeignKey(
        'django_ledger.ContractModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã hợp đồng'),
        related_name='chi_tiet_phieu_xuat_tra_lai_nha_cung_cap_ma_hd',
        null=True,
        blank=True,
    )
    ma_dtt = models.ForeignKey(
        'django_ledger.DotThanhToanModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã đợt thanh toán'),
        related_name='chi_tiet_phieu_xuat_tra_lai_nha_cung_cap_ma_dtt',
        null=True,
        blank=True,
    )
    ma_ku = models.ForeignKey(
        'django_ledger.KheUocModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã khế ước'),
        related_name='chi_tiet_phieu_xuat_tra_lai_nha_cung_cap_ma_ku',
        null=True,
        blank=True,
    )
    ma_phi = models.ForeignKey(
        'django_ledger.PhiModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã phí'),
        related_name='chi_tiet_phieu_xuat_tra_lai_nha_cung_cap_ma_phi',
        null=True,
        blank=True,
    )
    ma_sp = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã sản phẩm'),
        related_name='chi_tiet_phieu_xuat_tra_lai_nha_cung_cap_ma_sp',
        null=True,
        blank=True,
    )
    ma_lsx = models.CharField(_("Mã lệnh sản xuất"), max_length=50, blank=True)
    ma_cp0 = models.ForeignKey(
        'django_ledger.ChiPhiKhongHopLeModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã chi phí không hợp lệ'),
        related_name='chi_tiet_phieu_xuat_tra_lai_nha_cung_cap_ma_cp0',
        null=True,
        blank=True,
    )
    gia = models.DecimalField(_("Giá"), max_digits=18, decimal_places=2)
    tien = models.DecimalField(_("Tiền"), max_digits=18, decimal_places=2)
    thue = models.DecimalField(_("Thuế"), max_digits=18, decimal_places=2)
    # Links to invoice and invoice detail by UUID stored as CharField
    id_hd = models.CharField(
        max_length=36,
        verbose_name=_("ID hóa đơn"),
        help_text=_("UUID of HoaDonMuaHangTrongNuocModel"),
        blank=True,
        null=True,
    )
    line_hd = models.CharField(
        max_length=36,
        verbose_name=_("Line hóa đơn"),
        help_text=_("UUID of ChiTietHoaDonMuaHangTrongNuocModel"),
        blank=True,
        null=True,
    )
    objects = ChiTietPhieuXuatTraLaiNhaCungCapModelManager.from_queryset(
        ChiTietPhieuXuatTraLaiNhaCungCapModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _('Chi Tiết Phiếu Xuất Trả Lại Nhà Cung Cấp')
        verbose_name_plural = _('Chi Tiết Phiếu Xuất Trả Lại Nhà Cung Cấp')
        indexes = [
            models.Index(fields=['phieu_xuat_tra_lai']),
            models.Index(fields=['ma_vt']),
            models.Index(fields=['ma_kho']),
        ]

    def __str__(self):  # noqa: C901
        return f'{self.phieu_xuat_tra_lai.so_ct} - {self.line}: {self.ma_vt}'


class ChiTietPhieuXuatTraLaiNhaCungCapModel(
    ChiTietPhieuXuatTraLaiNhaCungCapModelAbstract
):
    """
    Base Supplier Return Note Detail Model Implementation
    """

    class Meta(ChiTietPhieuXuatTraLaiNhaCungCapModelAbstract.Meta):
        abstract = False
        db_table = 'chi_tiet_phieu_xuat_tra_lai_nha_cung_cap'
