"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

View for HoaDonDieuChinhGiaHangBan (Price Adjustment Invoice) model.
"""

from datetime import datetime
from uuid import UUID

from django.db import transaction
from rest_framework import status, viewsets  # noqa: F401
from rest_framework.decorators import action  # noqa: F401
from rest_framework.permissions import IsAuthenticated  # noqa: F401
from rest_framework.response import Response  # noqa: F401
from rest_framework.serializers import ValidationError

from django_ledger.api.decorators import api_exception_handler  # noqa: F401
from django_ledger.api.serializers.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dieu_chinh_gia_hang_ban import (  # noqa: F401,
    ChiTietHoaDonDieuChinhGiaHangBanModelSerializer,
    HoaDonDieuChinhGiaHangBanModelCreateUpdateSerializer,
    HoaDonDieuChinhGiaHangBanModelSerializer,
)
from django_ledger.repositories.quyen_chung_tu.repository import QuyenChungTuRepository
from django_ledger.services.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dieu_chinh_gia_hang_ban import (  # noqa: F401,
    ChiTietHoaDonDieuChinhGiaHangBanService,
    HoaDonDieuChinhGiaHangBanService,
)


class HoaDonDieuChinhGiaHangBanViewSet(viewsets.ModelViewSet):
    """
    ViewSet for the HoaDonDieuChinhGiaHangBan model.
    """

    permission_classes = [IsAuthenticated]
    lookup_field = "uuid"
    http_method_names = [
        "get",
        "post",
        "put",
        "patch",
        "delete",
        "head",
        "options",
    ]

    def __init__(self, *args, **kwargs):  # noqa: C901
        super().__init__(*args, **kwargs)
        self.service = HoaDonDieuChinhGiaHangBanService()
        self.chi_tiet_service = ChiTietHoaDonDieuChinhGiaHangBanService()

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for the view.

        Returns
        -------
        QuerySet
            The queryset for the view.
        """
        entity_slug = self.kwargs.get("entity_slug")
        # Sort by created descending to show newest first
        return self.service.get_all(entity_slug).order_by("-created")

    def get_serializer_class(self):  # noqa: C901
        """
        Get the serializer class for the view.

        Returns
        -------
        Serializer
            The serializer class for the view.
        """
        if self.action in ["create", "update", "partial_update"]:
            return HoaDonDieuChinhGiaHangBanModelCreateUpdateSerializer
        return HoaDonDieuChinhGiaHangBanModelSerializer

    def get_serializer_context(self):  # noqa: C901
        """
        Get the serializer context.

        Returns
        -------
        dict
            The serializer context.
        """
        context = super().get_serializer_context()
        context['entity_slug'] = self.kwargs.get('entity_slug')
        return context

    @api_exception_handler
    def create(self, request, entity_slug=None):
        """
        Creates a new HoaDonDieuChinhGiaHangBanModel instance.
        """
        request_data = request.data.copy()  # Create a mutable copy
        serializer = self.get_serializer(data=request_data)
        serializer.is_valid(raise_exception=True)

        # Process validated data
        validated_data = serializer.validated_data.copy()

        # Get entity model instance
        from django_ledger.models import EntityModel

        try:
            entity_model = EntityModel.objects.get(slug=entity_slug)
        except EntityModel.DoesNotExist:
            return Response(
                {
                    "message": f"Entity with slug '{entity_slug}' not found",
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        # Extract chi_tiet data if present from validated_data
        chi_tiet_data = validated_data.pop("chi_tiet", [])

        # Remove any other related fields from validated_data
        if "chi_tiet_items" in validated_data:
            validated_data.pop("chi_tiet_items")

        # Add entity_model to validated_data
        validated_data["entity_model"] = entity_model

        # Use transaction to ensure data consistency
        with transaction.atomic():
            # First create the invoice with chi_tiet
            instance = self.service.create(entity_slug, validated_data, chi_tiet_data)

            # Reload the instance with related objects to ensure chi_tiet is included
            # Use prefetch_related to load chi_tiet objects
            instance = (
                self.get_queryset().prefetch_related('chi_tiet').get(uuid=instance.uuid)
            )

            # Use the read serializer for response to include chi_tiet
            response_serializer = HoaDonDieuChinhGiaHangBanModelSerializer(instance)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update a HoaDonDieuChinhGiaHangBan instance.
        """
        partial = kwargs.pop(
            'partial', True
        )  # Default to partial=True for both PUT and PATCH
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        # Use the read serializer for response to include chi_tiet
        response_serializer = HoaDonDieuChinhGiaHangBanModelSerializer(
            serializer.instance
        )
        return Response(response_serializer.data)

    @api_exception_handler
    def partial_update(self, request, *args, **kwargs):  # noqa: C901
        """
        Partially update a HoaDonDieuChinhGiaHangBan instance.
        """
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)

    @api_exception_handler
    def perform_update(self, serializer):  # noqa: C901
        """
        Perform the update operation.

        Parameters
        ----------
        serializer : Serializer
            The serializer instance.
        """
        instance = self.get_object()
        validated_data = serializer.validated_data
        entity_slug = self.kwargs.get("entity_slug")

        # Use update_with_details method like hoa_don_ban_hang
        instance = self.service.update_with_details(
            entity_slug=entity_slug, uuid=instance.uuid, data=validated_data
        )

        # Reload the instance with related objects to ensure chi_tiet is included
        instance = (
            self.get_queryset().prefetch_related('chi_tiet').get(uuid=instance.uuid)
        )

        serializer.instance = instance

    @api_exception_handler
    def perform_destroy(self, instance):  # noqa: C901
        """
        Perform the destroy operation.

        Parameters
        ----------
        instance : HoaDonDieuChinhGiaHangBanModel
            The instance to destroy.
        """
        self.service.delete(instance.uuid)

    @action(detail=True, methods=["get"])
    @api_exception_handler
    def chi_tiet(self, request, entity_slug=None, uuid=None):  # noqa: F811,
        """
        Get the chi_tiet for a HoaDonDieuChinhGiaHangBan.

        Parameters
        ----------
        request : Request
            The request object.
        entity_slug : str
            The entity slug.
        uuid : str
            The UUID of the HoaDonDieuChinhGiaHangBan.

        Returns
        -------
        Response
            The response object.
        """
        chi_tiet_list = self.chi_tiet_service.get_by_invoice(uuid)
        serializer = ChiTietHoaDonDieuChinhGiaHangBanModelSerializer(
            chi_tiet_list, many=True
        )

        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=False, methods=["get"])
    @api_exception_handler
    def search(self, request, entity_slug=None):  # noqa: F811,
        """
        Search for HoaDonDieuChinhGiaHangBan instances.

        Parameters
        ----------
        request : Request
            The request object.
        entity_slug : str
            The entity slug.

        Returns
        -------
        Response
            The response object.
        """
        query = request.query_params.get("q", "")
        queryset = self.service.search(entity_slug, query)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
