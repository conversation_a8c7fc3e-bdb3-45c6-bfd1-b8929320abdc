from django.utils.translation import gettext_lazy as _  # noqa: F401
from drf_spectacular.utils import (  # noqa: F401
    extend_schema,
    extend_schema_view,
)
from rest_framework import status  # noqa: F401
from rest_framework.decorators import action  # noqa: F401
from rest_framework.exceptions import ValidationError  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.serializers.quyen_chung_tu import (  # noqa: F401
    QuyenChungTuChiTietSerializer,
    QuyenChungTuCreateUpdateSerializer,
    QuyenChungTuDetailSerializer,
    QuyenChungTuListSerializer,
    QuyenChungTuNgaySerializer,
)
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401
from django_ledger.api.views.common import ERPPagination

from django_ledger.services.quyen_chung_tu.chi_tiet_service import (  # noqa: F401,
    QuyenChungTuChiTietModelService,
)
from django_ledger.services.quyen_chung_tu.ngay_service import (  # noqa: F401,
    QuyenChungTuNgayModelService,
)
from django_ledger.services.quyen_chung_tu.service import (  # noqa: F401,
    QuyenChungTuModelService,
)


@extend_schema_view(
    list=extend_schema(
        summary='List Document Permissions',
        description='Get a list of Document Permissions for a specific Entity',
        tags=['Document Permissions'],
    ),
    retrieve=extend_schema(
        summary='Retrieve Document Permission',
        description='Retrieve a specific Document Permission by UUID',
        tags=['Document Permissions'],
    ),
    create=extend_schema(
        summary='Create Document Permission',
        description='Create a new Document Permission for a specific Entity',
        tags=['Document Permissions'],
    ),
    update=extend_schema(
        summary='Update Document Permission',
        description='Update a specific Document Permission by UUID',
        tags=['Document Permissions'],
    ),
    partial_update=extend_schema(
        summary='Partially Update Document Permission',
        description='Partially update a specific Document Permission by UUID',
        tags=['Document Permissions'],
    ),
    destroy=extend_schema(
        summary='Delete Document Permission',
        description='Delete a specific Document Permission by UUID',
        tags=['Document Permissions'],
    ),
)
class QuyenChungTuViewSet(EntityRelatedViewSet):
    """
    ViewSet for QuyenChungTu model.
    """

    http_method_names = ['get', 'post', 'put', 'patch', 'delete']
    pagination_class = ERPPagination

    def get_service(self):  # noqa: C901
        """
        Returns an instance of QuyenChungTuModelService.
        """
        return QuyenChungTuModelService(
            entity_slug=self.kwargs.get('entity_slug'),
            user_model=self.request.user,
        )

    def get_chi_tiet_service(self):  # noqa: C901
        """
        Returns an instance of QuyenChungTuChiTietModelService.
        """
        return QuyenChungTuChiTietModelService(
            entity_slug=self.kwargs.get('entity_slug'),
            user_model=self.request.user,
        )

    def get_ngay_service(self):  # noqa: C901
        """
        Returns an instance of QuyenChungTuNgayModelService.
        """
        return QuyenChungTuNgayModelService(
            entity_slug=self.kwargs.get('entity_slug'),
            user_model=self.request.user,
        )

    def get_serializer_class(self):  # noqa: C901
        """
        Returns the appropriate serializer class based on the request method.
        """
        if self.action == 'list':
            return QuyenChungTuListSerializer
        elif self.action == 'retrieve':
            return QuyenChungTuDetailSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return QuyenChungTuCreateUpdateSerializer
        elif self.action == 'add_document_type':
            return QuyenChungTuChiTietSerializer
        elif self.action == 'track_document_number':
            return QuyenChungTuNgaySerializer
        return QuyenChungTuDetailSerializer

    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        List all document permissions for a specific entity.
        """
        entity_slug = self.kwargs.get('entity_slug')
        active_only = (
            request.query_params.get('active_only', 'false').lower() == 'true'
        )
        service = self.get_service()
        if active_only:
            queryset = service.get_active_quyen_chung_tu(entity_slug=entity_slug)
        else:
            queryset = service.get_all_quyen_chung_tu(entity_slug=entity_slug)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def retrieve(self, request, *args, **kwargs):  # noqa: C901
        """
        Retrieve a specific document permission by UUID.
        """
        entity_slug = self.kwargs.get('entity_slug')
        uuid = self.kwargs.get('pk')
        service = self.get_service()
        quyen_chung_tu = service.get_quyen_chung_tu(
            entity_slug=entity_slug, uuid=uuid
        )
        if not quyen_chung_tu:
            return Response(
                {'detail': _('Document permission not found')},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.get_serializer(quyen_chung_tu)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new document permission for a specific entity.
        """
        entity_slug = self.kwargs.get('entity_slug')
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            try:
                service = self.get_service()
                quyen_chung_tu = service.create_quyen_chung_tu(
                    entity_slug=entity_slug,
                    data=serializer.validated_data,
                )

                detail_serializer = QuyenChungTuDetailSerializer(quyen_chung_tu)
                return Response(
                    detail_serializer.data, status=status.HTTP_201_CREATED
                )
            except ValueError as e:
                return Response(
                    {'detail': str(e)}, status=status.HTTP_400_BAD_REQUEST
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update a specific document permission by UUID.
        """
        entity_slug = self.kwargs.get('entity_slug')
        uuid = self.kwargs.get('pk')
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            try:
                service = self.get_service()
                quyen_chung_tu = service.update_quyen_chung_tu(
                    entity_slug=entity_slug,
                    uuid=uuid,
                    data=serializer.validated_data,
                )

                if not quyen_chung_tu:
                    return Response(
                        {'detail': _('Document permission not found')},
                        status=status.HTTP_404_NOT_FOUND,
                    )

                detail_serializer = QuyenChungTuDetailSerializer(quyen_chung_tu)
                return Response(detail_serializer.data)
            except ValueError as e:
                return Response(
                    {'detail': str(e)}, status=status.HTTP_400_BAD_REQUEST
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def partial_update(self, request, *args, **kwargs):  # noqa: C901
        """
        Partially update a specific document permission by UUID.
        """
        entity_slug = self.kwargs.get('entity_slug')
        uuid = self.kwargs.get('pk')
        # Get current document permission
        service = self.get_service()
        quyen_chung_tu = service.get_quyen_chung_tu(
            entity_slug=entity_slug, uuid=uuid
        )
        if not quyen_chung_tu:
            return Response(
                {'detail': _('Document permission not found')},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Create serializer with current data and partial update
        serializer = self.get_serializer(
            quyen_chung_tu, data=request.data, partial=True
        )

        if serializer.is_valid():
            try:
                # Extract validated data including danh_sach_chung_tu if present
                update_data = serializer.validated_data
                quyen_chung_tu = service.update_quyen_chung_tu(
                    entity_slug=entity_slug, uuid=uuid, data=update_data
                )

                detail_serializer = QuyenChungTuDetailSerializer(quyen_chung_tu)
                return Response(detail_serializer.data)
            except ValueError as e:
                return Response(
                    {'detail': str(e)}, status=status.HTTP_400_BAD_REQUEST
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a specific document permission by UUID.
        """
        entity_slug = self.kwargs.get('entity_slug')
        uuid = self.kwargs.get('pk')
        service = self.get_service()
        success = service.delete_quyen_chung_tu(
            entity_slug=entity_slug, uuid=uuid
        )
        if not success:
            return Response(
                {'detail': _('Document permission not found')},
                status=status.HTTP_404_NOT_FOUND,
            )

        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=['post'], url_path='add-document-type')
    def add_document_type(self, request, entity_slug, pk=None):  # noqa: F811,
        """
        Add a document type to a document permission.
        """
        service = self.get_service()
        serializer = QuyenChungTuChiTietSerializer(data=request.data)
        if serializer.is_valid():
            try:
                chi_tiet = service.add_document_type(
                    entity_slug=entity_slug,
                    quyen_chung_tu_uuid=pk,
                    data=serializer.validated_data,
                )

                return Response(
                    QuyenChungTuChiTietSerializer(chi_tiet).data,
                    status=status.HTTP_201_CREATED,
                )
            except ValueError as e:
                return Response(
                    {'detail': str(e)}, status=status.HTTP_400_BAD_REQUEST
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], url_path='track-document-number')
    def track_document_number(
        self, request, entity_slug, pk=None
    ):  # noqa: F811,
        """
        Track document number for a specific date.
        """
        service = self.get_service()
        serializer = QuyenChungTuNgaySerializer(data=request.data)
        if serializer.is_valid():
            try:
                ngay = service.track_document_number(
                    entity_slug=entity_slug,
                    quyen_chung_tu_uuid=pk,
                    data=serializer.validated_data,
                )

                return Response(
                    QuyenChungTuNgaySerializer(ngay).data,
                    status=status.HTTP_201_CREATED,
                )
            except ValueError as e:
                return Response(
                    {'detail': str(e)}, status=status.HTTP_400_BAD_REQUEST
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'], url_path='document-types')
    def get_document_types(self, request, entity_slug, pk=None):  # noqa: F811,
        """
        Get all document types for a specific document permission.
        """
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 20))
        service = self.get_chi_tiet_service()
        queryset, pagination_info = service.get_all_chi_tiet(
            entity_slug=entity_slug,
            quyen_chung_tu_uuid=pk,
            page=page,
            page_size=page_size,
        )

        serializer = QuyenChungTuChiTietSerializer(queryset, many=True)
        return Response(
            {
                'count': pagination_info['count'],
                'next': pagination_info['next'],
                'previous': pagination_info['previous'],
                'results': serializer.data,
            }
        )

    @action(detail=True, methods=['get'], url_path='document-dates')
    def get_document_dates(self, request, entity_slug, pk=None):  # noqa: F811,
        """
        Get all document dates for a specific document permission.
        """
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 20))
        service = self.get_ngay_service()
        queryset, pagination_info = service.get_all_ngay(
            entity_slug=entity_slug,
            quyen_chung_tu_uuid=pk,
            page=page,
            page_size=page_size,
        )

        serializer = QuyenChungTuNgaySerializer(queryset, many=True)
        return Response(
            {
                'count': pagination_info['count'],
                'next': pagination_info['next'],
                'previous': pagination_info['previous'],
                'results': serializer.data,
            }
        )

    @action(
        detail=False,
        methods=['get'],
        url_path='by-document-type/(?P<ma_ct>[^/.]+)',
    )
    def get_by_document_type(
        self, request, entity_slug, ma_ct=None
    ):  # noqa: F811,
        """
        Get all document permissions for a specific document type.
        """
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 20))
        service = self.get_service()
        queryset, pagination_info = service.get_quyen_chung_tu_by_ma_ct(
            entity_slug=entity_slug,
            ma_ct=ma_ct,
            page=page,
            page_size=page_size,
        )

        serializer = QuyenChungTuListSerializer(queryset, many=True)
        return Response(
            {
                'count': pagination_info['count'],
                'next': pagination_info['next'],
                'previous': pagination_info['previous'],
                'results': serializer.data,
            }
        )

    @action(
        detail=False,
        methods=['get'],
        url_path='by-document-types',
    )
    def get_by_document_types(
        self, request, entity_slug
    ):  # noqa: F811,
        """
        Get all document permissions for multiple document types.

        Query Parameters:
        - ma_ct: Comma-separated list of document type codes (e.g., "HD1,HD2")
        - page: Page number (default: 1)
        - page_size: Number of items per page (default: 20)

        Example: /api/entities/tutimi-dnus2xnc/erp/document-books/by-document-types?ma_ct=HD1,HD2&page_size=10
        """
        ma_ct_param = request.query_params.get('ma_ct', '')
        if not ma_ct_param:
            return Response(
                {'error': 'ma_ct parameter is required (e.g., ma_ct=HD1,HD2)'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Parse comma-separated ma_ct values
        ma_ct_list = [ma_ct.strip() for ma_ct in ma_ct_param.split(',') if ma_ct.strip()]
        if not ma_ct_list:
            return Response(
                {'error': 'At least one ma_ct value is required'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 20))
        service = self.get_service()
        queryset, pagination_info = service.get_quyen_chung_tu_by_ma_ct_list(
            entity_slug=entity_slug,
            ma_ct_list=ma_ct_list,
            page=page,
            page_size=page_size,
        )

        serializer = QuyenChungTuListSerializer(queryset, many=True)
        return Response(
            {
                'count': pagination_info['count'],
                'next': pagination_info['next'],
                'previous': pagination_info['previous'],
                'results': serializer.data,
            }
        )

    def get_quyen_chung_tu_by_ct(
        self, request, entity_slug
    ):  # noqa: F811,
        """
        Get all document permissions that have chi tiet pointing to a specific chung tu.

        This endpoint finds QuyenChungTu records that have QuyenChungTuChiTiet
        with foreign key pointing to the specified ChungTu.

        Query Parameters:
        - ma_ct: Document type code (alternative to chung_tu_uuid)
        - chung_tu_uuid: ChungTu UUID (alternative to ma_ct)
        - ngay_hl: Effective date filter (optional)

        Note: Either ma_ct or chung_tu_uuid is required.
        """
        try:
            # Get query parameters
            ma_ct = request.query_params.get('ma_ct')
            chung_tu_uuid = request.query_params.get('chung_tu_uuid')
            ngay_hl = request.query_params.get('ngay_hl')

            # Validate that at least one of ma_ct or chung_tu_uuid is provided
            if not ma_ct and not chung_tu_uuid:
                return Response(
                    {'error': 'Either ma_ct or chung_tu_uuid parameter is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            service = self.get_service()
            queryset = service.get_quyen_chung_tu_by_chung_tu(
                entity_slug=entity_slug,
                ma_ct=ma_ct,
                chung_tu_uuid=chung_tu_uuid,
                ngay_hl=ngay_hl,
            )

            serializer = QuyenChungTuListSerializer(queryset, many=True)
            return Response(
                {
                    'count': queryset.count(),
                    'results': serializer.data,
                }
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'], url_path='active')
    def get_active(self, request, entity_slug):  # noqa: C901
        """
        Get all active document permissions for a specific entity.
        """
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 20))
        service = self.get_service()
        queryset, pagination_info = service.get_active_quyen_chung_tu(
            entity_slug=entity_slug, page=page, page_size=page_size
        )

        serializer = QuyenChungTuListSerializer(queryset, many=True)
        return Response(
            {
                'count': pagination_info['count'],
                'next': pagination_info['next'],
                'previous': pagination_info['previous'],
                'results': serializer.data,
            }
        )

    @action(
        detail=False, methods=['get'], url_path='by-ma-nk/(?P<ma_nk>[^/.]+)'
    )
    def get_by_ma_nk(self, request, entity_slug, ma_nk=None):  # noqa: F811,
        """
        Get a document permission by ma_nk.
        """
        service = self.get_service()
        quyen_chung_tu = service.get_quyen_chung_tu_by_ma_nk(
            entity_slug=entity_slug, ma_nk=ma_nk
        )

        if not quyen_chung_tu:
            return Response(
                {'detail': _('Document permission not found')},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = QuyenChungTuDetailSerializer(quyen_chung_tu)
        return Response(serializer.data)
