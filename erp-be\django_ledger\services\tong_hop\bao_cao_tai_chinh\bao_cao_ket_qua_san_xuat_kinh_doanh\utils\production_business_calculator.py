"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Production Business Calculator for Bao Cao Ket Qua San Xuat Kinh Doanh.
Expert ERP Implementation - 20+ years experience.

Calculator for production and business results with date range comparison.
"""

import logging
from datetime import datetime, date
from decimal import Decimal
from typing import Dict, List, Any

from django.db.models import Sum
from django_ledger.models import EntityModel
from django_ledger.models.account_balance_audit import AccountBalanceAuditModel
from django_ledger.models.accounts import AccountModel
from django_ledger.models.chart_of_accounts import ChartOfAccountModel
from django_ledger.utils_new.accounting.audit_utils import get_total_movement_for_period
from .production_business_mapping import ProductionBusinessMapping

logger = logging.getLogger(__name__)


class ProductionBusinessCalculator:
    """
    Calculator for Production and Business Results Report.
    
    Expert ERP Implementation - 20+ years experience
    
    This calculator handles:
    - Production indicators calculation
    - Business results calculation  
    - Date range comparison (current vs previous period)
    - Account balance aggregation
    - Formula evaluation
    """

    def __init__(self, entity_uuid: str):
        """
        Initialize calculator for specific entity.

        Parameters
        ----------
        entity_uuid : str
            Entity UUID for calculations
        """
        self.entity_uuid = entity_uuid
        self.entity = EntityModel.objects.get(uuid=entity_uuid)
        self.mapping = ProductionBusinessMapping()

    def calculate_production_business_results(
        self,
        ngay_ct11: date, ngay_ct12: date,  # Current period
        ngay_ct01: date, ngay_ct02: date,  # Previous period
        **kwargs
    ) -> Dict[str, Dict[str, Any]]:
        """
        Calculate production and business results for both periods.

        This is the main entry point for the calculator, orchestrating the two-step calculation process.
        """
        try:
            # The service layer passes date objects, so no conversion is needed.
            current_start, current_end = ngay_ct11, ngay_ct12
            previous_start, previous_end = ngay_ct01, ngay_ct02

            all_indicators = self.mapping.get_all_indicators()
            calculated_results = {}

            # Step 1: Calculate all account-based indicators individually.
            for code, info in all_indicators.items():
                if not info.get('formula') and info.get('accounts'):
                    account_codes = info.get('accounts', [])
                    opposite_codes = info.get('tk_doi_ung')

                    ky_nay_val = get_total_movement_for_period(
                        entity_uuid=self.entity_uuid,
                        account_codes=account_codes,
                        from_date=current_start,
                        to_date=current_end,
                        opposite_account_codes=opposite_codes
                    )

                    ky_truoc_val = get_total_movement_for_period(
                        entity_uuid=self.entity_uuid,
                        account_codes=account_codes,
                        from_date=previous_start,
                        to_date=previous_end,
                        opposite_account_codes=opposite_codes
                    )

                    # Apply sign correction for expense accounts (Debit-positive).
                    if account_codes and account_codes[0].startswith(('6', '8')):
                        ky_nay_val = -ky_nay_val
                        ky_truoc_val = -ky_truoc_val

                    calculated_results[code] = {
                        'ky_nay': ky_nay_val,
                        'ky_truoc': ky_truoc_val
                    }

            # Step 2: Calculate all formula-based indicators.
            # This loop depends on the results from Step 1.
            for code, info in all_indicators.items():
                if info.get('formula'):
                    calculated_results[code] = {
                        'ky_nay': self._evaluate_formula(info['formula'], calculated_results, 'ky_nay'),
                        'ky_truoc': self._evaluate_formula(info['formula'], calculated_results, 'ky_truoc')
                    }

            # Step 3: Add indicator_info back for the data transformer.
            for code, values in calculated_results.items():
                values['indicator_info'] = all_indicators.get(code)

            return calculated_results

        except Exception as e:
            logger.error(f"Error in calculate_production_business_results: {e}", exc_info=True)
            return {}



    def _evaluate_formula(self, formula: str, calculated_values: Dict, period_key: str) -> Decimal:
        """
        Evaluates a formula string by substituting indicator codes with their pre-calculated values.

        This function is designed to work with the two-step calculation process, where all
        account-based values are computed before any formulas are evaluated.

        Args:
            formula (str): The formula string, e.g., '[01]-[02]'.
            calculated_values (Dict): The dictionary holding pre-calculated values.
            period_key (str): The period to use for the calculation ('ky_nay' or 'ky_truoc').

        Returns:
            Decimal: The calculated result of the formula.
        """
        try:
            import re
            eval_formula = formula

            # Find all indicator references like [01A], [10], etc.
            indicator_refs = re.findall(r'\[(\w+)\]', formula)

            for ref_code in indicator_refs:
                # Look up the pre-calculated value for the specific period.
                if ref_code in calculated_values and period_key in calculated_values[ref_code]:
                    value = calculated_values[ref_code][period_key]
                    eval_formula = eval_formula.replace(f'[{ref_code}]', str(value))
                else:
                    # If a referenced value is not found, default to 0 to avoid breaking the calculation.
                    eval_formula = eval_formula.replace(f'[{ref_code}]', '0')

            # Safely evaluate the final mathematical expression.
            # Remove any characters that are not part of the formula syntax for safety
            safe_formula = re.sub(r'[^0-9+\-*/.() ]', '', eval_formula)
            result = eval(safe_formula)
            return Decimal(str(result))

        except Exception as e:
            logger.error(f"Error evaluating formula '{formula}': {e}", exc_info=True)
            return Decimal('0')

    def get_indicator_metadata(self, indicator_code: str) -> Dict[str, Any]:
        """
        Get metadata for an indicator.
        
        Parameters
        ----------
        indicator_code : str
            Indicator code
            
        Returns
        -------
        Dict[str, Any]
            Indicator metadata
        """
        return self.mapping.get_indicator_by_code(indicator_code)
