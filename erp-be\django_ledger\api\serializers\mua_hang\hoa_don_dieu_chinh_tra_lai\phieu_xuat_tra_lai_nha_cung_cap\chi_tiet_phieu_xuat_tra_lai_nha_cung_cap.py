"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for ChiTietPhieuXuatTraLaiNhaCungCap (Supplier Return Note Detail) model.
"""

from rest_framework import serializers  # noqa: F401

# Import các serializer cần thiết cho các trường _data
from django_ledger.api.serializers.account import AccountModelSerializer  # noqa: F401
from django_ledger.api.serializers.contract import (  # noqa: F401,
    ContractModelSerializer,
)
from django_ledger.api.serializers.danh_muc import (  # noqa: F401,
    ChiPhiKhongHopLeSerializer,
    PhiSerializer,
)
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import (  # noqa: F401,
    KheUocModelSerializer,
)
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer  # noqa: F401,
from django_ledger.api.serializers.lo import LoModelSerializer  # noqa: F401,
from django_ledger.api.serializers.organization import (  # noqa: F401,
    BoPhanModelSerializer,
)
from django_ledger.api.serializers.tien_do_thanh_toan import (  # noqa: F401,
    DotThanhToanModelSerializer,
)
from django_ledger.api.serializers.vat_tu import VatTuSerializer  # noqa: F401,
from django_ledger.api.serializers.vi_tri_kho_hang import (  # noqa: F401,
    ViTriKhoHangModelSerializer,
)
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer  # noqa: F401,
from django_ledger.api.serializers.warehouse import (  # noqa: F401,
    KhoHangModelSerializer,
)

# Import model
from django_ledger.models.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_xuat_tra_lai_nha_cung_cap import (  # noqa: F401,
    ChiTietPhieuXuatTraLaiNhaCungCapModel,
)
from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_tiet_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401
    ChiTietHoaDonMuaHangTrongNuocModel,
)
from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401
    HoaDonMuaHangTrongNuocModel,
)


class ChiTietPhieuXuatTraLaiNhaCungCapSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietPhieuXuatTraLaiNhaCungCap model
    """

    phieu_xuat_tra_lai_uuid = serializers.UUIDField(
        source="phieu_xuat_tra_lai.uuid", read_only=True
    )

    # Reference data fields
    ma_vt_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    ma_kho_data = serializers.SerializerMethodField(read_only=True)
    ma_lo_data = serializers.SerializerMethodField(read_only=True)
    ma_vi_tri_data = serializers.SerializerMethodField(read_only=True)
    tk_vt_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)

    def get_ma_vt_data(self, obj):  # noqa: C901
        """Return product data if available"""
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_dvt_data(self, obj):  # noqa: C901
        """Return unit data if available"""
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_kho_data(self, obj):  # noqa: C901
        """Return warehouse data if available"""
        if obj.ma_kho:
            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def get_ma_lo_data(self, obj):  # noqa: C901
        """Return lot data if available"""
        if obj.ma_lo:
            return LoModelSerializer(obj.ma_lo).data
        return None

    def get_ma_vi_tri_data(self, obj):  # noqa: C901
        """Return position data if available"""
        if obj.ma_vi_tri:
            return ViTriKhoHangModelSerializer(obj.ma_vi_tri).data
        return None

    def get_tk_vt_data(self, obj):  # noqa: C901
        """Return account data if available"""
        if obj.tk_vt:
            return AccountModelSerializer(obj.tk_vt).data
        return None

    def get_ma_bp_data(self, obj):  # noqa: C901
        """Return department data if available"""
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):  # noqa: C901
        """Return case data if available"""
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):  # noqa: C901
        """Return contract data if available"""
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_dtt_data(self, obj):  # noqa: C901
        """Return payment progress data if available"""
        if obj.ma_dtt:
            return DotThanhToanModelSerializer(obj.ma_dtt).data
        return None

    def get_ma_ku_data(self, obj):  # noqa: C901
        """Return loan data if available"""
        if obj.ma_ku:
            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):  # noqa: C901
        """Return fee data if available"""
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_sp_data(self, obj):  # noqa: C901
        """Return product data if available"""
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):  # noqa: C901
        """Return invalid expense data if available"""
        if obj.ma_cp0:
            return ChiPhiKhongHopLeSerializer(obj.ma_cp0).data
        return None

    # Custom object responses
    id_hd = serializers.SerializerMethodField(read_only=True)
    line_hd = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietPhieuXuatTraLaiNhaCungCapModel
        fields = [
            "uuid",
            "phieu_xuat_tra_lai_uuid",
            "line",
            "id_hd",
            "line_hd",
            "ma_vt",
            "ma_vt_data",
            "dvt",
            "dvt_data",
            "ma_kho",
            "ma_kho_data",
            "ma_lo",
            "ma_lo_data",
            "lo_yn",
            "ma_vi_tri",
            "ma_vi_tri_data",
            "vi_tri_yn",
            "he_so",
            "qc_yn",
            "so_luong",
            "gia_nt",
            "tien_nt",
            "thue_nt",
            "tk_vt",
            "tk_vt_data",
            "ma_bp",
            "ma_bp_data",
            "ma_vv",
            "ma_vv_data",
            "ma_hd",
            "ma_hd_data",
            "ma_dtt",
            "ma_dtt_data",
            "ma_ku",
            "ma_ku_data",
            "ma_phi",
            "ma_phi_data",
            "ma_sp",
            "ma_sp_data",
            "ma_lsx",
            "ma_cp0",
            "ma_cp0_data",
            "gia",
            "tien",
            "thue",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "phieu_xuat_tra_lai_uuid",
            "id_hd",
            "line_hd",
            "created",
            "updated",
        ]

    def get_id_hd(self, obj):  # noqa: C901
        """
        Return object for invoice id: { uuid, so_ct }
        """
        if not getattr(obj, 'id_hd', None):
            return None
        try:
            hd = HoaDonMuaHangTrongNuocModel.objects.select_related(
                'chung_tu_item'
            ).get(uuid=obj.id_hd)

            so_ct = None
            if hasattr(hd, 'so_ct'):
                so_ct = hd.so_ct
            elif hasattr(hd, 'chung_tu_item') and hd.chung_tu_item:
                so_ct = getattr(hd.chung_tu_item, 'so_ct', None)
            return {"uuid": str(hd.uuid), "so_ct": so_ct}
        except HoaDonMuaHangTrongNuocModel.DoesNotExist:
            return None

    def get_line_hd(self, obj):  # noqa: C901
        """
        Return object for invoice line: { uuid, line }
        """
        if not getattr(obj, 'line_hd', None):
            return None
        try:
            line = ChiTietHoaDonMuaHangTrongNuocModel.objects.only('uuid', 'line').get(
                uuid=obj.line_hd
            )
            return {"uuid": str(line.uuid), "line": line.line, "sl_cl": line.sl_cl}
        except ChiTietHoaDonMuaHangTrongNuocModel.DoesNotExist:
            return None


class ChiTietPhieuXuatTraLaiNhaCungCapCreateUpdateSerializer(
    serializers.ModelSerializer
):
    """
    Serializer for creating and updating ChiTietPhieuXuatTraLaiNhaCungCap model
    """

    class Meta:
        model = ChiTietPhieuXuatTraLaiNhaCungCapModel
        fields = [
            "line",
            "id_hd",
            "line_hd",
            "ma_vt",
            "dvt",
            "ma_kho",
            "ma_lo",
            "lo_yn",
            "ma_vi_tri",
            "vi_tri_yn",
            "he_so",
            "qc_yn",
            "so_luong",
            "gia_nt",
            "tien_nt",
            "thue_nt",
            "tk_vt",
            "ma_bp",
            "ma_vv",
            "ma_hd",
            "ma_dtt",
            "ma_ku",
            "ma_phi",
            "ma_sp",
            "ma_lsx",
            "ma_cp0",
            "gia",
            "tien",
            "thue",
        ]

    def validate(self, data):  # noqa: C901
        # Normalize empty strings -> None for optional char fields
        for f in ['id_hd', 'line_hd']:
            if f in data and data[f] == "":
                data[f] = None
        return data


class ChiTietPhieuXuatTraLaiNhaCungCapNestedSerializer(serializers.ModelSerializer):
    """
    Serializer for nested ChiTietPhieuXuatTraLaiNhaCungCap model in PhieuXuatTraLaiNhaCungCap  # noqa: E501
    """

    class Meta:
        model = ChiTietPhieuXuatTraLaiNhaCungCapModel
        fields = [
            "line",
            "ma_vt",
            "dvt",
            "ma_kho",
            "ma_lo",
            "lo_yn",
            "ma_vi_tri",
            "vi_tri_yn",
            "he_so",
            "qc_yn",
            "so_luong",
            "gia_nt",
            "tien_nt",
            "thue_nt",
            "tk_vt",
            "ma_bp",
            "ma_vv",
            "ma_hd",
            "ma_dtt",
            "ma_ku",
            "ma_phi",
            "ma_sp",
            "ma_lsx",
            "ma_cp0",
            "gia",
            "tien",
            "thue",
        ]
