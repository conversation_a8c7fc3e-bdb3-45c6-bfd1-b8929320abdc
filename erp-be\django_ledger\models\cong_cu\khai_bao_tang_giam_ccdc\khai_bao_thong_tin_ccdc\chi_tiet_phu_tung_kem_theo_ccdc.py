"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietPhuTungKemTheoCCDC (Tool Accessory Detail) model implementation.
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ChiTietPhuTungKemTheoCCDCModelQueryset(QuerySet):
    """
    A custom defined ChiTietPhuTungKemTheoCCDCModelQueryset that will act as an interface to handling the DB queries to the  # noqa: E501
    ChiTietPhuTungKemTheoCCDCModel.
    """

    def for_khai_bao_thong_tin_ccdc(
        self, khai_bao_thong_tin_ccdc_id
    ):  # noqa: C901
        """
        Fetches a QuerySet of ChiTietPhuTungKemTheoCCDCModels for a specific KhaiBaoThongTinCCDCModel.  # noqa: E501

        Parameters
        __________
        khai_bao_thong_tin_ccdc_id: str or UUID
            The KhaiBaoThongTinCCDCModel UUID to filter by.
        """
        return self.filter(
            khai_bao_thong_tin_ccdc__uuid__exact=khai_bao_thong_tin_ccdc_id
        )


class ChiTietPhuTungKemTheoCCDCModelManager(Manager):
    """
    A custom defined ChiTietPhuTungKemTheoCCDCModelManager that will act as an interface to handling the DB queries to the  # noqa: E501
    ChiTietPhuTungKemTheoCCDCModel.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom ChiTietPhuTungKemTheoCCDCModelQueryset.
        """
        return ChiTietPhuTungKemTheoCCDCModelQueryset(
            self.model, using=self._db
        )

    def for_khai_bao_thong_tin_ccdc(
        self, khai_bao_thong_tin_ccdc_id
    ):  # noqa: C901
        """
        Returns ChiTietPhuTungKemTheoCCDCModel for a specific KhaiBaoThongTinCCDCModel.

        Parameters
        ----------
        khai_bao_thong_tin_ccdc_id: str or UUID
            The KhaiBaoThongTinCCDCModel UUID to filter by.

        Returns
        -------
        ChiTietPhuTungKemTheoCCDCModelQueryset
            A QuerySet of ChiTietPhuTungKemTheoCCDCModel with applied filters.
        """
        return self.get_queryset().for_khai_bao_thong_tin_ccdc(
            khai_bao_thong_tin_ccdc_id=khai_bao_thong_tin_ccdc_id
        )


class ChiTietPhuTungKemTheoCCDCModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiTietPhuTungKemTheoCCDCModel database will inherit from.  # noqa: E501
    The ChiTietPhuTungKemTheoCCDCModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501

    ma_cc: str
        The tool code.

    line: int
        The line number.

    dvt: str
        The unit of measure.

    so_luong: decimal
        The quantity.

    gia_tri_nt: decimal
        The value in foreign currency.

    gia_tri: decimal
        The value.
    """

    uuid = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        verbose_name=_('UUID'),
        help_text=_('Unique identifier for the record'),
    )
    khai_bao_thong_tin_ccdc = models.ForeignKey(
        'django_ledger.KhaiBaoThongTinCCDCModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet_phu_tung_kem_theo_ccdc',
        verbose_name=_('Khai báo thông tin CCDC'),
        help_text=_('Tool information declaration this detail belongs to'),
    )
    ma_cc = models.CharField(
        max_length=50,
        verbose_name=_('Mã công cụ'),
        help_text=_('Tool code'),
    )
    line = models.IntegerField(
        verbose_name=_('Số dòng'), help_text=_('Line number')
    )
    ten_ptcc = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_('Tên phụ tùng công cụ'),
        help_text=_('Tool accessory name'),
    )
    dvt = models.CharField(
        max_length=50,
        verbose_name=_('Đơn vị tính'),
        help_text=_('Unit of measure'),
    )
    so_luong = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name=_('Số lượng'),
        help_text=_('Quantity'),
    )
    gia_tri_nt = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        verbose_name=_('Giá trị ngoại tệ'),
        help_text=_('Value in foreign currency'),
    )
    gia_tri = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        verbose_name=_('Giá trị'),
        help_text=_('Value'),
    )

    objects = ChiTietPhuTungKemTheoCCDCModelManager.from_queryset(
        ChiTietPhuTungKemTheoCCDCModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _('Chi Tiết Phụ Tùng Kèm Theo CCDC')
        verbose_name_plural = _('Chi Tiết Phụ Tùng Kèm Theo CCDC')
        indexes = [
            models.Index(fields=['ma_cc']),
            models.Index(fields=['line']),
        ]

    def __str__(self):  # noqa: C901
        return f'CCDC: {self.ma_cc} - Dòng: {self.line}'


class ChiTietPhuTungKemTheoCCDCModel(ChiTietPhuTungKemTheoCCDCModelAbstract):
    """
    Base Tool Accessory Detail Model Implementation
    """

    class Meta(ChiTietPhuTungKemTheoCCDCModelAbstract.Meta):
        abstract = False
        db_table = 'chi_tiet_phu_tung_kem_theo_ccdc'
