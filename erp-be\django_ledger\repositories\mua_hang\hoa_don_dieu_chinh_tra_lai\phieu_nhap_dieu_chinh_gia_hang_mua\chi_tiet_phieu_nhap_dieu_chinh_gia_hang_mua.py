"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Repository for ChiTietPhieuNhapDieuChinhGiaHangMua model.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import Q, QuerySet  # noqa: F401

from django_ledger.models import (  # noqa: F401,
    ChiTietPhieuNhapDieuChinhGiaHangMuaModel,
    PhieuNhapDieuChinhGiaHangMuaModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class ChiTietPhieuNhapDieuChinhGiaHangMuaRepository(BaseRepository):
    """
    Repository class for handling ChiTietPhieuNhapDieuChinhGiaHangMua model database operations.  # noqa: E501
    Implements the Repository pattern for ChiTietPhieuNhapDieuChinhGiaHangMua.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the repository with the ChiTietPhieuNhapDieuChinhGiaHangMuaModel.
        """
        super().__init__(model_class=ChiTietPhieuNhapDieuChinhGiaHangMuaModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for ChiTietPhieuNhapDieuChinhGiaHangMuaModel.

        Returns
        -------
        QuerySet
            The base queryset for ChiTietPhieuNhapDieuChinhGiaHangMuaModel.
        """
        return self.model_class.objects.all().select_related(
            'phieu_nhap', 'ma_vt', 'dvt', 'ma_kho', 'tk_vt', 'ma_bp'
        )

    def get_by_id(  # noqa: C901
        self, phieu_nhap_uuid: Union[str, UUID], uuid: Union[str, UUID]
    ) -> Optional[ChiTietPhieuNhapDieuChinhGiaHangMuaModel]:
        """
        Retrieves a ChiTietPhieuNhapDieuChinhGiaHangMuaModel by its UUID.

        Parameters
        ----------
        phieu_nhap_uuid : Union[str, UUID]
            The parent PhieuNhapDieuChinhGiaHangMuaModel UUID.
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhieuNhapDieuChinhGiaHangMuaModel to retrieve.

        Returns
        -------
        Optional[ChiTietPhieuNhapDieuChinhGiaHangMuaModel]
            The ChiTietPhieuNhapDieuChinhGiaHangMuaModel with the given UUID, or None if not found.  # noqa: E501
        """
        try:
            return self.model_class.objects.filter(
                phieu_nhap__uuid=phieu_nhap_uuid
            ).get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def list_by_phieu_nhap(
        self, phieu_nhap_uuid: Union[str, UUID], **kwargs
    ) -> QuerySet:  # noqa: C901
        """
        Lists ChiTietPhieuNhapDieuChinhGiaHangMuaModel instances for a specific parent.

        Parameters
        ----------
        phieu_nhap_uuid : Union[str, UUID]
            The parent PhieuNhapDieuChinhGiaHangMuaModel UUID.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of ChiTietPhieuNhapDieuChinhGiaHangMuaModel instances.
        """
        qs = self.model_class.objects.filter(phieu_nhap__uuid=phieu_nhap_uuid)
        # Apply search if provided
        search_query = kwargs.get('search_query')
        if search_query:
            qs = qs.filter(
                Q(ma_vt__ma_vt__icontains=search_query)
                | Q(ma_vt__ten_vt__icontains=search_query)
                | Q(ma_kho__ten_kho__icontains=search_query)
            )

        # Apply material filter if provided
        ma_vt = kwargs.get('ma_vt')
        if ma_vt:
            qs = qs.filter(ma_vt__uuid=ma_vt)
        # Apply warehouse filter if provided
        ma_kho = kwargs.get('ma_kho')
        if ma_kho:
            qs = qs.filter(ma_kho__uuid=ma_kho)
        # Apply ordering
        return qs.order_by('line')

    def create(
        self, phieu_nhap, data: Dict[str, Any]
    ) -> ChiTietPhieuNhapDieuChinhGiaHangMuaModel:  # noqa: C901
        """
        Creates a new ChiTietPhieuNhapDieuChinhGiaHangMuaModel instance.

        Parameters
        ----------
        phieu_nhap : PhieuNhapDieuChinhGiaHangMuaModel
            The parent PhieuNhapDieuChinhGiaHangMuaModel instance.
        data : Dict[str, Any]
            The data for the new ChiTietPhieuNhapDieuChinhGiaHangMuaModel.

        Returns
        -------
        ChiTietPhieuNhapDieuChinhGiaHangMuaModel
            The created ChiTietPhieuNhapDieuChinhGiaHangMuaModel instance.
        """
        # Get the next line number if not provided
        if 'line' not in data:
            existing_lines = self.model_class.objects.filter(
                phieu_nhap=phieu_nhap
            ).order_by('-line')

            if existing_lines.exists():
                data['line'] = existing_lines.first().line + 1
            else:
                data['line'] = 1
        # Convert UUID strings to model instances
        data_copy = self.convert_uuids_to_model_instances(data)
        # Create the instance with entity_model from parent
        instance = self.model_class(
            phieu_nhap=phieu_nhap,
            entity_model=phieu_nhap.entity_model,
            **data_copy,
        )
        instance.save()

        return instance

    def update(  # noqa: C901
        self,
        phieu_nhap_uuid: Union[str, UUID],
        uuid: Union[str, UUID],
        data: Dict[str, Any],
    ) -> ChiTietPhieuNhapDieuChinhGiaHangMuaModel:
        """
        Updates an existing ChiTietPhieuNhapDieuChinhGiaHangMuaModel instance.

        Parameters
        ----------
        phieu_nhap_uuid : Union[str, UUID]
            The parent PhieuNhapDieuChinhGiaHangMuaModel UUID.
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhieuNhapDieuChinhGiaHangMuaModel to update.
        data : Dict[str, Any]
            The data to update.

        Returns
        -------
        ChiTietPhieuNhapDieuChinhGiaHangMuaModel
            The updated ChiTietPhieuNhapDieuChinhGiaHangMuaModel instance.

        Raises
        ------
        ChiTietPhieuNhapDieuChinhGiaHangMuaModel.DoesNotExist
            If the instance does not exist.
        """
        instance = self.model_class.objects.filter(
            phieu_nhap__uuid=phieu_nhap_uuid
        ).get(uuid=uuid)
        # Prevent overwriting the primary key
        if 'uuid' in data:
            del data['uuid']

        # Convert UUID strings to model instances
        data_copy = self.convert_uuids_to_model_instances(data)
        # Update fields
        for key, value in data_copy.items():
            setattr(instance, key, value)

        instance.save()
        return instance

    def delete(
        self, phieu_nhap_uuid: Union[str, UUID], uuid: Union[str, UUID]
    ) -> None:  # noqa: C901
        """
        Deletes a ChiTietPhieuNhapDieuChinhGiaHangMuaModel instance.

        Parameters
        ----------
        phieu_nhap_uuid : Union[str, UUID]
            The parent PhieuNhapDieuChinhGiaHangMuaModel UUID.
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhieuNhapDieuChinhGiaHangMuaModel to delete.

        Raises
        ------
        ChiTietPhieuNhapDieuChinhGiaHangMuaModel.DoesNotExist
            If the instance does not exist.
        """
        instance = self.model_class.objects.filter(
            phieu_nhap__uuid=phieu_nhap_uuid
        ).get(uuid=uuid)
        instance.delete()

    def bulk_create(
        self, phieu_nhap, data_list: List[Dict[str, Any]]
    ) -> List[ChiTietPhieuNhapDieuChinhGiaHangMuaModel]:  # noqa: C901
        """
        Creates multiple ChiTietPhieuNhapDieuChinhGiaHangMuaModel instances in bulk.

        Parameters
        ----------
        phieu_nhap : PhieuNhapDieuChinhGiaHangMuaModel
            The parent PhieuNhapDieuChinhGiaHangMuaModel instance.
        data_list : List[Dict[str, Any]]
            List of data dictionaries for the new instances.

        Returns
        -------
        List[ChiTietPhieuNhapDieuChinhGiaHangMuaModel]
            List of created ChiTietPhieuNhapDieuChinhGiaHangMuaModel instances.
        """
        # Get the current max line number
        existing_lines = self.model_class.objects.filter(
            phieu_nhap=phieu_nhap
        ).order_by('-line')

        max_line = existing_lines.first().line if existing_lines.exists() else 0
        instances = []
        for i, data in enumerate(data_list):
            # Set line number if not provided
            if 'line' not in data:
                data['line'] = max_line + i + 1
            # Convert UUID strings to model instances
            data_copy = self.convert_uuids_to_model_instances(data)
            instance = self.model_class(phieu_nhap=phieu_nhap, **data_copy)
            instances.append(instance)

        # Bulk create
        created_instances = self.model_class.objects.bulk_create(instances)
        return created_instances

    def delete_by_phieu_nhap(
        self, phieu_nhap_uuid: Union[str, UUID]
    ) -> None:  # noqa: C901
        """
        Deletes all ChiTietPhieuNhapDieuChinhGiaHangMuaModel instances for a specific parent.  # noqa: E501

        Parameters
        ----------
        phieu_nhap_uuid : Union[str, UUID]
            The parent PhieuNhapDieuChinhGiaHangMuaModel UUID.
        """
        self.model_class.objects.filter(phieu_nhap__uuid=phieu_nhap_uuid).delete()

    def get_by_line(
        self, phieu_nhap_uuid: Union[str, UUID], line: int
    ) -> Optional[ChiTietPhieuNhapDieuChinhGiaHangMuaModel]:  # noqa: C901
        """
        Retrieves a ChiTietPhieuNhapDieuChinhGiaHangMuaModel by its line number.

        Parameters
        ----------
        phieu_nhap_uuid : Union[str, UUID]
            The parent PhieuNhapDieuChinhGiaHangMuaModel UUID.
        line : int
            The line number.

        Returns
        -------
        Optional[ChiTietPhieuNhapDieuChinhGiaHangMuaModel]
            The ChiTietPhieuNhapDieuChinhGiaHangMuaModel with the given line number, or None if not found.  # noqa: E501
        """
        try:
            return self.model_class.objects.filter(
                phieu_nhap__uuid=phieu_nhap_uuid
            ).get(line=line)
        except self.model_class.DoesNotExist:
            return None
