"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Repository for ChiTietHoaDonDichVuTraLaiGiamGia (Service Invoice Return/Discount Detail) model.  # noqa: E501
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import QuerySet  # noqa: F401

# Import các model cần thiết
from django_ledger.models import (  # noqa: F401,
    AccountModel,
    BoPhanModel,
    ChiPhiKhongHopLeModel,
    ContractModel,
    DonViTinhModel,
    DotThanhToanModel,
    KheUocModel,
    PhiModel,
    VatTuModel,
    VuViecModel,
)
from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dich_vu_tra_lai_giam_gia import (  # noqa: F401,
    ChiTietHoaDonDichVuTraLaiGiamGiaModel,
    HoaDonDichVuTraLaiGiamGiaModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class ChiTietHoaDonDichVuTraLaiGiamGiaRepository(BaseRepository):
    """
    Repository class for ChiTietHoaDonDichVuTraLaiGiamGiaModel.
    Handles database operations for the model.
    """

    def __init__(self):  # noqa: C901
        super().__init__(model_class=ChiTietHoaDonDichVuTraLaiGiamGiaModel)

    def _clean_data(self, data: Dict[str, Any]) -> Dict[str, Any]:  # noqa: C901
        """
        Clean and prepare data for database operations.

        Parameters
        ----------
        data : Dict[str, Any]
            The data to clean.

        Returns
        -------
        Dict[str, Any]
            The cleaned data.
        """
        data_copy = data.copy()
        # Convert hoa_don UUID to HoaDonDichVuTraLaiGiamGiaModel instance
        if 'hoa_don' in data_copy and data_copy['hoa_don']:
            try:
                data_copy['hoa_don'] = HoaDonDichVuTraLaiGiamGiaModel.objects.get(
                    uuid=data_copy['hoa_don']
                )
            except (
                HoaDonDichVuTraLaiGiamGiaModel.DoesNotExist,
                ValueError,
                TypeError,
            ):
                pass

        # Convert tk_no UUID to AccountModel instance (if not already converted)
        if 'tk_no' in data_copy and data_copy['tk_no']:
            if not isinstance(data_copy['tk_no'], AccountModel):
                try:
                    data_copy['tk_no'] = AccountModel.objects.get(
                        uuid=data_copy['tk_no']
                    )
                except (AccountModel.DoesNotExist, ValueError, TypeError):
                    pass

        # Convert tk_thue_no UUID to AccountModel instance (if not already converted)
        if 'tk_thue_no' in data_copy and data_copy['tk_thue_no']:
            if not isinstance(data_copy['tk_thue_no'], AccountModel):
                try:
                    data_copy['tk_thue_no'] = AccountModel.objects.get(
                        uuid=data_copy['tk_thue_no']
                    )
                except (AccountModel.DoesNotExist, ValueError, TypeError):
                    pass

        # Convert dvt UUID to DonViTinhModel instance
        if 'dvt' in data_copy and data_copy['dvt']:
            try:
                data_copy['dvt'] = DonViTinhModel.objects.get(uuid=data_copy['dvt'])
            except (DonViTinhModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert ma_bp UUID to BoPhanModel instance
        if 'ma_bp' in data_copy and data_copy['ma_bp']:
            try:
                data_copy['ma_bp'] = BoPhanModel.objects.get(uuid=data_copy['ma_bp'])
            except (BoPhanModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert ma_vv UUID to VuViecModel instance
        if 'ma_vv' in data_copy and data_copy['ma_vv']:
            try:
                data_copy['ma_vv'] = VuViecModel.objects.get(uuid=data_copy['ma_vv'])
            except (VuViecModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert ma_hd UUID to ContractModel instance
        if 'ma_hd' in data_copy and data_copy['ma_hd']:
            try:
                data_copy['ma_hd'] = ContractModel.objects.get(uuid=data_copy['ma_hd'])
            except (ContractModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert ma_dtt UUID to DotThanhToanModel instance
        if 'ma_dtt' in data_copy and data_copy['ma_dtt']:
            try:
                data_copy['ma_dtt'] = DotThanhToanModel.objects.get(
                    uuid=data_copy['ma_dtt']
                )
            except (DotThanhToanModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert ma_ku UUID to KheUocModel instance
        if 'ma_ku' in data_copy and data_copy['ma_ku']:
            try:
                data_copy['ma_ku'] = KheUocModel.objects.get(uuid=data_copy['ma_ku'])
            except (KheUocModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert ma_phi UUID to PhiModel instance
        if 'ma_phi' in data_copy and data_copy['ma_phi']:
            try:
                data_copy['ma_phi'] = PhiModel.objects.get(uuid=data_copy['ma_phi'])
            except (PhiModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert ma_sp UUID to VatTuModel instance
        if 'ma_sp' in data_copy and data_copy['ma_sp']:
            try:
                data_copy['ma_sp'] = VatTuModel.objects.get(uuid=data_copy['ma_sp'])
            except (VatTuModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert ma_cp0 UUID to ChiPhiKhongHopLeModel instance
        if 'ma_cp0' in data_copy and data_copy['ma_cp0']:
            try:
                data_copy['ma_cp0'] = ChiPhiKhongHopLeModel.objects.get(
                    uuid=data_copy['ma_cp0']
                )
            except (ChiPhiKhongHopLeModel.DoesNotExist, ValueError, TypeError):
                pass

        return data_copy

    def convert_uuids_to_model_instances(
        self, data: Dict[str, Any]
    ) -> Dict[str, Any]:  # noqa: C901
        """
        Convert UUID strings to model instances for foreign key fields.
        This method extends the base implementation to specifically handle
        ChiTietHoaDonDichVuTraLaiGiamGia related fields.

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing UUID strings

        Returns
        -------
        Dict[str, Any]
            The data with UUID strings converted to model instances
        """
        # Create a copy and exclude CharField fields that should not be converted
        data_copy = data.copy()

        # Fields that are CharField and should NOT be converted to model instances
        char_fields = [
            'ten_thue',
            'ten_tk_thue',
            'dien_giai',
            'ma_lsx',
            'id_hd',
            'line_hd',
        ]

        # Temporarily remove CharField fields to prevent conversion
        char_field_values = {}
        for field in char_fields:
            if field in data_copy:
                char_field_values[field] = data_copy.pop(field)

        # Use the base implementation to handle common patterns (excluding CharField fields)
        data_copy = super().convert_uuids_to_model_instances(data_copy)

        # Handle specific fields that might not be covered by base implementation
        # Convert ma_dtt UUID to DotThanhToanModel instance
        if 'ma_dtt' in data_copy and data_copy['ma_dtt']:
            try:
                if isinstance(data_copy['ma_dtt'], str):
                    data_copy['ma_dtt'] = DotThanhToanModel.objects.get(
                        uuid=data_copy['ma_dtt']
                    )
            except (DotThanhToanModel.DoesNotExist, ValueError, TypeError):
                # If conversion fails, leave as is and let model validation handle it
                pass

        # Restore CharField fields with their original values
        data_copy.update(char_field_values)

        return data_copy

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for ChiTietHoaDonDichVuTraLaiGiamGiaModel.

        Returns
        -------
        QuerySet
            The base queryset for ChiTietHoaDonDichVuTraLaiGiamGiaModel.
        """
        return self.model_class.objects.all().select_related(
            'hoa_don',
            'tk_no',
            'tk_thue_no',
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_cp0',
        )

    def get_by_id(
        self, uuid: Union[str, UUID]
    ) -> Optional[ChiTietHoaDonDichVuTraLaiGiamGiaModel]:  # noqa: C901
        """
        Retrieves a ChiTietHoaDonDichVuTraLaiGiamGiaModel by its UUID.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietHoaDonDichVuTraLaiGiamGiaModel to retrieve.

        Returns
        -------
        Optional[ChiTietHoaDonDichVuTraLaiGiamGiaModel]
            The ChiTietHoaDonDichVuTraLaiGiamGiaModel with the given UUID, or None if not found.
        """
        try:
            return self.get_queryset().get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def get_by_hoa_don(self, hoa_don_uuid: UUID) -> QuerySet:  # noqa: C901
        """
        Get all ChiTietHoaDonDichVuTraLaiGiamGiaModel instances for a specific HoaDonDichVuTraLaiGiamGiaModel.  # noqa: E501

        Parameters
        ----------
        hoa_don_uuid : UUID
            The UUID of the HoaDonDichVuTraLaiGiamGiaModel to filter by.

        Returns
        -------
        QuerySet
            A queryset of ChiTietHoaDonDichVuTraLaiGiamGiaModel instances for the specified HoaDonDichVuTraLaiGiamGiaModel.  # noqa: E501
        """
        return self.get_queryset().filter(hoa_don__uuid=hoa_don_uuid)

    def create(
        self, hoa_don_id: Union[str, UUID], data: Dict[str, Any]
    ) -> ChiTietHoaDonDichVuTraLaiGiamGiaModel:  # noqa: C901
        """
        Create a new ChiTietHoaDonDichVuTraLaiGiamGiaModel instance.

        Parameters
        ----------
        hoa_don_id : Union[str, UUID]
            The UUID of the HoaDonDichVuTraLaiGiamGiaModel.
        data : Dict[str, Any]
            The data to create the ChiTietHoaDonDichVuTraLaiGiamGiaModel with.

        Returns
        -------
        ChiTietHoaDonDichVuTraLaiGiamGiaModel
            The created ChiTietHoaDonDichVuTraLaiGiamGiaModel instance.
        """
        # Make a copy to avoid modifying the original data
        data_copy = data.copy()

        # Remove uuid from data to prevent UNIQUE constraint violations
        # The model will auto-generate a new UUID
        data_copy.pop('uuid', None)

        # Convert UUIDs to model instances
        data_copy = self.convert_uuids_to_model_instances(data_copy)

        # Get the HoaDonDichVuTraLaiGiamGiaModel instance
        from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dich_vu_tra_lai_giam_gia.hoa_don_dich_vu_tra_lai_giam_gia import (
            HoaDonDichVuTraLaiGiamGiaModel,
        )

        try:
            hoa_don_instance = HoaDonDichVuTraLaiGiamGiaModel.objects.get(
                uuid=hoa_don_id
            )
        except HoaDonDichVuTraLaiGiamGiaModel.DoesNotExist:
            raise ValueError(
                f"HoaDonDichVuTraLaiGiamGiaModel with UUID {hoa_don_id} does not exist"
            )

        # Add hoa_don reference to data
        data_copy['hoa_don'] = hoa_don_instance

        # Create chi_tiet
        instance = self.model_class(**data_copy)
        instance.save()
        return instance

    def update(
        self, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[ChiTietHoaDonDichVuTraLaiGiamGiaModel]:  # noqa: C901
        """
        Update an existing ChiTietHoaDonDichVuTraLaiGiamGiaModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietHoaDonDichVuTraLaiGiamGiaModel to update.
        data : Dict[str, Any]
            The data to update the ChiTietHoaDonDichVuTraLaiGiamGiaModel with.

        Returns
        -------
        Optional[ChiTietHoaDonDichVuTraLaiGiamGiaModel]
            The updated ChiTietHoaDonDichVuTraLaiGiamGiaModel instance, or None if not found.
        """
        instance = self.get_by_id(uuid)
        if not instance:
            return None

        # Convert UUIDs to model instances
        data = self.convert_uuids_to_model_instances(data)

        for key, value in data.items():
            setattr(instance, key, value)

        instance.save()
        return instance

    def delete(self, uuid: UUID) -> bool:  # noqa: C901
        """
        Delete a ChiTietHoaDonDichVuTraLaiGiamGiaModel instance.

        Parameters
        ----------
        uuid : UUID
            The UUID of the ChiTietHoaDonDichVuTraLaiGiamGiaModel to delete.

        Returns
        -------
        bool
            True if the ChiTietHoaDonDichVuTraLaiGiamGiaModel was deleted, False otherwise.  # noqa: E501
        """
        instance = self.get_by_id(uuid)
        if not instance:
            return False

        instance.delete()
        return True

    def bulk_create(
        self, data_list: List[Dict[str, Any]]
    ) -> List[ChiTietHoaDonDichVuTraLaiGiamGiaModel]:  # noqa: C901
        """
        Create multiple ChiTietHoaDonDichVuTraLaiGiamGiaModel instances.

        Parameters
        ----------
        data_list : List[Dict[str, Any]]
            A list of data dictionaries to create ChiTietHoaDonDichVuTraLaiGiamGiaModel instances with.  # noqa: E501

        Returns
        -------
        List[ChiTietHoaDonDichVuTraLaiGiamGiaModel]
            The created ChiTietHoaDonDichVuTraLaiGiamGiaModel instances.
        """
        instances = []
        for data in data_list:
            cleaned_data = self._clean_data(data)
            instances.append(self.model(**cleaned_data))

        return self.model.objects.bulk_create(instances)
