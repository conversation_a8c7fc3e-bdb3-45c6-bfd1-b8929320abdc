"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietPhuTungKemTheoCCDC (Tool Accessory Detail) serializer implementation.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.models.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc.chi_tiet_phu_tung_kem_theo_ccdc import (  # noqa: F401
    ChiTietPhuTungKemTheoCCDCModel,
)

# Avoid circular import
# from django_ledger.api.serializers.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc.khai_bao_thong_tin_ccdc import KhaiBaoThongTinCCDCModelSerializer  # noqa: E501


class ChiTietPhuTungKemTheoCCDCModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the ChiTietPhuTungKemTheoCCDCModel.

    This serializer handles the conversion between ChiTietPhuTungKemTheoCCDCModel instances and JSON representations,  # noqa: E501
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields
    - When deserializing, accepts UUIDs for reference fields
    """

    # Add nested serializer for khai_bao_thong_tin_ccdc
    khai_bao_thong_tin_ccdc_data = serializers.SerializerMethodField(
        read_only=True
    )

    class Meta:
        model = ChiTietPhuTungKemTheoCCDCModel
        fields = [
            'uuid',
            'khai_bao_thong_tin_ccdc',
            'khai_bao_thong_tin_ccdc_data',
            'ma_cc',
            'ten_ptcc',
            'line',
            'dvt',
            'so_luong',
            'gia_tri_nt',
            'gia_tri',
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'khai_bao_thong_tin_ccdc',
            'khai_bao_thong_tin_ccdc_data',
            'created',
            'updated',
        ]

    def get_khai_bao_thong_tin_ccdc_data(self, obj):  # noqa: C901
        """
        Gets basic data for the khai_bao_thong_tin_ccdc field.

        Parameters
        ----------
        obj : ChiTietPhuTungKemTheoCCDCModel
            The ChiTietPhuTungKemTheoCCDCModel instance.

        Returns
        -------
        dict or None
            Dictionary with basic information about the khai_bao_thong_tin_ccdc or None if khai_bao_thong_tin_ccdc is None.  # noqa: E501
        """
        if obj.khai_bao_thong_tin_ccdc:
            # Return basic information to avoid circular import
            return {
                'uuid': obj.khai_bao_thong_tin_ccdc.uuid,
                'ngay_ct': obj.khai_bao_thong_tin_ccdc.ngay_ct,
                'status': obj.khai_bao_thong_tin_ccdc.status,
                'created': obj.khai_bao_thong_tin_ccdc.created,
                'updated': obj.khai_bao_thong_tin_ccdc.updated,
            }
        return None
