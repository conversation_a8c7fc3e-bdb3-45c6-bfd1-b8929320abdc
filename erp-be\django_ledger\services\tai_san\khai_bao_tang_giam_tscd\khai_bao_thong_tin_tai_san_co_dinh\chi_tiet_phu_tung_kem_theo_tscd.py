"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiTietPhuTungKemTheoTSCDService, which handles business logic  # noqa: E501
for the ChiTietPhuTungKemTheoTSCDModel.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import transaction
from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models import (
    ChiTietPhuTungKemTheoTSCDModel,  # noqa: F401
    KhaiBaoThongTinTaiSanCoDinhModel,
)
from django_ledger.repositories.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh.chi_tiet_phu_tung_kem_theo_tscd import (
    ChiTietPhuTungKemTheoTSCDRepository,  # noqa: F401
)
from django_ledger.services.base import BaseService  # noqa: F401,


class ChiTietPhuTungKemTheoTSCDService(BaseService):
    """
    Service class for handling ChiTietPhuTungKemTheoTSCD (Fixed Asset Accessory Detail) model business logic  # noqa: E501
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the service with the repository.
        """
        self.repository = ChiTietPhuTungKemTheoTSCDRepository()
        super().__init__()

    def get_by_id(
        self, uuid: str
    ) -> Optional[ChiTietPhuTungKemTheoTSCDModel]:  # noqa: C901
        """
        Retrieves a ChiTietPhuTungKemTheoTSCDModel by its UUID.

        Parameters
        ----------
        uuid : str
            The UUID of the ChiTietPhuTungKemTheoTSCDModel to retrieve.

        Returns
        -------
        Optional[ChiTietPhuTungKemTheoTSCDModel]
            The ChiTietPhuTungKemTheoTSCDModel with the given UUID, or None if not found.  # noqa: E501
        """
        return self.repository.get_by_id(uuid=uuid)

    def get_by_parent(self, parent_uuid: str) -> QuerySet:  # noqa: C901
        """
        Retrieves all ChiTietPhuTungKemTheoTSCDModel instances for a parent KhaiBaoThongTinTaiSanCoDinhModel.  # noqa: E501

        Parameters
        ----------
        parent_uuid : str
            The UUID of the parent KhaiBaoThongTinTaiSanCoDinhModel.

        Returns
        -------
        QuerySet
            A QuerySet of ChiTietPhuTungKemTheoTSCDModel instances.
        """
        return self.repository.get_by_parent(parent_uuid=parent_uuid)

    def create(
        self, parent: KhaiBaoThongTinTaiSanCoDinhModel, data: Dict[str, Any]
    ) -> ChiTietPhuTungKemTheoTSCDModel:  # noqa: C901
        """
        Creates a new ChiTietPhuTungKemTheoTSCDModel instance.

        Parameters
        ----------
        parent : KhaiBaoThongTinTaiSanCoDinhModel
            The parent KhaiBaoThongTinTaiSanCoDinhModel instance.
        data : Dict[str, Any]
            The data for the new ChiTietPhuTungKemTheoTSCDModel.

        Returns
        -------
        ChiTietPhuTungKemTheoTSCDModel
            The created ChiTietPhuTungKemTheoTSCDModel instance.
        """
        return self.repository.create(parent_uuid=parent.uuid, data=data)

    @transaction.atomic
    def bulk_create(
        self, parent: KhaiBaoThongTinTaiSanCoDinhModel, data: List[Dict[str, Any]]
    ) -> None:  # noqa: C901
        """
        Creates multiple ChiTietPhuTungKemTheoTSCDModel instances.

        Parameters
        ----------
        parent : KhaiBaoThongTinTaiSanCoDinhModel
            The parent KhaiBaoThongTinTaiSanCoDinhModel instance.
        data : List[Dict[str, Any]]
            The list of data for creating the ChiTietPhuTungKemTheoTSCDModel instances.

        Returns
        -------
        None
        """
        for item in data:
            self.repository.create(parent_uuid=parent.uuid, data=item)
        return None

    @transaction.atomic
    def bulk_update(
        self, parent: KhaiBaoThongTinTaiSanCoDinhModel, data: List[Dict[str, Any]]
    ) -> None:  # noqa: C901
        """
        Update multiple ChiTietPhuTungKemTheoTSCDModel instances for a specific parent.

        Parameters
        ----------
        parent : KhaiBaoThongTinTaiSanCoDinhModel
            The parent KhaiBaoThongTinTaiSanCoDinhModel instance.
        data : List[Dict[str, Any]]
            The list of data for the instances to update.

        Returns
        -------
        None
        """
        existing_chi_tiet = parent.chi_tiet_phu_tung_kem_theo.filter(deleted__isnull=True)
        existing_chi_tiet_dict = {str(ct.uuid): ct for ct in existing_chi_tiet}

        updated_uuids = set()

        for item in data:
            item_uuid = item.get("uuid")

            if item_uuid and item_uuid in existing_chi_tiet_dict:
                updated_uuids.add(item_uuid)
                item_data = item.copy()
                if "uuid" in item_data:
                    item_data.pop("uuid")
                self.update(uuid=item_uuid, data=item_data)
            else:
                item_data = item.copy()
                if "uuid" in item_data:
                    item_data.pop("uuid")
                self.create(parent=parent, data=item_data)

        for uuid_str in existing_chi_tiet_dict:
            if uuid_str not in updated_uuids:
                self.delete(uuid=uuid_str)

    def update(
        self, uuid: str, data: Dict[str, Any]
    ) -> Optional[ChiTietPhuTungKemTheoTSCDModel]:  # noqa: C901
        """
        Updates an existing ChiTietPhuTungKemTheoTSCDModel instance.

        Parameters
        ----------
        uuid : str
            The UUID of the ChiTietPhuTungKemTheoTSCDModel to update.
        data : Dict[str, Any]
            The data to update the ChiTietPhuTungKemTheoTSCDModel with.

        Returns
        -------
        Optional[ChiTietPhuTungKemTheoTSCDModel]
            The updated ChiTietPhuTungKemTheoTSCDModel instance, or None if not found.
        """
        # Check if the record exists
        instance = self.get_by_id(uuid=uuid)
        if not instance:
            raise ValueError(f"Record with UUID {uuid} not found")

        return self.repository.update(uuid=uuid, data=data)

    def delete(self, uuid: str) -> bool:  # noqa: C901
        """
        Deletes a ChiTietPhuTungKemTheoTSCDModel instance.

        Parameters
        ----------
        uuid : str
            The UUID of the ChiTietPhuTungKemTheoTSCDModel to delete.

        Returns
        -------
        bool
            True if the ChiTietPhuTungKemTheoTSCDModel was deleted, False otherwise.
        """
        # Check if the record exists
        instance = self.get_by_id(uuid=uuid)
        if not instance:
            raise ValueError(f"Record with UUID {uuid} not found")

        return self.repository.delete(uuid=uuid)
