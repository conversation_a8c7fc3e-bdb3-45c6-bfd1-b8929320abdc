import { useState, useEffect, useCallback } from 'react';
import { QuyenChungTu, QuyenChungTuResponse, QuyenChungTuInput } from '@/types/schemas';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

interface UseQuyenChungTuReturn {
  quyenChungTus: QuyenChungTu[];
  isLoading: boolean;
  totalItems: number;
  currentPage: number;
  handlePageChange: (page: number) => Promise<void>;
  addQuyenChungTu: (newQuyenChungTu: QuyenChungTuInput) => Promise<void>;
  updateQuyenChungTu: (uuid: string, updatedQuyenChungTu: QuyenChungTuInput) => Promise<void>;
  deleteQuyenChungTu: (uuid: string) => Promise<void>;
  refreshQuyenChungTu: () => Promise<void>;
}

interface UseQuyenChungTuParams {
  initialData?: QuyenChungTu[];
  pageSize?: number;
}

export const useQuyenChungTu = ({
  initialData = [],
  pageSize = 10
}: UseQuyenChungTuParams = {}): UseQuyenChungTuReturn => {
  const [quyenChungTus, setQuyenChungTu] = useState<QuyenChungTu[]>(initialData);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(0);

  const { entity } = useAuth();

  const fetchQuyenChungTu = useCallback(
    async (page: number = 0): Promise<void> => {
      if (!entity?.slug) return;

      setIsLoading(true);
      try {
        const params = {
          page: page + 1,
          page_size: pageSize
        };

        const response = await api.get<QuyenChungTuResponse>(
          `/entities/${entity.slug}/erp/${QUERY_KEYS.QUYEN_CHUNG_TU}/`,
          { params }
        );

        const mappedData: QuyenChungTu[] = response.data.results;

        setQuyenChungTu(mappedData);
        setTotalItems(response.data.count || 0);
        setCurrentPage(page);
      } catch (error) {
        console.error('Error fetching document books:', error);
        setQuyenChungTu([]);
        setTotalItems(0);
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug, pageSize]
  );

  const addQuyenChungTu = async (newQuyenChungTu: QuyenChungTuInput) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.post(`/entities/${entity.slug}/erp/${QUERY_KEYS.QUYEN_CHUNG_TU}/`, newQuyenChungTu);

      // Add the new quyenChungTu to the list
      const newQuyenChungTuData: QuyenChungTu = response.data;

      setQuyenChungTu(prev => [...prev, newQuyenChungTuData]);

      await fetchQuyenChungTu(currentPage);
    } catch (error) {
      console.error('Error adding document book:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateQuyenChungTu = async (uuid: string, updatedQuyenChungTu: QuyenChungTuInput) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.put(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.QUYEN_CHUNG_TU}/${uuid}/`,
        updatedQuyenChungTu
      );

      // Update the quyenChungTu in the list
      const updatedQuyenChungTuData: QuyenChungTu = response.data;

      setQuyenChungTu(prev =>
        prev.map(quyenChungTu =>
          quyenChungTu.uuid === updatedQuyenChungTuData.uuid ? updatedQuyenChungTuData : quyenChungTu
        )
      );
      await fetchQuyenChungTu(currentPage);
    } catch (error) {
      console.error('Error updating document book:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteQuyenChungTu = async (uuid: string) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.QUYEN_CHUNG_TU}/${uuid}/`);

      // Remove the quyenChungTu from the list
      setQuyenChungTu(prev => prev.filter(quyenChungTu => quyenChungTu.uuid !== uuid));

      // Refresh the current page to show the updated list
      await fetchQuyenChungTu(currentPage);
    } catch (error) {
      console.error('Error deleting document book:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handlePageChange = useCallback(
    async (page: number) => {
      await fetchQuyenChungTu(page);
    },
    [fetchQuyenChungTu]
  );

  const refreshQuyenChungTu = useCallback(async (): Promise<void> => {
    await fetchQuyenChungTu(currentPage);
  }, [fetchQuyenChungTu, currentPage]);

  useEffect(() => {
    fetchQuyenChungTu();
  }, [fetchQuyenChungTu]);

  return {
    quyenChungTus,
    isLoading,
    totalItems,
    currentPage,
    handlePageChange,
    addQuyenChungTu,
    updateQuyenChungTu,
    deleteQuyenChungTu,
    refreshQuyenChungTu
  };
};
