"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Balance Sheet Calculator for Bang Can Doi Ke <PERSON>.
Expert ERP Implementation - 20+ years experience.

Calculates balance sheet values using account balance audit data.
"""

import logging
from typing import Dict, List, Optional, Any
from decimal import Decimal
from datetime import datetime, date
from django.db.models import Sum, Q
from django_ledger.models import AccountBalanceAuditModel, AccountModel
from django_ledger.utils_new.accounting.audit_utils import get_account_balances_on_date_in_bulk
from .report_date_calculator import ReportDateCalculator

logger = logging.getLogger(__name__)



class BalanceSheetCalculator:
    """
    Balance Sheet Calculator using Account Balance Audit data.
    
    Expert ERP Implementation - Calculates all 440+ balance sheet line items
    using actual account balances with proper formulas and validations.
    """

    def __init__(self, entity_uuid: str):
        """
        Initialize calculator for specific entity.

        Parameters
        ----------
        entity_uuid : str
            Entity UUID for calculations
        """
        self.entity_uuid = entity_uuid
        self.date_calculator = ReportDateCalculator()

    def calculate_balance_sheet(self, as_of_date: date) -> Dict[str, Any]:
        """
        Calculate complete balance sheet as of specific date.
        
        Expert ERP Implementation - Calculates all line items using account
        balance audit data with proper formula evaluation.
        
        Parameters
        ----------
        as_of_date : date
            Date for balance sheet calculation
            
        Returns
        -------
        Dict[str, Any]
            Complete balance sheet with all line items
        """
        # Get all account balances as of date
        account_balances = self._get_account_balances(as_of_date)

        # Calculate all balance sheet items
        balance_sheet_data = {}

        # Use complete 216-line mapping to ensure all items are included
        from .full_216_balance_sheet_mapping import Full216BalanceSheetMapping
        complete_mapping = Full216BalanceSheetMapping()
        all_items = complete_mapping.get_all_items()

        # Sort by item code to ensure proper calculation order
        sorted_items = sorted(all_items.keys())

        for item_code in sorted_items:
            item_info = all_items[item_code]

            # Calculate value for this item
            value = self._calculate_item_value(
                item_info, account_balances, balance_sheet_data
            )

            balance_sheet_data[item_code] = {
                'ma_so': item_code,
                'chi_tieu': item_info['name'],
                'value': value,
                'level': item_info['level'],
                'is_total': item_info.get('is_total', False),
                'parent': item_info.get('parent'),
                'accounts': item_info.get('accounts', []),
                'formula': item_info.get('formula')
            }
        
        # Validate balance sheet equation
        validation_result = self._validate_balance_sheet(balance_sheet_data)

        # Add validation info to response
        balance_sheet_data['_validation'] = validation_result

        return balance_sheet_data

    def _get_account_balances(self, as_of_date: date) -> Dict[str, Decimal]:
        """
        Gets the total aggregated balance for all relevant accounts on a specific date.

        This function is now optimized to avoid the N+1 problem by fetching all balances
        in a single, efficient bulk query.

        Args:
            as_of_date (date): The date for which to calculate balances.

        Returns:
            Dict[str, Decimal]: A dictionary mapping each account code to its total balance.
        """
        from .full_216_balance_sheet_mapping import Full216BalanceSheetMapping
        mapping = Full216BalanceSheetMapping()
        all_items = mapping.get_all_items()

        # Gather all unique account codes from the entire mapping...
        all_account_codes = set()
        for item_info in all_items.values():
            accounts = item_info.get('accounts', [])
            if accounts:
                all_account_codes.update(accounts)

        # Fetch all balances in a single bulk query.
        return get_account_balances_on_date_in_bulk(
            entity_uuid=self.entity_uuid,
            account_codes=list(all_account_codes),
            on_date=as_of_date
        )

    def _calculate_item_value(
        self,
        item_info: Dict,
        account_balances: Dict[str, Decimal],
        calculated_items: Dict[str, Any]
    ) -> Decimal:
        """
        Calculate value for a specific balance sheet item.
        
        Parameters
        ----------
        item_code : str
            Balance sheet item code
        item_info : Dict
            Item information from mapping
        account_balances : Dict[str, Decimal]
            Account balances
        calculated_items : Dict[str, Any]
            Already calculated items
            
        Returns
        -------
        Decimal
            Calculated value for the item
        """
        formula = item_info.get('formula')
        accounts = item_info.get('accounts', [])
        is_negative = item_info.get('is_negative', False)
        
        if formula:
            # Calculate using formula (sum of other items)
            value = self._evaluate_formula(formula, calculated_items)
        elif accounts:
            # Calculate from account balances
            value = Decimal('0')
            for account_code in accounts:
                account_balance = account_balances.get(account_code, Decimal('0'))
                value += account_balance
        else:
            # No formula and no accounts - default to 0
            value = Decimal('0')
        
        # Apply negative flag if needed (e.g., for provisions)
        if is_negative and value > 0:
            value = -value
        
        return value

    def _evaluate_formula(self, formula: str, calculated_items: Dict[str, Any]) -> Decimal:
        """
        Evaluate formula string using calculated item values.
        
        Parameters
        ----------
        formula : str
            Formula string like '[111] + [112] - [113]'
        calculated_items : Dict[str, Any]
            Already calculated items
            
        Returns
        -------
        Decimal
            Calculated result
        """
        import re
        
        # Find all item references in formula [XXX]
        item_refs = re.findall(r'\[([^\]]+)\]', formula)
        
        # Replace item references with actual values
        eval_formula = formula
        for item_ref in item_refs:
            if item_ref in calculated_items:
                value = calculated_items[item_ref]['value']
            else:
                value = Decimal('0')  # Default to 0 if item not calculated yet
            
            eval_formula = eval_formula.replace(f'[{item_ref}]', str(value))
        
        # Safely evaluate the mathematical expression
        try:
            # Remove any non-numeric/operator characters for safety
            safe_formula = re.sub(r'[^0-9+\-*/.() ]', '', eval_formula)
            result = eval(safe_formula)
            return Decimal(str(result))
        except:
            return Decimal('0')

    def _validate_balance_sheet(self, balance_sheet_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate balance sheet equation: Assets = Liabilities + Equity.

        Expert ERP Implementation - Validates according to accounting standards
        with proper error reporting and tolerance handling.

        Parameters
        ----------
        balance_sheet_data : Dict[str, Any]
            Complete balance sheet data

        Returns
        -------
        Dict[str, Any]
            Validation results with status and details
        """
        validation_result = {
            'is_balanced': True,
            'total_assets': Decimal('0'),
            'total_sources': Decimal('0'),
            'difference': Decimal('0'),
            'tolerance': Decimal('0.01'),
            'errors': [],
            'warnings': []
        }

        try:
            # Get key totals
            total_assets = balance_sheet_data.get('270', {}).get('value', Decimal('0'))
            total_sources = balance_sheet_data.get('440', {}).get('value', Decimal('0'))

            validation_result['total_assets'] = total_assets
            validation_result['total_sources'] = total_sources

            # Check balance
            difference = abs(total_assets - total_sources)
            validation_result['difference'] = difference

            tolerance = validation_result['tolerance']

            if difference > tolerance:
                validation_result['is_balanced'] = False
                validation_result['errors'].append(
                    f"Balance sheet equation failed: Assets ({total_assets:,.2f}) ≠ "
                    f"Sources ({total_sources:,.2f}). Difference: {difference:,.2f}"
                )

                import logging
                logger = logging.getLogger(__name__)
                logger.error(
                    f"Balance sheet validation failed: Assets={total_assets}, "
                    f"Sources={total_sources}, Difference={difference}"
                )
            else:
                validation_result['warnings'].append(
                    f"Balance sheet is balanced within tolerance. "
                    f"Difference: {difference:,.2f} (≤ {tolerance:,.2f})"
                )

            # Additional validations
            self._validate_section_totals(balance_sheet_data, validation_result)

        except Exception as e:
            validation_result['is_balanced'] = False
            validation_result['errors'].append(f"Validation error: {str(e)}")

            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Balance sheet validation exception: {e}", exc_info=True)

        return validation_result

    def _validate_section_totals(
        self,
        balance_sheet_data: Dict[str, Any],
        validation_result: Dict[str, Any]
    ) -> None:
        """
        Validate individual section totals against their components.

        Parameters
        ----------
        balance_sheet_data : Dict[str, Any]
            Complete balance sheet data
        validation_result : Dict[str, Any]
            Validation result to update
        """
        # Key section validations
        validations = [
            # Assets sections
            ('100', ['110', '120', '130', '140', '150'], 'TÀI SẢN NGẮN HẠN'),
            ('200', ['210', '220', '240', '250', '260'], 'TÀI SẢN DÀI HẠN'),
            ('270', ['100', '200'], 'TỔNG CỘNG TÀI SẢN'),

            # Liabilities and Equity sections
            ('300', ['310', '330'], 'NỢ PHẢI TRẢ'),
            ('400', ['410', '411', '412', '413', '414', '415', '416', '417', '418', '419', '420', '421'], 'VỐN CHỦ SỞ HỮU'),
            ('440', ['300', '400'], 'TỔNG CỘNG NGUỒN VỐN'),
        ]

        for total_code, component_codes, section_name in validations:
            try:
                total_value = balance_sheet_data.get(total_code, {}).get('value', Decimal('0'))
                component_sum = sum(
                    balance_sheet_data.get(code, {}).get('value', Decimal('0'))
                    for code in component_codes
                )

                difference = abs(total_value - component_sum)
                tolerance = Decimal('0.01')

                if difference > tolerance:
                    validation_result['warnings'].append(
                        f"{section_name} ({total_code}): Total {total_value:,.2f} ≠ "
                        f"Sum of components {component_sum:,.2f}. Difference: {difference:,.2f}"
                    )

            except Exception as e:
                validation_result['warnings'].append(
                    f"Error validating {section_name}: {str(e)}"
                )

    def get_multi_period_comparison(
        self, 
        dates: List[date]
    ) -> Dict[str, Dict[str, Any]]:
        """
        Calculate balance sheet for multiple periods for comparison.
        
        Parameters
        ----------
        dates : List[date]
            List of dates for comparison
            
        Returns
        -------
        Dict[str, Dict[str, Any]]
            Multi-period balance sheet data
        """
        multi_period_data = {}
        
        for i, as_of_date in enumerate(dates):
            period_key = f'period_{i+1}'
            balance_sheet = self.calculate_balance_sheet(as_of_date)
            
            # Transform for multi-period structure
            for item_code, item_data in balance_sheet.items():
                if item_code not in multi_period_data:
                    multi_period_data[item_code] = {
                        'ma_so': item_data['ma_so'],
                        'chi_tieu': item_data['chi_tieu'],
                        'level': item_data['level'],
                        'is_total': item_data['is_total'],
                        'periods': {}
                    }
                
                multi_period_data[item_code]['periods'][period_key] = item_data['value']
        
        return multi_period_data

    def calculate_multi_period_balance_sheet(self, ky: int, nam: int, loai_ky: int, so_ky: int) -> Dict[str, Any]:
        """
        Calculate balance sheet for multiple periods with dynamic columns.

        Parameters
        ----------
        ky : int
            Starting month (1-12)
        nam : int
            Year
        loai_ky : int
            Period type (1=monthly, 2=quarterly, 3=semi-annual, 4=annual)
        so_ky : int
            Number of periods (1-12)

        Returns
        -------
        Dict[str, Any]
            Multi-period balance sheet data with dynamic columns _0001_$0, _0002_$0, etc.
        """
        try:
            # Calculate report dates
            report_dates = self.date_calculator.calculate_report_dates(ky, nam, loai_ky, so_ky)
            column_names = self.date_calculator.get_period_column_names(so_ky)

            logger.info(f"Calculating multi-period balance sheet for {len(report_dates)} periods")
            logger.info(f"Report dates: {[d.isoformat() for d in report_dates]}")

            # Get complete mapping
            from .full_216_balance_sheet_mapping import Full216BalanceSheetMapping
            complete_mapping = Full216BalanceSheetMapping()
            all_items = complete_mapping.get_all_items()

            # Calculate balance sheet for each period
            period_data = {}
            for i, report_date in enumerate(report_dates):
                logger.info(f"Calculating balance sheet for period {i+1}: {report_date}")

                # Get account balances for this date
                account_balances = self._get_account_balances(report_date)

                # Calculate balance sheet items for this period
                period_balance_sheet = {}
                sorted_items = sorted(all_items.keys())

                for item_code in sorted_items:
                    item_info = all_items[item_code]
                    value = self._calculate_item_value(item_info, account_balances, period_balance_sheet)

                    period_balance_sheet[item_code] = {
                        'value': value,
                        'name': item_info['name'],
                        'level': item_info.get('level', 0),
                        'is_total': item_info.get('is_total', False)
                    }

                period_data[f'period_{i+1}'] = period_balance_sheet

            # Format response with dynamic columns
            formatted_results = []
            for item_code in sorted(all_items.keys()):
                item_info = all_items[item_code]

                # Base item data
                item_result = {
                    'ma_chi_tieu': item_code,
                    'ten_chi_tieu': item_info['name'],
                    'level': item_info.get('level', 0),
                    'is_total': item_info.get('is_total', False)
                }

                # Add dynamic period columns
                for i, column_name in enumerate(column_names):
                    period_key = f'period_{i+1}'
                    if period_key in period_data and item_code in period_data[period_key]:
                        value = period_data[period_key][item_code]['value']
                        item_result[column_name] = float(value) if value else 0.0
                    else:
                        item_result[column_name] = 0.0

                formatted_results.append(item_result)

            return {
                'results': formatted_results,
                'count': len(formatted_results)
            }

        except Exception as e:
            logger.error(f"Error calculating multi-period balance sheet: {e}")
            raise
