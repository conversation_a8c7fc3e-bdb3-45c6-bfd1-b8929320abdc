"""
Cash Flow Statement Direct Method Calculator - Thông tư 200/2014/TT-BTC
Expert ERP Implementation - 20+ years experience

Provides comprehensive calculation for Cash Flow Statement Direct Method with:
- Direct cash flow calculation from account movements
- Period comparison (current vs previous)
- Formula evaluation for computed indicators
- Vietnamese accounting standards compliance
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from decimal import Decimal
from datetime import datetime, date, timedelta

from django.db.models import Q, Sum, F
from django_ledger.models import EntityModel
from django_ledger.models.account_balance_audit import AccountBalanceAuditModel
from django_ledger.models.accounts import AccountModel
from django_ledger.models.chart_of_accounts import ChartOfAccountModel
from django_ledger.utils_new.accounting.audit_utils import get_total_account_balance_on_date, get_total_movement_for_period
from .cash_flow_direct_mapping import CashFlowDirectMapping

logger = logging.getLogger(__name__)


class CashFlowDirectCalculator:
    """
    Calculator for Cash Flow Statement Direct Method.
    
    Expert ERP Implementation - Handles direct cash flow calculations
    with period comparison and formula evaluation.
    """

    def __init__(self, entity_slug: str):
        """
        Initialize calculator for specific entity.
        
        Args:
            entity_slug: Entity identifier
        """
        self.entity_slug = entity_slug
        self.mapping = CashFlowDirectMapping()
        self.entity = self._get_entity()

    def _get_entity(self) -> EntityModel:
        """Get entity model instance."""
        try:
            return EntityModel.objects.get(slug=self.entity_slug)
        except EntityModel.DoesNotExist:
            logger.error(f"Entity not found: {self.entity_slug}")
            raise ValueError(f"Entity not found: {self.entity_slug}")

    def calculate_cash_flow_direct(
        self,
        ngay_ct11: str,  # Current period start
        ngay_ct12: str,  # Current period end
        ngay_ct01: str,  # Previous period start
        ngay_ct02: str   # Previous period end
    ) -> Dict[str, Dict[str, Decimal]]:
        """
        Calculate cash flow direct method for two periods.
        
        Args:
            ngay_ct11: Current period start date (YYYY-MM-DD)
            ngay_ct12: Current period end date (YYYY-MM-DD)
            ngay_ct01: Previous period start date (YYYY-MM-DD)
            ngay_ct02: Previous period end date (YYYY-MM-DD)
            
        Returns:
            Dict with indicator codes as keys and period values
        """
        results = {}
        indicators = self.mapping.get_all_indicators()

        # Calculate each indicator for both periods
        for ma_so, indicator_info in indicators.items():
            try:
                # Calculate for current period
                ky_nay = self._calculate_indicator_for_period(
                    ma_so, indicator_info, ngay_ct11, ngay_ct12
                )
                
                # Calculate for previous period
                ky_truoc = self._calculate_indicator_for_period(
                    ma_so, indicator_info, ngay_ct01, ngay_ct02
                )

                results[ma_so] = {
                    'ky_nay': ky_nay,
                    'ky_truoc': ky_truoc
                }


            except Exception as e:
                logger.error(f"Error calculating indicator {ma_so}: {str(e)}")
                results[ma_so] = {
                    'ky_nay': Decimal('0'),
                    'ky_truoc': Decimal('0')
                }

        # Evaluate formulas after all base calculations
        results = self._evaluate_formulas(results)

        return results

    def _calculate_indicator_for_period(
        self,
        ma_so: str,
        indicator_info: Dict[str, Any],
        start_date: str,
        end_date: str
    ) -> Decimal:
        """
        Calculate specific indicator for a period using direct method.

        Expert ERP Implementation - Calculates cash flows using direct method
        according to Vietnamese accounting standards.

        Args:
            ma_so: Indicator code
            indicator_info: Indicator configuration
            start_date: Period start date
            end_date: Period end date

        Returns:
            Calculated value for the indicator
        """
        # If indicator has formula, skip direct calculation
        if indicator_info.get('formula'):
            return Decimal('0')  # Will be calculated in formula evaluation

        # Calculate based on specific indicator using direct method
        if ma_so == '01':  # Tiền thu từ bán hàng
            return self._calculate_cash_from_sales(start_date, end_date)
        elif ma_so == '02A':  # Chi trả cho nhà cung cấp [A]
            return self._calculate_payments_to_suppliers(start_date, end_date)
        elif ma_so == '02B':  # Chi trả cho nhà cung cấp [B]
            return self._calculate_payments_to_suppliers(start_date, end_date) * Decimal('0.5')  # Split for demo
        elif ma_so == '03':  # Chi trả cho người lao động
            return self._calculate_payments_to_employees(start_date, end_date)
        elif ma_so == '04':  # Chi trả lãi vay
            return self._calculate_interest_payments(start_date, end_date)
        elif ma_so == '05':  # Chi nộp thuế TNDN
            return self._calculate_tax_payments(start_date, end_date)
        elif ma_so == '06':  # Tiền thu khác từ HĐKD
            return self._calculate_other_operating_receipts(start_date, end_date)
        elif ma_so == '07':  # Chi khác cho HĐKD
            return self._calculate_other_operating_payments(start_date, end_date)
        elif ma_so in ['21', '22A', '22B', '23', '24', '25', '26', '27']:  # Investing activities
            return self._calculate_investing_cash_flows(ma_so, start_date, end_date)
        elif ma_so in ['31', '32', '33', '34', '35', '36']:  # Financing activities
            return self._calculate_financing_cash_flows(ma_so, start_date, end_date)
        elif ma_so == '60':  # Tiền đầu kỳ
            return self.get_cash_balance_for_period(start_date, is_start=True)
        elif ma_so == '61A':  # Lãi do đánh giá chênh lệch tỷ giá
            fx_effect = self._calculate_exchange_rate_effects(start_date, end_date)
            return max(fx_effect, Decimal('0'))  # Only positive effects
        elif ma_so == '61B':  # Lỗ do đánh giá chênh lệch tỷ giá
            fx_effect = self._calculate_exchange_rate_effects(start_date, end_date)
            return abs(min(fx_effect, Decimal('0')))  # Only negative effects (as positive)
        else:
            return Decimal('0')

    def _get_cash_movements_for_accounts(
        self,
        account_codes: List[str],
        start_date: str,
        end_date: str,
        activity_type: str
    ) -> Decimal:
        """
        Gets the total movement for a list of accounts for a specific period using the audit utility.
        This function is now a wrapper around the centralized audit utility.
        """
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

        return get_total_movement_for_period(
            entity_uuid=self.entity.uuid,
            account_codes=account_codes,
            from_date=start_date_obj,
            to_date=end_date_obj
        )

    def _evaluate_formulas(self, results: Dict[str, Dict[str, Decimal]]) -> Dict[str, Dict[str, Decimal]]:
        """
        Evaluate formulas for computed indicators.
        
        Args:
            results: Current calculation results
            
        Returns:
            Updated results with formula evaluations
        """
        indicators = self.mapping.get_all_indicators()
        
        # Process indicators with formulas
        for ma_so, indicator_info in indicators.items():
            formula = indicator_info.get('formula')
            if not formula:
                continue

            try:
                # Evaluate formula for current period
                ky_nay = self._evaluate_formula_for_period(formula, results, 'ky_nay')
                
                # Evaluate formula for previous period
                ky_truoc = self._evaluate_formula_for_period(formula, results, 'ky_truoc')

                results[ma_so] = {
                    'ky_nay': ky_nay,
                    'ky_truoc': ky_truoc
                }


            except Exception as e:
                logger.error(f"Error evaluating formula for {ma_so}: {str(e)}")
                results[ma_so] = {
                    'ky_nay': Decimal('0'),
                    'ky_truoc': Decimal('0')
                }

        return results

    def _evaluate_formula_for_period(
        self,
        formula: str,
        results: Dict[str, Dict[str, Decimal]],
        period_key: str
    ) -> Decimal:
        """
        Evaluate formula for specific period.
        
        Args:
            formula: Formula string (e.g., '[01]+[02]-[03]')
            results: Current calculation results
            period_key: Period key ('ky_nay' or 'ky_truoc')
            
        Returns:
            Calculated formula result
        """
        try:
            # Replace indicator references with actual values
            eval_formula = formula
            
            # Find all indicator references [XX]
            import re
            indicator_refs = re.findall(r'\[(\d+[A-Z]*)\]', formula)
            
            for ref in indicator_refs:
                if ref in results:
                    value = results[ref].get(period_key, Decimal('0'))
                    eval_formula = eval_formula.replace(f'[{ref}]', str(value))
                else:
                    eval_formula = eval_formula.replace(f'[{ref}]', '0')

            # Safely evaluate the mathematical expression
            result = eval(eval_formula)
            return Decimal(str(result))

        except Exception as e:
            logger.error(f"Error evaluating formula '{formula}': {str(e)}")
            return Decimal('0')

    def get_cash_balance_for_period(self, date: str, is_start: bool = True) -> Decimal:
        """
        Get cash and cash equivalents balance for specific date.
        
        Args:
            date: Date in YYYY-MM-DD format
            is_start: True for start of period, False for end
            
        Returns:
            Cash balance
        """
        try:
            # Cash and cash equivalent accounts
            cash_accounts = ['111', '112', '121']  # Cash, Bank, Short-term investments
            
            # Adjust date for start/end of period
            if is_start:
                # For start of period, get balance at end of previous day
                from datetime import datetime, timedelta
                date_obj = datetime.strptime(date, '%Y-%m-%d')
                date_obj = date_obj - timedelta(days=1)
                query_date = date_obj.strftime('%Y-%m-%d')
            else:
                query_date = date

            return self._get_account_balances_for_date(cash_accounts, query_date)

        except Exception as e:
            logger.error(f"Error getting cash balance for {date}: {str(e)}")
            return Decimal('0')

    def _get_account_balances_for_date(self, account_codes: List[str], date: str) -> Decimal:
        """
        Gets the total aggregated account balance for a list of accounts on a specific date.
        This function is now a wrapper around the centralized audit utility to ensure
        correct aggregation from multiple underlying entities (customers, vendors).
        """
        date_obj = datetime.strptime(date, '%Y-%m-%d').date()
        return get_total_account_balance_on_date(
            entity_uuid=self.entity.uuid,
            account_codes=account_codes,
            on_date=date_obj
        )

    def _calculate_cash_from_sales(self, start_date: str, end_date: str) -> Decimal:
        """
        Calculate cash received from sales using direct method.

        Formula: Revenue + Beginning AR - Ending AR
        Uses AccountBalanceAuditModel for accurate calculation.
        """
        try:
            # Get revenue accounts (511x series)
            revenue_accounts = ['5111', '5112', '5113', '5114', '5118']

            # Get accounts receivable accounts (131x series)
            ar_accounts = ['131111', '1312']

            # Calculate revenue for the period (sum of changes in revenue accounts)
            revenue_changes = self._get_cash_movements_for_accounts(
                revenue_accounts, start_date, end_date, 'operating'
            )

            # Get AR balance at start and end of period
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

            # AR at beginning of period
            ar_beginning = self._get_account_balances_for_date(
                ar_accounts, (start_date_obj - timedelta(days=1)).strftime('%Y-%m-%d')
            )

            # AR at end of period
            ar_ending = self._get_account_balances_for_date(ar_accounts, end_date)

            # Cash from sales = Revenue + Beginning AR - Ending AR
            cash_from_sales = revenue_changes + ar_beginning - ar_ending

            return cash_from_sales

        except Exception as e:
            logger.error(f"Error calculating cash from sales: {str(e)}")
            return Decimal('0')

    def _calculate_payments_to_suppliers(self, start_date: str, end_date: str) -> Decimal:
        """
        Calculate cash paid to suppliers using direct method.

        Formula: COGS + Beginning AP - Ending AP + Inventory Increase
        Uses AccountBalanceAuditModel for accurate calculation.
        """
        try:
            # Get COGS accounts (632x series)
            cogs_accounts = ['6321', '6322']

            # Get accounts payable accounts (331x series)
            ap_accounts = ['331', '3331', '3332', '3333', '3335', '3338']

            # Get inventory accounts (15x series)
            inventory_accounts = ['151', '152', '153', '154', '156']

            # Calculate COGS for the period
            cogs_changes = self._get_cash_movements_for_accounts(
                cogs_accounts, start_date, end_date, 'operating'
            )

            # Get AP balances
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

            ap_beginning = self._get_account_balances_for_date(
                ap_accounts, (start_date_obj - timedelta(days=1)).strftime('%Y-%m-%d')
            )
            ap_ending = self._get_account_balances_for_date(ap_accounts, end_date)

            # Get inventory changes
            inventory_beginning = self._get_account_balances_for_date(
                inventory_accounts, (start_date_obj - timedelta(days=1)).strftime('%Y-%m-%d')
            )
            inventory_ending = self._get_account_balances_for_date(inventory_accounts, end_date)
            inventory_increase = inventory_ending - inventory_beginning

            # Cash to suppliers = COGS + Beginning AP - Ending AP + Inventory Increase
            cash_to_suppliers = cogs_changes + ap_beginning - ap_ending + inventory_increase

            return abs(cash_to_suppliers)  # Return as positive for cash outflow

        except Exception as e:
            logger.error(f"Error calculating payments to suppliers: {str(e)}")
            return Decimal('0')

    def _calculate_payments_to_employees(self, start_date: str, end_date: str) -> Decimal:
        """
        Calculate cash paid to employees using direct method.

        Formula: Salary Expense + Beginning Salary Payable - Ending Salary Payable
        """
        try:
            # Get salary expense accounts (641x, 642x series)
            salary_accounts = ['641', '642']

            # Get salary payable accounts (334x series)
            payable_accounts = ['334', '141']

            # Calculate salary expenses for the period
            salary_changes = self._get_cash_movements_for_accounts(
                salary_accounts, start_date, end_date, 'operating'
            )

            # Get payable balances
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()

            payable_beginning = self._get_account_balances_for_date(
                payable_accounts, (start_date_obj - timedelta(days=1)).strftime('%Y-%m-%d')
            )
            payable_ending = self._get_account_balances_for_date(payable_accounts, end_date)

            # Cash to employees = Salary Expense + Beginning Payable - Ending Payable
            cash_to_employees = salary_changes + payable_beginning - payable_ending

            return abs(cash_to_employees)

        except Exception as e:
            logger.error(f"Error calculating payments to employees: {str(e)}")
            return Decimal('0')

    def _calculate_interest_payments(self, start_date: str, end_date: str) -> Decimal:
        """Calculate cash paid for interest."""
        try:
            interest_accounts = ['6352']
            interest_payable_accounts = ['335']

            interest_expense = self._get_cash_movements_for_accounts(
                interest_accounts, start_date, end_date, 'operating'
            )

            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()

            payable_beginning = self._get_account_balances_for_date(
                interest_payable_accounts, (start_date_obj - timedelta(days=1)).strftime('%Y-%m-%d')
            )
            payable_ending = self._get_account_balances_for_date(interest_payable_accounts, end_date)

            cash_interest = interest_expense + payable_beginning - payable_ending
            return abs(cash_interest)

        except Exception as e:
            logger.error(f"Error calculating interest payments: {str(e)}")
            return Decimal('0')

    def _calculate_tax_payments(self, start_date: str, end_date: str) -> Decimal:
        """Calculate cash paid for corporate income tax."""
        try:
            tax_payable_accounts = ['3334']

            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()

            payable_beginning = self._get_account_balances_for_date(
                tax_payable_accounts, (start_date_obj - timedelta(days=1)).strftime('%Y-%m-%d')
            )
            payable_ending = self._get_account_balances_for_date(tax_payable_accounts, end_date)

            # Tax payments = Beginning Payable - Ending Payable
            tax_payments = payable_beginning - payable_ending
            return max(tax_payments, Decimal('0'))  # Only positive payments

        except Exception as e:
            logger.error(f"Error calculating tax payments: {str(e)}")
            return Decimal('0')

    def _calculate_other_operating_receipts(self, start_date: str, end_date: str) -> Decimal:
        """Calculate other operating cash receipts."""
        try:
            # This would be flexible based on specific accounts
            # For now, return 0 as it's marked with # in Excel
            return Decimal('0')
        except Exception as e:
            logger.error(f"Error calculating other operating receipts: {str(e)}")
            return Decimal('0')

    def _calculate_other_operating_payments(self, start_date: str, end_date: str) -> Decimal:
        """Calculate other operating cash payments."""
        try:
            # This would be flexible based on specific accounts
            # For now, return 0 as it's marked with # in Excel
            return Decimal('0')
        except Exception as e:
            logger.error(f"Error calculating other operating payments: {str(e)}")
            return Decimal('0')

    def _calculate_investing_cash_flows(self, ma_so: str, start_date: str, end_date: str) -> Decimal:
        """Calculate investing activity cash flows."""
        try:
            indicator_info = self.mapping.get_indicator_by_code(ma_so)
            if not indicator_info:
                return Decimal('0')

            account_sources = indicator_info.get('account_sources', [])
            if not account_sources:
                return Decimal('0')

            return self._get_cash_movements_for_accounts(
                account_sources, start_date, end_date, 'investing'
            )
        except Exception as e:
            logger.error(f"Error calculating investing cash flows for {ma_so}: {str(e)}")
            return Decimal('0')

    def _calculate_financing_cash_flows(self, ma_so: str, start_date: str, end_date: str) -> Decimal:
        """Calculate financing activity cash flows."""
        try:
            indicator_info = self.mapping.get_indicator_by_code(ma_so)
            if not indicator_info:
                return Decimal('0')

            account_sources = indicator_info.get('account_sources', [])
            if not account_sources:
                return Decimal('0')

            return self._get_cash_movements_for_accounts(
                account_sources, start_date, end_date, 'financing'
            )
        except Exception as e:
            logger.error(f"Error calculating financing cash flows for {ma_so}: {str(e)}")
            return Decimal('0')

    def _calculate_exchange_rate_effects(self, start_date: str, end_date: str) -> Decimal:
        """Calculate exchange rate effects on cash."""
        try:
            # Exchange rate difference accounts (413 series)
            fx_accounts = ['413']

            return self._get_cash_movements_for_accounts(
                fx_accounts, start_date, end_date, 'summary'
            )
        except Exception as e:
            logger.error(f"Error calculating exchange rate effects: {str(e)}")
            return Decimal('0')
