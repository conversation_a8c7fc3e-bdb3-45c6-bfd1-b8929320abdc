"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ThuePhieuNhapDieuChinhGiaHangMuaModel, which represents the tax details  # noqa: E501
of the purchase price adjustment receipt in the system.
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ThuePhieuNhapDieuChinhGiaHangMuaModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the ThuePhieuNhapDieuChinhGiaHangMuaModel.
    """

    def for_phieu_nhap(self, phieu_nhap_uuid):  # noqa: C901
        """
        Returns tax details for a specific purchase price adjustment receipt.

        Parameters
        ----------
        phieu_nhap_uuid: UUID
            The PhieuNhapDieuChinhGiaHangMuaModel UUID used for filtering the QuerySet.

        Returns
        -------
        ThuePhieuNhapDieuChinhGiaHangMuaModelQueryset
            A QuerySet of ThuePhieuNhapDieuChinhGiaHangMuaModel with applied filters.
        """
        return self.filter(phieu_nhap__uuid=phieu_nhap_uuid)


class ThuePhieuNhapDieuChinhGiaHangMuaModelManager(Manager):
    """
    A custom defined ThuePhieuNhapDieuChinhGiaHangMuaModel Manager that will act as an interface to handle the  # noqa: E501
    ThuePhieuNhapDieuChinhGiaHangMuaModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom ThuePhieuNhapDieuChinhGiaHangMuaModelQueryset.
        """
        return ThuePhieuNhapDieuChinhGiaHangMuaModelQueryset(self.model, using=self._db)

    def for_phieu_nhap(self, phieu_nhap_uuid):  # noqa: C901
        """
        Returns tax details for a specific purchase price adjustment receipt.

        Parameters
        ----------
        phieu_nhap_uuid: UUID
            The PhieuNhapDieuChinhGiaHangMuaModel UUID used for filtering the QuerySet.

        Returns
        -------
        ThuePhieuNhapDieuChinhGiaHangMuaModelQueryset
            A QuerySet of ThuePhieuNhapDieuChinhGiaHangMuaModel with applied filters.
        """
        return self.get_queryset().for_phieu_nhap(phieu_nhap_uuid=phieu_nhap_uuid)


class ThuePhieuNhapDieuChinhGiaHangMuaModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ThuePhieuNhapDieuChinhGiaHangMuaModel database will inherit from.  # noqa: E501
    The ThuePhieuNhapDieuChinhGiaHangMuaModel inherits functionality from the following MixIns:  # noqa: E501

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    phieu_nhap : ForeignKey
        Reference to the main purchase price adjustment receipt.
    ma_thue : ForeignKey
        Tax code (required).
    ma_kh : ForeignKey
        Customer code (required).
    tk_thue_no : ForeignKey
        Tax debit account (required).
    tk_du : ForeignKey
        Balance account (required).
    ma_tt : ForeignKey
        Payment term code.
    so_ct0 : ForeignKey
        Original document number.
    so_ct2 : ForeignKey
        Secondary document number.
    ma_kh9 : ForeignKey
        Customer 9 code.
    ma_bp : ForeignKey
        Department code.
    ma_vv : ForeignKey
        Case code.
    ma_hd : ForeignKey
        Contract code.
    ma_dtt : ForeignKey
        Payment batch code.
    ma_ku : ForeignKey
        Loan agreement code.
    ma_phi : ForeignKey
        Fee code.
    ma_sp : ForeignKey
        Product code.
    ma_lsx : ForeignKey
        Production order code.
    ma_cp0 : ForeignKey
        Invalid expense code.
    entity_model : ForeignKey
        The entity this tax record belongs to.
    line : IntegerField
        Line number.
    id_tt : IntegerField
        Status ID.
    ma_mau_ct : CharField
        Document template code.
    ma_mau_bc : CharField
        Report template code.
    ma_tc_thue : CharField
        Tax criteria code.
    ten_kh_thue : CharField
        Tax customer name.
    dia_chi : TextField
        Address.
    ma_so_thue : CharField
        Tax number.
    ten_vt_thue : CharField
        Tax material name.
    ten_tk_thue_no : CharField
        Tax debit account name.
    ten_tk_du : CharField
        Balance account name.
    ten_kh9 : CharField
        Customer 9 name.
    ten_tt : CharField
        Status name.
    ghi_chu : TextField
        Notes.
    ngay_ct0 : DateTimeField
        Original document date.
    thue_suat : DecimalField
        Tax rate.
    t_tien_nt : DecimalField
        Total amount in foreign currency.
    t_tien : DecimalField
        Total amount.
    t_thue_nt : DecimalField
        Total tax in foreign currency.
    t_thue : DecimalField
        Total tax.
    """

    uuid = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    # Link to main table
    phieu_nhap = models.ForeignKey(
        'django_ledger.PhieuNhapDieuChinhGiaHangMuaModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet_thue_list',
        verbose_name=_('Phiếu nhập điều chỉnh giá'),
        help_text=_('Reference to the main purchase price adjustment receipt'),
    )

    # Required foreign keys
    ma_thue = models.ForeignKey(
        'django_ledger.TaxModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã thuế'),
        null=True,
        blank=True,
        help_text=_('Tax code (required)'),
    )

    ma_kh = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã khách hàng'),
        null=True,
        blank=True,
        help_text=_('Customer code (required)'),
    )

    tk_thue_no = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        related_name='thue_phieu_nhap_dieu_chinh_gia_thue_no_list',
        verbose_name=_('Tài khoản thuế nợ'),
        help_text=_('Tax debit account (required)'),
    )

    tk_du = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        related_name='thue_phieu_nhap_dieu_chinh_gia_du_list',
        verbose_name=_('Tài khoản dư'),
        help_text=_('Balance account (required)'),
    )

    # Optional foreign keys
    ma_tt = models.ForeignKey(
        'django_ledger.HanThanhToanModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã thanh toán'),
        help_text=_('Payment term code'),
    )

    so_ct0 = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        help_text=_('Original document number'),
    )

    so_ct2 = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        help_text=_('Secondary document number'),
    )

    ma_kh9 = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='thue_phieu_nhap_dieu_chinh_gia_kh9_list',
        verbose_name=_('Mã khách hàng 9'),
        help_text=_('Customer 9 code'),
    )

    ma_bp = models.ForeignKey(
        'django_ledger.BoPhanModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã bộ phận'),
        help_text=_('Department code'),
    )

    ma_vv = models.ForeignKey(
        'django_ledger.VuViecModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã vụ việc'),
        help_text=_('Case code'),
    )

    ma_hd = models.ForeignKey(
        'django_ledger.ContractModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã hợp đồng'),
        help_text=_('Contract code'),
    )

    ma_dtt = models.ForeignKey(
        'django_ledger.DotThanhToanModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã đợt thanh toán'),
        help_text=_('Payment batch code'),
    )

    ma_ku = models.ForeignKey(
        'django_ledger.KheUocModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã khe ước'),
        help_text=_('Loan agreement code'),
    )

    ma_phi = models.ForeignKey(
        'django_ledger.PhiModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã phí'),
        help_text=_('Fee code'),
    )

    ma_sp = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='thue_phieu_nhap_dieu_chinh_gia_sp_list',
        verbose_name=_('Mã sản phẩm'),
        help_text=_('Product code'),
    )

    ma_lsx = models.CharField(
        max_length=50,
        verbose_name=_('Mã lệnh sản xuất'),
        help_text=_('Production order code'),
        null=True,
        blank=True,
    )

    ma_cp0 = models.ForeignKey(
        'django_ledger.ChiPhiKhongHopLeModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã chi phí không hợp lệ'),
        help_text=_('Invalid expense code'),
    )

    entity_model = models.ForeignKey(
        'django_ledger.EntityModel',
        on_delete=models.CASCADE,
        verbose_name=_('Entity'),
        help_text=_('The entity this tax record belongs to'),
    )

    # Integer fields
    line = models.IntegerField(verbose_name=_('Dòng'), help_text=_('Line number'))
    id_tt = models.IntegerField(
        default=0,
        verbose_name=_('ID trạng thái'),
        help_text=_('Status ID'),
    )

    # Text fields
    ma_mau_ct = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Mã mẫu chứng từ'),
        help_text=_('Document template code'),
    )

    ma_mau_bc = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Mã mẫu báo cáo'),
        help_text=_('Report template code'),
    )

    ma_tc_thue = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Mã tiêu chí thuế'),
        help_text=_('Tax criteria code'),
    )

    ten_kh_thue = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('Tên khách hàng thuế'),
        help_text=_('Tax customer name'),
    )

    dia_chi = models.TextField(
        verbose_name=_('Địa chỉ'),
        help_text=_('Customer address'),
        blank=True,
        null=True,
    )

    ma_so_thue = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Mã số thuế'),
        help_text=_('Tax identification number'),
    )

    ten_vt_thue = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('Tên vật tư thuế'),
        help_text=_('Tax material name'),
    )

    ten_tk_thue_no = models.CharField(
        max_length=255,
        verbose_name=_('Tên tài khoản thuế nợ'),
        help_text=_('Tax debit account name'),
        blank=True,
        null=True,
    )

    ten_tk_du = models.CharField(
        max_length=255,
        verbose_name=_('Tên tài khoản dư'),
        help_text=_('Balance account name'),
        blank=True,
        null=True,
    )

    ten_kh9 = models.CharField(
        max_length=255,
        verbose_name=_('Tên khách hàng 9'),
        help_text=_('Customer 9 name'),
        blank=True,
        null=True,
    )

    ten_tt = models.CharField(
        max_length=255,
        verbose_name=_('Tên trạng thái'),
        help_text=_('Status name'),
        blank=True,
        null=True,
    )

    ghi_chu = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Ghi chú'),
        help_text=_('Additional notes'),
    )

    # DateTime fields
    ngay_ct0 = models.DateTimeField(
        verbose_name=_('Ngày chứng từ gốc'),
        blank=True,
        null=True,
        help_text=_('Original document date and time'),
    )

    # Decimal fields
    thue_suat = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        verbose_name=_('Thuế suất'),
        help_text=_('Tax rate percentage'),
    )

    t_tien_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng tiền ngoại tệ'),
        help_text=_('Total amount in foreign currency'),
    )

    t_tien = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng tiền'),
        help_text=_('Total amount'),
    )

    t_thue_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng thuế ngoại tệ'),
        help_text=_('Total tax in foreign currency'),
    )

    t_thue = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tổng thuế'),
        help_text=_('Total tax amount'),
    )

    objects = ThuePhieuNhapDieuChinhGiaHangMuaModelManager()

    class Meta:
        abstract = True
        verbose_name = _('Thuế phiếu nhập điều chỉnh giá hàng mua')
        verbose_name_plural = _('Thuế phiếu nhập điều chỉnh giá hàng mua')
        indexes = [
            models.Index(fields=['phieu_nhap']),
            models.Index(fields=['ma_thue']),
            models.Index(fields=['ma_kh']),
            models.Index(fields=['tk_thue_no']),
            models.Index(fields=['tk_du']),
            models.Index(fields=['entity_model']),
            models.Index(fields=['line']),
        ]
        ordering = ['line']

    def __str__(self):  # noqa: C901
        return f'{self.phieu_nhap} - {self.line}: {self.ma_thue}'


class ThuePhieuNhapDieuChinhGiaHangMuaModel(
    ThuePhieuNhapDieuChinhGiaHangMuaModelAbstract
):
    """
    Base ThuePhieuNhapDieuChinhGiaHangMuaModel Implementation
    """

    class Meta(ThuePhieuNhapDieuChinhGiaHangMuaModelAbstract.Meta):
        abstract = False
        db_table = 'thue_phieu_nhap_dieu_chinh_gia_hang_mua'
