"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Contributions to this module:
- <PERSON> <<EMAIL>>

This module contains the ChiTietButToanDieuChinhGiamCongNoMuaHangModel, which represents
the detail lines of a debt reduction adjustment accounting entry in the purchase module.
"""

from decimal import Decimal
from uuid import uuid4

from django.core.exceptions import ValidationError
from django.db import models
from django.utils.translation import gettext_lazy as _

from django_ledger.models.mixins import CreateUpdateMixIn


class ChiTietButToanDieuChinhGiamCongNoModelQuerySet(models.QuerySet):
    """
    A custom defined QuerySet for the ChiTietButToanDieuChinhGiamCongNoModel.
    This QuerySet will be used to return a set of ChiTietButToanDieuChinhGiamCongNoModel that meet certain criteria.
    """

    def active(self):
        """
        Returns active ChiTietButToanDieuChinhGiamCongNoModel.

        Returns
        -------
        ChiTietButToanDieuChinhGiamCongNoModelQuerySet
            A QuerySet of active ChiTietButToanDieuChinhGiamCongNoModel.
        """
        return self.filter(but_toan__status="1")

    def for_but_toan(self, but_toan_uuid):
        """
        Returns ChiTietButToanDieuChinhGiamCongNoModel for a specific but_toan.

        Parameters
        ----------
        but_toan_uuid : UUID
            The but_toan UUID.

        Returns
        -------
        ChiTietButToanDieuChinhGiamCongNoModelQuerySet
            A QuerySet of ChiTietButToanDieuChinhGiamCongNoModel for the but_toan.
        """
        return self.filter(but_toan__uuid=but_toan_uuid)


class ChiTietButToanDieuChinhGiamCongNoModelManager(models.Manager):
    """
    A custom defined ChiTietButToanDieuChinhGiamCongNoModelManager that will act as an interface to handling the DB queries to the
    ChiTietButToanDieuChinhGiamCongNoModel.
    """

    def get_queryset(self):
        """
        Returns the custom QuerySet for the ChiTietButToanDieuChinhGiamCongNoModel.
        """
        return ChiTietButToanDieuChinhGiamCongNoModelQuerySet(
            self.model, using=self._db
        )

    def active(self):
        """
        Returns active ChiTietButToanDieuChinhGiamCongNoModel.

        Returns
        -------
        ChiTietButToanDieuChinhGiamCongNoModelQuerySet
            A QuerySet of active ChiTietButToanDieuChinhGiamCongNoModel.
        """
        return self.get_queryset().active()

    def for_but_toan(self, but_toan_uuid):
        """
        Returns ChiTietButToanDieuChinhGiamCongNoModel for a specific but_toan.

        Parameters
        ----------
        but_toan_uuid : UUID
            The but_toan UUID.

        Returns
        -------
        ChiTietButToanDieuChinhGiamCongNoModelQuerySet
            A QuerySet of ChiTietButToanDieuChinhGiamCongNoModel for the but_toan.
        """
        return self.get_queryset().for_but_toan(but_toan_uuid)

    def for_entity(self, entity_slug: str, user_model):
        """
        Returns ChiTietButToanDieuChinhGiamCongNoModel for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        user_model : User
            The user model.

        Returns
        -------
        ChiTietButToanDieuChinhGiamCongNoModelQuerySet
            A QuerySet of ChiTietButToanDieuChinhGiamCongNoModel for the entity.
        """
        qs = self.get_queryset()
        if entity_slug:
            return qs.filter(
                but_toan__entity_model__slug__exact=entity_slug,
                but_toan__entity_model__admin=user_model,
            )
        return qs


class ChiTietButToanDieuChinhGiamCongNoMuaHangModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiTietButToanDieuChinhGiamCongNoModel database will inherit from.
    The ChiTietButToanDieuChinhGiamCongNoModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().

    but_toan : ButToanDieuChinhGiamCongNoMuaHangModel
        Foreign key reference to the parent ButToanDieuChinhGiamCongNoMuaHangModel.

    stt : int
        Line sequence number.

    tk_no : str
        Debit account code.

    tk_co : str
        Credit account code.

    dien_giai : str
        Line description.

    so_tien_nt : Decimal
        Amount in foreign currency.

    so_tien : Decimal
        Amount in base currency.

    ma_kh : str
        Customer code.

    ma_vt : str
        Item/Material code.

    so_luong : Decimal
        Quantity.

    don_gia_nt : Decimal
        Unit price in foreign currency.

    don_gia : Decimal
        Unit price in base currency.

    ma_thue : str
        Tax code.

    thue_suat : Decimal
        Tax rate.

    tien_thue_nt : Decimal
        Tax amount in foreign currency.

    tien_thue : Decimal
        Tax amount in base currency.

    ma_bp : str
        Department code.

    ma_vv : str
        Job code.

    ma_phi : str
        Expense code.

    ma_kho : str
        Warehouse code.

    ma_td : str
        Field code.

    dien_giai_them : str
        Additional description.
    """

    uuid = models.UUIDField(default=uuid4, editable=False, primary_key=True)

    # Foreign key to parent model
    but_toan = models.ForeignKey(
        'django_ledger.ButToanDieuChinhGiamCongNoMuaHangModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet_items',
        verbose_name=_('But Toan Dieu Chinh Giam Cong No'),
    )

    # Line details
    stt = models.IntegerField(null=True, blank=True, verbose_name=_('Line Number'))
    tk_no = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Debit Account'),
        related_name='chi_tiet_but_toan_dieu_chinh_giam_cong_no_mua_hang_debit_set',
    )
    id_hd = models.CharField(
        max_length=50,
        verbose_name=_("ID hợp đồng"),
        help_text=_("Contract ID"),
        null=True,
        blank=True,
    )
    tk_co = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Credit Account'),
        related_name='chi_tiet_but_toan_dieu_chinh_giam_cong_no_mua_hang_credit_set',
    )
    dien_giai = models.CharField(
        max_length=255, null=True, blank=True, verbose_name=_('Description')
    )

    # Amount fields
    so_tien_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Amount (Foreign Currency)'),
    )
    so_tien = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Amount'),
    )

    tien_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Amount (Foreign Currency)'),
    )
    tien = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Amount'),
    )

    # Reference fields - ForeignKey relationships
    ma_kh = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Customer'),
        related_name='chi_tiet_but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )
    ma_vt = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet_but_toan_dieu_chinh_giam_cong_no_mua_hang_vat_tu_set',
        null=True,
        blank=True,
        verbose_name=_('Product ID'),
    )

    # Quantity and price
    so_luong = models.DecimalField(
        max_digits=15,
        decimal_places=3,
        default=Decimal('0.000'),
        verbose_name=_('Quantity'),
    )
    don_gia_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Unit Price (Foreign Currency)'),
    )
    don_gia = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Unit Price'),
    )

    # Tax fields
    ma_thue = models.ForeignKey(
        'django_ledger.TaxModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Tax'),
        related_name='chi_tiet_but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )
    thue_suat = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Tax Rate'),
    )
    tien_thue_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Tax Amount (Foreign Currency)'),
    )
    tien_thue = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Tax Amount'),
    )

    # Additional reference fields - ForeignKey relationships
    ma_bp = models.ForeignKey(
        'django_ledger.BoPhanModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Department'),
        related_name='chi_tiet_but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )
    ma_vv = models.ForeignKey(
        'django_ledger.VuViecModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Job'),
        related_name='chi_tiet_but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )
    ma_phi = models.ForeignKey(
        'django_ledger.PhiModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Expense'),
        related_name='chi_tiet_but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )
    ma_kho = models.ForeignKey(
        'django_ledger.KhoHangModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Warehouse'),
        related_name='chi_tiet_but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )
    ma_td = models.CharField(
        max_length=50, null=True, blank=True, verbose_name=_('Field Code')
    )
    dien_giai_them = models.CharField(
        max_length=255, null=True, blank=True, verbose_name=_('Additional Description')
    )

    so_ct_hd = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Số chứng từ hóa đơn"),
        help_text=_("Invoice document number"),
    )

    # Additional fields from API request
    du_cn = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Debt Balance'),
        help_text=_('Debt balance amount'),
    )
    ma_hd = models.ForeignKey(
        'django_ledger.ContractModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Contract'),
        related_name='chi_tiet_but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )
    ma_dtt = models.ForeignKey(
        'django_ledger.DotThanhToanModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Payment Object'),
        related_name='chi_tiet_but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )
    ma_ku = models.ForeignKey(
        'django_ledger.KheUocModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Loan'),
        related_name='chi_tiet_but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )
    ma_sp = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Product'),
        related_name='chi_tiet_but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )
    ma_lsx = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Production Order'),
        help_text=_('Production order code'),
    )
    ma_cp0 = models.ForeignKey(
        'django_ledger.ChiPhiKhongHopLeModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Invalid Cost Center'),
        related_name='chi_tiet_but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )

    objects = ChiTietButToanDieuChinhGiamCongNoModelManager()

    class Meta:
        abstract = True
        verbose_name = _('Purchase Debt Reduction Adjustment Detail')
        verbose_name_plural = _('Purchase Debt Reduction Adjustment Details')
        ordering = ['stt', 'created']
        indexes = [
            models.Index(fields=['but_toan', 'stt']),
            models.Index(fields=['but_toan', 'tk_no']),
            models.Index(fields=['but_toan', 'tk_co']),
            models.Index(fields=['but_toan', 'ma_kh']),
            models.Index(fields=['but_toan', 'ma_vt']),
        ]

    def __str__(self):
        return f'{self.but_toan.so_ct} - Line {self.stt}: {self.dien_giai}'

    def clean(self):
        """
        Validates the model data.
        """
        super().clean()

        if self.so_tien_nt and self.so_tien_nt < 0:
            raise ValidationError(_('Amount in foreign currency cannot be negative'))

        if self.so_tien and self.so_tien < 0:
            raise ValidationError(_('Amount cannot be negative'))

        if self.thue_suat and (self.thue_suat < 0 or self.thue_suat > 100):
            raise ValidationError(_('Tax rate must be between 0 and 100'))

        if self.so_luong and self.so_luong < 0:
            raise ValidationError(_('Quantity cannot be negative'))

    def save(self, *args, **kwargs):
        """
        Saves the model instance.
        """
        self.clean()
        super().save(*args, **kwargs)

    @property
    def total_amount_with_tax_nt(self):
        """
        Returns the total amount including tax in foreign currency.

        Returns
        -------
        Decimal
            The total amount including tax in foreign currency.
        """
        return self.so_tien_nt + self.tien_thue_nt

    @property
    def total_amount_with_tax(self):
        """
        Returns the total amount including tax in base currency.

        Returns
        -------
        Decimal
            The total amount including tax in base currency.
        """
        return self.so_tien + self.tien_thue

    def calculate_tax_amount(self):
        """
        Calculates the tax amount based on the amount and tax rate.
        """
        if self.thue_suat and self.so_tien:
            self.tien_thue = self.so_tien * (self.thue_suat / 100)

        if self.thue_suat and self.so_tien_nt:
            self.tien_thue_nt = self.so_tien_nt * (self.thue_suat / 100)


class ChiTietButToanDieuChinhGiamCongNoMuaHangModel(
    ChiTietButToanDieuChinhGiamCongNoMuaHangModelAbstract
):
    """
    Base ChiTietButToanDieuChinhGiamCongNoMuaHangModel from Abstract.
    """

    class Meta(ChiTietButToanDieuChinhGiamCongNoMuaHangModelAbstract.Meta):
        abstract = False
        db_table = "django_ledger_chi_tiet_but_toan_dieu_chinh_giam_cong_no_mua_hang"
