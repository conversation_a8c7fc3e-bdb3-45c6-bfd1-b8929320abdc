"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiTietPhieuNhapChiPhiMuaHangModel, which represents the details  # noqa: E501
of a purchase expense receipt.
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ChiTietPhieuNhapChiPhiMuaHangModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the ChiTietPhieuNhapChiPhiMuaHangModel.
    """

    def for_phieu_nhap(self, phieu_nhap_id):  # noqa: C901
        """
        Returns details for a specific purchase expense receipt.

        Parameters
        ----------
        phieu_nhap_id: UUID
            The PhieuNhapChiPhiMuaHangModel UUID used for filtering the QuerySet.

        Returns
        -------
        ChiTietPhieuNhapChiPhiMuaHangModelQueryset
            A QuerySet of ChiTietPhieuNhapChiPhiMuaHangModel with applied filters.
        """
        return self.filter(phieu_nhap_id=phieu_nhap_id)


class ChiTietPhieuNhapChiPhiMuaHangModelManager(Manager):
    """
    A custom defined ChiTietPhieuNhapChiPhiMuaHangModel Manager that will act as an interface to handle the  # noqa: E501
    ChiTietPhieuNhapChiPhiMuaHangModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom ChiTietPhieuNhapChiPhiMuaHangModelQueryset.
        """
        return ChiTietPhieuNhapChiPhiMuaHangModelQueryset(self.model, using=self._db)

    def for_phieu_nhap(self, phieu_nhap_id):  # noqa: C901
        """
        Returns details for a specific purchase expense receipt.

        Parameters
        ----------
        phieu_nhap_id: UUID
            The PhieuNhapChiPhiMuaHangModel UUID used for filtering the QuerySet.

        Returns
        -------
        ChiTietPhieuNhapChiPhiMuaHangModelQueryset
            A QuerySet of ChiTietPhieuNhapChiPhiMuaHangModel with applied filters.
        """
        return self.get_queryset().for_phieu_nhap(phieu_nhap_id=phieu_nhap_id)


class ChiTietPhieuNhapChiPhiMuaHangModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiTietPhieuNhapChiPhiMuaHangModel database will inherit from.  # noqa: E501
    The ChiTietPhieuNhapChiPhiMuaHangModel inherits functionality from the following MixIns:  # noqa: E501

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    phieu_nhap : ForeignKey
        Reference to the purchase expense receipt.
    line : IntegerField
        Line number.
    ma_vt : ForeignKey
        Material code.
    dvt : ForeignKey
        Unit of measure.
    ten_dvt : CharField
        Unit name.
    ma_kho : ForeignKey
        Warehouse code.
    ten_kho : CharField
        Warehouse name.
    he_so : DecimalField
        Conversion factor.
    so_luong : DecimalField
        Quantity.
    tien0 : DecimalField
        Original amount.
    cp_nt : DecimalField
        Expense in foreign currency.
    tk_vt : ForeignKey
        Material account.
    ten_tk_vt : CharField
        Material account name.
    ma_bp : ForeignKey
        Department code.
    """

    uuid = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    phieu_nhap = models.ForeignKey(
        'django_ledger.PhieuNhapChiPhiMuaHangModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet_phieu_nhaps',
        verbose_name=_('Phiếu nhập chi phí mua hàng'),
    )

    # Line information
    line = models.IntegerField(verbose_name=_("Số thứ tự"))
    # Material information
    ma_vt = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã vật tư'),
        related_name='chi_tiet_phieu_nhap_chi_phi_mua_hang',
        null=True,
    )
    dvt = models.ForeignKey(
        'django_ledger.DonViTinhModel',
        on_delete=models.CASCADE,
        verbose_name=_('Đơn vị tính'),
        related_name='chi_tiet_phieu_nhap_chi_phi_mua_hang',
        null=True,
    )
    ten_dvt = models.CharField(max_length=255, verbose_name=_('Tên đơn vị tính'))
    # Warehouse information
    ma_kho = models.ForeignKey(
        'django_ledger.KhoHangModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã kho'),
        related_name='chi_tiet_phieu_nhap_chi_phi_mua_hang',
        null=True,
    )
    ten_kho = models.CharField(max_length=255, verbose_name=_('Tên kho'))
    # Quantity and amount information
    he_so = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_('Hệ số')
    )
    so_luong = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_('Số lượng')
    )
    tien0 = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_('Tiền gốc')
    )
    cp_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_('Chi phí ngoại tệ'),
    )

    # Account information
    tk_vt = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('Tài khoản vật tư'),
        related_name='chi_tiet_phieu_nhap_chi_phi_mua_hang_tk_vt',
        null=True,
    )
    ten_tk_vt = models.CharField(max_length=255, verbose_name=_('Tên tài khoản vật tư'))
    # Department information
    ma_bp = models.ForeignKey(
        'django_ledger.BoPhanModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã bộ phận'),
        related_name='chi_tiet_phieu_nhap_chi_phi_mua_hang',
        null=True,
    )

    # Reference to invoice and invoice line (by UUID stored as char)
    id_hd = models.CharField(
        max_length=255,
        verbose_name=_("ID hóa đơn"),
        help_text=_("UUID of HoaDonMuaHangTrongNuocModel"),
        blank=True,
        null=True,
    )
    line_hd = models.CharField(
        max_length=255,
        verbose_name=_("Line hóa đơn"),
        help_text=_("UUID of ChiTietHoaDonMuaHangTrongNuocModel"),
        blank=True,
        null=True,
    )

    objects = ChiTietPhieuNhapChiPhiMuaHangModelManager()

    class Meta:
        abstract = True
        verbose_name = _('Chi tiết phiếu nhập chi phí mua hàng')
        verbose_name_plural = _('Chi tiết phiếu nhập chi phí mua hàng')
        indexes = [
            models.Index(fields=['phieu_nhap']),
            models.Index(fields=['ma_vt']),
            models.Index(fields=['dvt']),
            models.Index(fields=['ma_kho']),
            models.Index(fields=['tk_vt']),
            models.Index(fields=['ma_bp']),
        ]
        ordering = ['line']

    def __str__(self):  # noqa: C901
        return f'{self.phieu_nhap} - {self.line}: {self.ma_vt}'


class ChiTietPhieuNhapChiPhiMuaHangModel(ChiTietPhieuNhapChiPhiMuaHangModelAbstract):
    """
    Base ChiTietPhieuNhapChiPhiMuaHangModel Implementation
    """

    class Meta(ChiTietPhieuNhapChiPhiMuaHangModelAbstract.Meta):
        abstract = False
        db_table = 'chi_tiet_phieu_nhap_chi_phi_mua_hang'
