"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuChi (Payment Voucher) Model
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models._mixins.chung_tu_mixins import ChungTuMixIn  # noqa: F401,
from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class PhieuChiModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the PhieuChiModel.
    """

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Returns PhieuChiModel for a specific entity.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug used for filtering the QuerySet.

        Returns
        -------
        PhieuChiModelQueryset
            A QuerySet of PhieuChiModel with applied filters.
        """
        return self.filter(entity_model__slug=entity_slug)

    def active(self):  # noqa: C901
        """
        Returns active PhieuChiModel.

        Returns
        -------
        PhieuChiModelQueryset
            A QuerySet of active PhieuChiModel.
        """
        return self.filter(status="1")

    def inactive(self):  # noqa: C901
        """
        Returns inactive PhieuChiModel.

        Returns
        -------
        PhieuChiModelQueryset
            A QuerySet of inactive PhieuChiModel.
        """
        return self.filter(status="0")


class PhieuChiModelManager(Manager):
    """
    A custom defined PhieuChiModelManager that will act as an interface to handling the DB queries to the  # noqa: E501
    PhieuChiModel.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom QuerySet for the PhieuChiModel.
        """
        return PhieuChiModelQueryset(self.model, using=self._db)

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Returns PhieuChiModel for a specific entity.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug used for filtering the QuerySet.

        Returns
        -------
        PhieuChiModelQueryset
            A QuerySet of PhieuChiModel with applied filters.
        """
        return self.get_queryset().for_entity(entity_slug=entity_slug)

    def active(self):  # noqa: C901
        """
        Returns active PhieuChiModel.

        Returns
        -------
        PhieuChiModelQueryset
            A QuerySet of active PhieuChiModel.
        """
        return self.get_queryset().active()

    def inactive(self):  # noqa: C901
        """
        Returns inactive PhieuChiModel.

        Returns
        -------
        PhieuChiModelQueryset
            A QuerySet of inactive PhieuChiModel.
        """
        return self.get_queryset().inactive()


class PhieuChiModelAbstract(ChungTuMixIn, CreateUpdateMixIn):
    """
    This is the main abstract class which the PhieuChiModel database will inherit from.
    The PhieuChiModel inherits functionality from the following MixIns:

        1. :func:`ChungTuMixIn <django_ledger.models._mixins.chung_tu_mixins.ChungTuMixIn>`
        2. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501

    entity_model: EntityModel
        The EntityModel this PhieuChiModel belongs to.

    id: int
        ID of the payment voucher.

    ma_ngv: str
        Business operation code.

    dia_chi: str
        Address.

    ong_ba: str
        Person name.

    dien_giai: str
        Description.

    tk: AccountModel
        Account.

    unit_id: EntityUnitModel
        Entity unit.

    id_progress: int
        Progress ID.

    xprogress: str
        Progress status.

    so_ct0: ChungTuModel
        Original document number.

    so_ct_goc: int
        Original document number ID.

    dien_giai_ct_goc: str
        Original document description.

    ngay_ct0: date
        Original document date.

    xdatetime2: str
        Additional datetime field.

    ma_nt: NgoaiTeModel
        Currency.

    ty_gia: Decimal
        Exchange rate.

    t_tien_nt: Decimal
        Total amount in foreign currency.

    t_tien: Decimal
        Total amount.

    t_thue_nt: Decimal
        Total tax in foreign currency.

    t_thue: Decimal
        Total tax.

    status: str
        Status.

    transfer_yn: bool
        Transfer flag.

    hd_yn: bool
        Invoice flag.

    tg_dd: bool
        Exchange rate flag.

    cltg_yn: bool
        Exchange rate difference flag.

    ma_kh: CustomerModel
        Customer.

    ma_tt: HanThanhToanModel
        Payment term.

    xfile: str
        Attached file.

    created_by: str
        Created by user.
    """

    uuid = models.UUIDField(default=uuid4, editable=False, primary_key=True)
    entity_model = models.ForeignKey(
        "django_ledger.EntityModel",
        on_delete=models.CASCADE,
        verbose_name=_("Thực thể"),
    )
    id = models.CharField(max_length=255, null=True, blank=True, verbose_name=_("ID"))
    ma_ngv = models.CharField(
        max_length=50, null=True, blank=True, verbose_name=_("Mã người giao/nhận vốn")
    )
    dia_chi = models.TextField(null=True, blank=True, verbose_name=_("Địa chỉ"))
    ong_ba = models.CharField(
        max_length=100, verbose_name=_("Ông/Bà"), null=True, blank=True
    )
    dien_giai = models.CharField(
        max_length=255, verbose_name=_("Diễn giải"), null=True, blank=True
    )
    tk = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.CASCADE,
        related_name="phieu_chi_tk",
        verbose_name=_("Tài khoản"),
    )
    unit_id = models.ForeignKey(
        "django_ledger.EntityUnitModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Đơn vị"),
    )
    id_progress = models.IntegerField(default=0, verbose_name=_("ID tiến trình"))
    xprogress = models.CharField(
        max_length=50, null=True, blank=True, verbose_name=_("Tiến trình")
    )

    so_ct0 = models.CharField(
        max_length=50,
        verbose_name=_("Số CT0"),
        help_text=_("Original document number - manual input"),
        blank=True,
        null=True,
    )
    so_ct_goc = models.IntegerField(
        null=True, blank=True, verbose_name=_("Số chứng từ gốc (ID)")
    )
    dien_giai_ct_goc = models.CharField(
        max_length=255, null=True, blank=True, verbose_name=_("Diễn giải chứng từ gốc")
    )

    ngay_ct0 = models.DateField(
        null=True, blank=True, verbose_name=_("Ngày chứng từ gốc")
    )
    xdatetime2 = models.CharField(
        max_length=50, null=True, blank=True, verbose_name=_("Ngày giờ 2")
    )
    ma_nt = models.ForeignKey(
        "django_ledger.NgoaiTeModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã ngoại tệ"),
    )
    ty_gia = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_("Tỷ giá")
    )
    t_tien_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_("Tổng tiền ngoại tệ"),
    )
    t_tien = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_("Tổng tiền")
    )
    t_thue_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_("Tổng thuế ngoại tệ"),
    )
    t_thue = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_("Tổng thuế")
    )
    status = models.CharField(max_length=10, verbose_name=_("Trạng thái"))
    transfer_yn = models.BooleanField(default=False, verbose_name=_("Đã chuyển"))
    hd_yn = models.BooleanField(default=True, verbose_name=_("Có hóa đơn"))
    tg_dd = models.BooleanField(default=True, verbose_name=_("Tách gộp đối đầu"))
    cltg_yn = models.BooleanField(default=True, verbose_name=_("Chênh lệch tỷ giá"))
    ma_kh = models.ForeignKey(
        "django_ledger.CustomerModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="phieu_chi",
        verbose_name=_("Mã khách hàng"),
    )
    ma_tt = models.ForeignKey(
        "django_ledger.HanThanhToanModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="phieu_chi",
        verbose_name=_("Mã thanh toán"),
    )
    xfile = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("File đính kèm"),
    )
    created_by = models.CharField(
        max_length=50,
        default="qtrieukobietcode",
        verbose_name=_("Người tạo"),
    )

    # Ledger relationship for 1:1 mapping with payment voucher
    ledger = models.OneToOneField(
        'django_ledger.LedgerModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_("Sổ cái"),
        help_text=_("Sổ cái được tạo cho phiếu chi này"),
        related_name="phieu_chi",
    )

    objects = PhieuChiModelManager.from_queryset(PhieuChiModelQueryset)()

    class Meta:
        abstract = True
        verbose_name = _("Phiếu Chi")
        verbose_name_plural = _("Phiếu Chi")
        ordering = [
            '-created',
        ]  # Removed -ngay_ct since it's now in ChungTuItem
        indexes = [
            models.Index(fields=["entity_model"]),
            models.Index(fields=["unit_id"]),
            models.Index(fields=["tk"]),
            models.Index(fields=["ma_kh"]),
            models.Index(fields=["status"]),
            models.Index(fields=["ledger"]),
        ]
        # Removed unique_together with so_ct since it's now in ChungTuItem

    def __str__(self):  # noqa: C901
        return f"{self.i_so_ct or ''} - {self.dien_giai or ''}"


class PhieuChiModel(PhieuChiModelAbstract):
    """
    Base PhieuChi Model Implementation
    """

    class Meta(PhieuChiModelAbstract.Meta):
        abstract = False
        db_table = "phieu_chi"
