# Generated by Django 4.2.10 on 2025-09-04 04:03

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            'django_ledger',
            '0277_fix_chi_phi_chi_tiet_foreign_key_constraint',
        ),
    ]

    operations = [
        migrations.AlterField(
            model_name='khaibaothongtinccdcmodel',
            name='ghi_chu',
            field=models.TextField(blank=True, null=True, verbose_name='Ghi chú'),
        ),
        migrations.AlterField(
            model_name='khaibaothongtinccdcmodel',
            name='nam_sx',
            field=models.IntegerField(
                blank=True,
                default=0,
                null=True,
                validators=[django.core.validators.MinValueValidator(0)],
                verbose_name='Năm sản xuất',
            ),
        ),
        migrations.AlterField(
            model_name='khaibaothongtinccdcmodel',
            name='nuoc_sx',
            field=models.Char<PERSON>ield(
                blank=True, max_length=100, null=True, verbose_name='Nước sản xuất'
            ),
        ),
        migrations.AlterField(
            model_name='khaibaothongtinccdcmodel',
            name='so_hieu_cc',
            field=models.CharField(
                blank=True, max_length=50, null=True, verbose_name='Số hiệu công cụ'
            ),
        ),
        migrations.AlterField(
            model_name='khaibaothongtinccdcmodel',
            name='ten_cc2',
            field=models.CharField(
                blank=True, max_length=255, null=True, verbose_name='Tên công cụ 2'
            ),
        ),
    ]
