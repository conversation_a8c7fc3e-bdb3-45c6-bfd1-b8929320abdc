"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the HoaDonMuaHangTrongNuocModel, which represents a Domestic Purchase Invoice  # noqa: E501
received from a Supplier/Vendor, on which the Vendor states the amount owed by the recipient  # noqa: E501
for the purposes of supplying goods and/or services within the domestic market.

This model inherits from CreateUpdateMixIn and ChungTuMixIn following the phieu_xuat_kho pattern.
"""

from uuid import uuid4  # noqa: F401,

from django.core.exceptions import ValidationError  # noqa: F401,
from django.db import models, transaction  # noqa: F401,
from django.utils import timezone  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models._mixins.chung_tu_mixins import ChungTuMixIn  # noqa: F401,
from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class HoaDonMuaHangTrongNuocModelQueryset(models.QuerySet):
    """
    A custom defined HoaDonMuaHangTrongNuocModel QuerySet.
    """

    def active(self):  # noqa: C901
        """
        Returns active HoaDonMuaHangTrongNuocModel instances.
        """
        return self.filter(status="1")

    def inactive(self):  # noqa: C901
        """
        Returns inactive HoaDonMuaHangTrongNuocModel instances.
        """
        return self.exclude(status="1")

    def for_entity(self, entity_slug: str, user_model):
        return self.filter(entity_model__slug__exact=entity_slug)


class HoaDonMuaHangTrongNuocModelManager(models.Manager):
    """
    A custom defined HoaDonMuaHangTrongNuocModel Manager that will act as an interface to handle the  # noqa: E501
    HoaDonMuaHangTrongNuocModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom HoaDonMuaHangTrongNuocModelQueryset.
        """
        return HoaDonMuaHangTrongNuocModelQueryset(self.model, using=self._db)

    def for_entity(self, entity_slug, user_model):  # noqa: C901
        """
        Returns a QuerySet of HoaDonMuaHangTrongNuocModel associated with a specific EntityModel.  # noqa: E501

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug used for filtering the QuerySet.

        Returns
        -------
        HoaDonMuaHangTrongNuocModelQueryset
            A QuerySet of HoaDonMuaHangTrongNuocModel with applied filters.
        """
        return self.get_queryset().for_entity(
            entity_slug=entity_slug, user_model=user_model
        )


class HoaDonMuaHangTrongNuocModel(ChungTuMixIn, CreateUpdateMixIn):
    """
    HoaDonMuaHangTrongNuocModel - Domestic Purchase Invoice Model.

    This model inherits from:
    1. ChungTuMixIn - provides document management functionality
    2. CreateUpdateMixIn - provides UUID, timestamps, and soft delete functionality

    Follows the same pattern as PhieuXuatKhoModel for consistency.
    """

    uuid = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        verbose_name=_('UUID'),
        help_text=_('Unique identifier for the record'),
    )
    entity_model = models.ForeignKey(
        "django_ledger.EntityModel",
        on_delete=models.CASCADE,
        verbose_name=_("Entity Model"),
        related_name="hoa_don_mua_hang_trong_nuoc",
    )

    # Invoice flags
    hdmh_yn = models.BooleanField(verbose_name=_("HDMH Yes/No"))
    pn_yn = models.BooleanField(verbose_name=_("PN Yes/No"))
    pc_tao_yn = models.BooleanField(verbose_name=_("PC Tạo Yes/No"))
    ma_httt = models.CharField(
        max_length=10, null=True, blank=True, verbose_name=_("Mã HTTT")
    )
    xt_yn = models.BooleanField(verbose_name=_("XT Yes/No"))
    loai_ck = models.CharField(max_length=5, verbose_name=_("Loại CK"))
    ck_tl_nt = models.DecimalField(
        max_digits=15, decimal_places=2, verbose_name=_("CK TL NT")
    )
    ck_yn = models.BooleanField(verbose_name=_("Chiếc khấu Y/N"))
    # Vendor information
    ma_ngv = models.CharField(max_length=20, verbose_name=_("Mã NGV"))
    ma_kh = models.ForeignKey(
        "django_ledger.CustomerModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã khách hàng"),
        related_name="hoa_don_mua_hang_trong_nuoc",
        null=True,
        blank=True,
    )
    ten_kh = models.CharField(max_length=255, verbose_name=_("Tên KH"))
    ong_ba = models.CharField(max_length=255, verbose_name=_("Ông/Bà"))
    ma_nvmh = models.ForeignKey(
        "django_ledger.NhanVienModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã NVMH"),
        related_name="hoa_don_mua_hang_trong_nuoc",
        null=True,
        blank=True,
    )
    e_mail = models.CharField(
        max_length=255, verbose_name=_("Email"), blank=True, null=True
    )

    # Account information
    tk = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.CASCADE,
        verbose_name=_("Tài khoản"),
        related_name="hoa_don_mua_hang_trong_nuoc",
        null=True,
        blank=True,
    )
    ma_tt = models.ForeignKey(
        "django_ledger.HanThanhToanModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã TT"),
        related_name="hoa_don_mua_hang_trong_nuoc",
        null=True,
        blank=True,
    )
    dien_giai = models.TextField(verbose_name=_("Diễn giải"))

    # Document information
    unit_id = models.ForeignKey(
        "django_ledger.EntityUnitModel",
        on_delete=models.CASCADE,
        verbose_name=_("Unit ID"),
        related_name="hoa_don_mua_hang_trong_nuoc",
        null=True,
        blank=True,
    )
    so_ct0 = models.CharField(
        max_length=50,
        verbose_name=_("Số CT0"),
        help_text=_("Original document number - manual input"),
        blank=True,
    )
    ngay_ct0 = models.DateField(verbose_name=_("Ngày CT0"))
    so_ct2 = models.CharField(
        max_length=50,
        verbose_name=_("Số CT2"),
        help_text=_("Secondary document number - manual input"),
        blank=True,
    )

    # Receipt note information
    ma_nk_pn = models.ForeignKey(
        "django_ledger.QuyenChungTu",
        on_delete=models.CASCADE,
        verbose_name=_("Mã NK PN"),
        related_name="hoa_don_mua_hang_trong_nuoc_pn",
        blank=True,
        null=True,
    )
    ten_nk_pn = models.CharField(
        max_length=20, blank=True, verbose_name=_("Mã NK PN"), null=True
    )
    so_pn = models.CharField(
        max_length=50, verbose_name=_("Số PN"), null=True, blank=True
    )
    ngay_pn = models.DateField(verbose_name=_("Ngày PN"), null=True, blank=True)
    i_so_pn = models.CharField(
        max_length=20, verbose_name=_("Số PN"), blank=True, null=True
    )

    # Currency information
    ma_nt = models.ForeignKey(
        "django_ledger.NgoaiTeModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã NT"),
        related_name="hoa_don_mua_hang_trong_nuoc",
        null=True,
        blank=True,
    )
    ty_gia = models.DecimalField(
        max_digits=15, decimal_places=2, verbose_name=_("Tỷ giá")
    )

    # Status information
    status = models.CharField(max_length=5, verbose_name=_("Trạng thái"))
    transfer_yn = models.BooleanField(verbose_name=_("Transfer Yes/No"))

    # Payment information
    ma_gd = models.CharField(max_length=10, verbose_name=_("Mã GD"))
    pc_ngay_ct = models.DateField(verbose_name=_("Ngày CT PC"), blank=True, null=True)
    pc_ma_ct = models.CharField(
        max_length=20, verbose_name=_("Mã CT PC"), blank=True, null=True
    )
    pc_ma_nk = models.CharField(
        max_length=20, verbose_name=_("Mã NK PC"), blank=True, null=True
    )
    pc_tknh = models.CharField(
        max_length=20, blank=True, verbose_name=_("TK NH PC"), null=True
    )
    pc_tk = models.CharField(
        max_length=20, verbose_name=_("TK PC"), blank=True, null=True
    )
    pc_t_tt_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("PC TT NT"),
        blank=True,
        null=True,
    )

    # Total/Summary Fields
    t_so_luong = models.FloatField(verbose_name=_("Tổng số lượng"), default=0)
    t_cp_nt = models.FloatField(verbose_name=_("Tổng chi phí ngoại tệ"), default=0)
    t_cp = models.FloatField(verbose_name=_("Tổng chi phí"), default=0)
    t_ck_nt = models.FloatField(verbose_name=_("Tổng CK ngoại tệ"), default=0)
    t_ck = models.FloatField(verbose_name=_("Tổng CK"), default=0)
    t_thue_nt = models.FloatField(verbose_name=_("Tổng thuế ngoại tệ"), default=0)
    t_thue = models.FloatField(verbose_name=_("Tổng thuế"), default=0)
    t_tien_nt0 = models.FloatField(verbose_name=_("Tổng tiền ngoại tệ 0"), default=0)
    t_tien0 = models.FloatField(verbose_name=_("Tổng tiền 0"), default=0)
    t_ck_nt_ex = models.FloatField(verbose_name=_("CK ngoại tệ mở rộng"), default=0)
    t_ck_ex = models.FloatField(verbose_name=_("CK mở rộng"), default=0)
    t_tien_nt = models.FloatField(verbose_name=_("Tổng tiền ngoại tệ"), default=0)
    t_tien = models.FloatField(verbose_name=_("Tổng tiền"), default=0)
    t_tt_nt = models.FloatField(verbose_name=_("Tổng thanh toán ngoại tệ"), default=0)
    t_tt = models.FloatField(verbose_name=_("Tổng thanh toán"), default=0)

    # Trường này đánh dấu hóa đơn đã tất toán hoặc chưa
    tat_toan_yn = models.BooleanField(verbose_name=_("Tất toán"), default=False)

    # Audit information
    nguoi_tao = models.CharField(max_length=50, verbose_name=_("Người tạo"), blank=True)
    ngay_tao = models.DateTimeField(verbose_name=_("Ngày tạo"), auto_now_add=True)

    # ✅ 1-to-1 Ledger relationship with CASCADE delete
    ledger = models.OneToOneField(
        "django_ledger.LedgerModel",
        on_delete=models.CASCADE,
        verbose_name=_("Ledger"),
        related_name="hoa_don_mua_hang_trong_nuoc",
        null=True,
        blank=True,
        help_text=_("Associated ledger for accounting entries"),
    )

    # Manager
    objects = HoaDonMuaHangTrongNuocModelManager()

    class Meta:
        abstract = False
        db_table = "hoa_don_mua_hang_trong_nuoc"
        verbose_name = _("Hóa đơn mua hàng trong nước")
        verbose_name_plural = _("Hóa đơn mua hàng trong nước")
        indexes = [
            models.Index(fields=['entity_model']),
            models.Index(fields=['ma_kh']),
            models.Index(fields=['ma_nt']),
            models.Index(fields=['unit_id']),
            models.Index(fields=['status']),
            models.Index(fields=['ma_nvmh']),
            models.Index(fields=['ledger']),
        ]
        ordering = ['-created']

    def __str__(self):  # noqa: C901
        return f"{self.so_ct or 'N/A'} - {self.ten_kh}"
