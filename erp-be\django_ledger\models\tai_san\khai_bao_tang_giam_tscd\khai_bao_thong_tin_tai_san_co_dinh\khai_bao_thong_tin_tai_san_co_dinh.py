"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoThongTinTaiSanCoDinh (Fixed Asset Information Declaration) Model
"""

import uuid as uuid_lib  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class KhaiBaoThongTinTaiSanCoDinhModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the KhaiBaoThongTinTaiSanCoDinhModel.
    """

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Fetches a QuerySet of KhaiBaoThongTinTaiSanCoDinhModels for a specific entity.

        Parameters
        __________
        entity_slug: str
            The entity slug to filter by.
        """
        return self.filter(entity_model__slug__exact=entity_slug)

    def active(self):  # noqa: C901
        """
        Returns a QuerySet of active KhaiBaoThongTinTaiSanCoDinhModel instances.
        """
        return self.filter(status='1')


class KhaiBaoThongTinTaiSanCoDinhModelManager(Manager):
    """
    A custom defined Manager for the KhaiBaoThongTinTaiSanCoDinhModel.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom KhaiBaoThongTinTaiSanCoDinhModelQueryset.
        """
        return KhaiBaoThongTinTaiSanCoDinhModelQueryset(
            self.model, using=self._db
        )

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Returns KhaiBaoThongTinTaiSanCoDinhModel for a specific entity.

        Parameters
        ----------
        entity_slug: str
            The entity slug to filter by.

        Returns
        -------
        KhaiBaoThongTinTaiSanCoDinhModelQueryset
            A QuerySet of KhaiBaoThongTinTaiSanCoDinhModel with applied filters.
        """
        return self.get_queryset().for_entity(entity_slug=entity_slug)

    def for_entity_active(self, entity_slug):  # noqa: C901
        """
        Returns a QuerySet of active KhaiBaoThongTinTaiSanCoDinhModel for a specific entity.  # noqa: E501

        Parameters
        ----------
        entity_slug: str
            The entity slug to filter by.

        Returns
        -------
        KhaiBaoThongTinTaiSanCoDinhModelQueryset
            A QuerySet of active KhaiBaoThongTinTaiSanCoDinhModel with applied filters.
        """
        return self.for_entity(entity_slug=entity_slug).active()


class KhaiBaoThongTinTaiSanCoDinhModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the KhaiBaoThongTinTaiSanCoDinhModel database will inherit from.  # noqa: E501
    The KhaiBaoThongTinTaiSanCoDinhModel inherits functionality from the following MixIns:  # noqa: E501

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid_lib.uuid4().  # noqa: E501

    entity_model : EntityModel
        The entity that this fixed asset information declaration belongs to.

    ma_ts : str
        The fixed asset code.

    ten_ts : str
        The fixed asset name.

    ma_lts : str
        The fixed asset type code.

    ngay_mua : date
        The purchase date.

    ngay_kh0 : date
        The depreciation start date.

    so_ky_kh : int
        The number of depreciation periods.

    ngay_kh_kt : date
        The depreciation end date.

    so_luong : decimal
        The quantity.

    dvt : str
        The unit of measure.

    unit_id : str
        The unit ID.

    ma_nt : Currency
        The currency code.

    ty_gia : decimal
        The exchange rate.

    tk_ts : Account
        The asset account.

    tk_kh : Account
        The depreciation account.

    tk_cp : Account
        The expense account.

    ma_bp : BoPhan
        The department code.

    kieu_kh : str
        The depreciation type.

    ty_le_kh : decimal
        The depreciation rate.

    nguyen_gia_nt : decimal
        The original price in foreign currency.

    nguyen_gia : decimal
        The original price.

    gt_da_kh_nt : decimal
        The depreciated value in foreign currency.

    gt_da_kh : decimal
        The depreciated value.

    gt_cl_nt : decimal
        The remaining value in foreign currency.

    gt_cl : decimal
        The remaining value.

    gt_kh_ky_nt : decimal
        The period depreciation value in foreign currency.

    gt_kh_ky : decimal
        The period depreciation value.

    status : str
        The status of the fixed asset information declaration.

    so_hieu_ts : str
        The fixed asset number.

    so_ct : ChungTu
        The document number.

    ngay_ct : date
        The document date.

    ma_barcode : str
        The barcode.

    nh_ts1 : GroupModel
        The fixed asset group 1.

    nh_ts2 : GroupModel
        The fixed asset group 2.

    nh_ts3 : GroupModel
        The fixed asset group 3.

    nuoc_sx : str
        The country of manufacture.

    nam_sx : str
        The year of manufacture.

    ghi_chu : str
        The note.
    """

    uuid = models.UUIDField(
        primary_key=True,
        default=uuid_lib.uuid4,
        editable=False,
        verbose_name=_("UUID"),
    )
    entity_model = models.ForeignKey(
        'django_ledger.EntityModel',
        on_delete=models.CASCADE,
        verbose_name=_('Entity Model'),
        related_name='khai_bao_thong_tin_tai_san_co_dinh',
    )
    ma_ts = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Mã tài sản"),
    )
    ten_ts = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Tên tài sản"),
    )
    ma_lts = models.ForeignKey(
        'django_ledger.LoaiTaiSanCongCuModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='khai_bao_thong_tin_tai_san_co_dinh',
        verbose_name=_("Loại tài sản"),
        help_text=_("Asset type")
    )
    ngay_mua = models.DateField(
        null=True, blank=True, verbose_name=_("Ngày mua")
    )
    ngay_kh0 = models.DateField(
        null=True, blank=True, verbose_name=_("Ngày khấu hao bắt đầu")
    )
    so_ky_kh = models.IntegerField(
        null=True, blank=True, verbose_name=_("Số kỳ khấu hao")
    )
    ngay_kh_kt = models.DateField(
        null=True, blank=True, verbose_name=_("Ngày khấu hao kết thúc")
    )
    so_luong = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Số lượng"),
    )
    dvt = models.ForeignKey(
        'django_ledger.DonViTinhModel',
        on_delete=models.CASCADE,
        verbose_name=_('Đơn vị tính'),
        related_name='khai_bao_thong_tin_tai_san_co_dinh',
        null=True,
        blank=True,
        help_text=_("Unit of measure for the asset"),
    )
    unit_id = models.ForeignKey(
        'django_ledger.EntityUnitModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Đơn vị'),
        related_name='khai_bao_thong_tin_tai_san_co_dinh',
        help_text=_('Unit ID'),
    )
    # Currency information
    ma_nt = models.ForeignKey(
        'django_ledger.NgoaiTeModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã ngoại tệ'),
        related_name='khai_bao_thong_tin_tai_san_co_dinh',
        null=True,
        blank=True,
        help_text=_("Currency used in the transaction"),
    )
    ty_gia = models.DecimalField(
        max_digits=20,
        decimal_places=4,
        null=True,
        blank=True,
        verbose_name=_("Tỷ giá"),
    )
    # Asset account information
    tk_ts = models.ForeignKey(
        'django_ledger.AccountModel',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name="ts_taisan",
        verbose_name=_("Tài khoản tài sản"),
        help_text=_("Account used to track the asset value"),
    )
    # Depreciation account information
    tk_kh = models.ForeignKey(
        'django_ledger.AccountModel',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name="ts_khauhao",
        verbose_name=_("Tài khoản khấu hao"),
        help_text=_("Account used to track accumulated depreciation"),
    )
    # Expense account information
    tk_cp = models.ForeignKey(
        'django_ledger.AccountModel',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name="ts_chiphi",
        verbose_name=_("Tài khoản chi phí"),
        help_text=_("Account used to track depreciation expenses"),
    )
    # Department information
    ma_bp = models.ForeignKey(
        'django_ledger.BoPhanSuDungTSModel',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="khai_bao_thong_tin_tai_san_co_dinh_bo_phan",
        verbose_name=_("Mã bộ phận sử dụng tài sản"),
        help_text=_("Department that owns the asset"),
    )
    kieu_kh = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Kiểu khấu hao"),
    )
    ty_le_kh = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Tỷ lệ khấu hao"),
    )
    nguyen_gia_nt = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Nguyên giá NT"),
    )
    nguyen_gia = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Nguyên giá"),
    )
    gt_da_kh_nt = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Giá trị đã khấu hao NT"),
    )
    gt_da_kh = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Giá trị đã khấu hao"),
    )
    gt_cl_nt = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Giá trị còn lại NT"),
    )
    gt_cl = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Giá trị còn lại"),
    )
    gt_kh_ky_nt = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Giá trị khấu hao kỳ NT"),
    )
    gt_kh_ky = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Giá trị khấu hao kỳ"),
    )
    status = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Trạng thái"),
    )
    so_hieu_ts = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Số hiệu tài sản"),
    )
    # Document information
    so_ct = models.ForeignKey(
        'django_ledger.ChungTu',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="khai_bao_thong_tin_tai_san_co_dinh_chung_tu",
        verbose_name=_("Số chứng từ"),
        help_text=_("Document reference for the asset"),
    )
    ngay_ct = models.DateField(
        null=True, blank=True, verbose_name=_("Ngày chứng từ")
    )
    ma_barcode = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Mã barcode"),
    )
    # Asset group information
    nh_ts1 = models.ForeignKey(
        'django_ledger.GroupModel',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="nhom_taisan_1",
        verbose_name=_("Nhóm tài sản 1"),
        help_text=_("Primary asset group classification"),
    )
    # Secondary asset group
    nh_ts2 = models.ForeignKey(
        'django_ledger.GroupModel',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="nhom_taisan_2",
        verbose_name=_("Nhóm tài sản 2"),
        help_text=_("Secondary asset group classification"),
    )
    # Tertiary asset group
    nh_ts3 = models.ForeignKey(
        'django_ledger.GroupModel',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="nhom_taisan_3",
        verbose_name=_("Nhóm tài sản 3"),
        help_text=_("Tertiary asset group classification"),
    )
    ten_ts2 = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Tên tài sản 2"),
    )
    nuoc_sx = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Nước sản xuất"),
    )
    nam_sx = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name=_("Năm sản xuất"),
    )
    ghi_chu = models.TextField(null=True, blank=True, verbose_name=_("Ghi chú"))
    objects = KhaiBaoThongTinTaiSanCoDinhModelManager.from_queryset(
        KhaiBaoThongTinTaiSanCoDinhModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _('Khai báo thông tin tài sản cố định')
        verbose_name_plural = _('Khai báo thông tin tài sản cố định')
        indexes = [
            models.Index(fields=['entity_model']),
            models.Index(fields=['ma_ts']),
            models.Index(fields=['status']),
        ]

    def __str__(self):  # noqa: C901
        return f'{self.ma_ts} - {self.ten_ts}'


class KhaiBaoThongTinTaiSanCoDinhModel(
    KhaiBaoThongTinTaiSanCoDinhModelAbstract
):
    """
    Base Fixed Asset Information Declaration Model Implementation
    """

    class Meta(KhaiBaoThongTinTaiSanCoDinhModelAbstract.Meta):
        abstract = False
        db_table = 'django_ledger_khai_bao_thong_tin_tai_san_co_dinh'
