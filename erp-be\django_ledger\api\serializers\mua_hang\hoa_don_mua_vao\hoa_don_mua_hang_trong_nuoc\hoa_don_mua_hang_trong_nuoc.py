"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Hoa Don Mua Hang Trong Nuoc (Domestic Purchase Invoice) serializer implementation.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers._utils.chung_tu_fields.chung_tu_fields import (  # noqa: F401
    ChungTuSerializerMixin,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_phi_chi_tiet_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401
    ChiPhiChiTietHoaDonMuaHangTrongNuocModelCreateUpdateSerializer,
    ChiPhiChiTietHoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_phi_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ChiPhiHoaDonMuaHangTrongNuocModelCreateUpdateSerializer,
    ChiPhiHoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_tiet_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ChiTietHoaDonMuaHangTrongNuocModelCreateUpdateSerializer,
    ChiTietHoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.thue_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ThueHoaDonMuaHangTrongNuocModelCreateUpdateSerializer,
    ThueHoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.api.serializers.unit import (  # noqa: F401,
    EntityUnitModelSimpleSerializer,
)
from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    HoaDonMuaHangTrongNuocModel,
)


class HoaDonMuaHangTrongNuocModelSerializer(
    ChungTuSerializerMixin, serializers.ModelSerializer
):
    """
    Serializer for the HoaDonMuaHangTrongNuocModel.

    This serializer handles the conversion between HoaDonMuaHangTrongNuocModel instances and JSON representations,  # noqa: E501
    supporting both serialization (model to JSON) and deserialization (JSON to model).
    """

    # Read-only fields for related objects
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    ma_nvmh_data = serializers.SerializerMethodField(read_only=True)
    tk_data = serializers.SerializerMethodField(read_only=True)
    ma_tt_data = serializers.SerializerMethodField(read_only=True)
    ma_nk_pn_data = serializers.SerializerMethodField(read_only=True)
    so_ct0_data = serializers.SerializerMethodField(read_only=True)
    so_ct2_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    # Add nested collections using SerializerMethodField
    chi_tiet_hoa_don = serializers.SerializerMethodField(read_only=True)
    chi_phi_hoa_don = serializers.SerializerMethodField(read_only=True)
    chi_phi_chi_tiet_hoa_don = serializers.SerializerMethodField(read_only=True)
    thue_hoa_don = serializers.SerializerMethodField(read_only=True)
    unit_id_data = serializers.SerializerMethodField(read_only=True)

    # Related document UUID arrays for purchase invoices
    phieu_chi_uuids = serializers.ListField(
        child=serializers.CharField(), read_only=True, default=list
    )
    giay_bao_no_uuids = serializers.ListField(
        child=serializers.CharField(), read_only=True, default=list
    )

    # Related PhieuNhapKho UUID for purchase invoices
    phieu_nhap_kho_uuid = serializers.CharField(
        read_only=True, allow_blank=True, default=''
    )


    class Meta:
        model = HoaDonMuaHangTrongNuocModel
        fields = [
            "uuid",
            "entity_model",
            "nguoi_tao",
            "ngay_tao",
            "hdmh_yn",
            "pn_yn",
            "pc_tao_yn",
            "ma_httt",
            "xt_yn",
            "loai_ck",
            "ck_tl_nt",
            "ck_yn",
            "ma_gd",
            "ma_ngv",
            "ma_kh",
            "ma_kh_data",
            "ten_kh",
            "ong_ba",
            "ma_nvmh",
            "ma_nvmh_data",
            "e_mail",
            "tk",
            "tk_data",
            "ma_tt",
            "ma_tt_data",
            "dien_giai",
            "unit_id",
            "unit_id_data",
            "i_so_ct",
            "ma_nk",
            "ma_nk_data",
            "so_ct",
            "ngay_ct",
            "ngay_lct",
            "so_ct0",
            "so_ct0_data",
            "ngay_ct0",
            "so_ct2",
            "so_ct2_data",
            "ma_nk_pn",
            "ma_nk_pn_data",
            "so_pn",
            "ngay_pn",
            "i_so_pn",
            "ma_nt",
            "ma_nt_data",
            "ty_gia",
            "status",
            "transfer_yn",
            "pc_ngay_ct",
            "pc_ma_ct",
            "pc_ma_nk",
            "pc_tknh",
            "pc_tk",
            "pc_t_tt_nt",
            "t_so_luong",
            "t_cp_nt",
            "t_cp",
            "t_ck_nt",
            "t_ck",
            "t_thue_nt",
            "t_thue",
            "t_tien_nt0",
            "t_tien0",
            "t_ck_nt_ex",
            "t_ck_ex",
            "t_tien_nt",
            "t_tien",
            "t_tt_nt",
            "t_tt",
            "chi_tiet_hoa_don",
            "chi_phi_hoa_don",
            "chi_phi_chi_tiet_hoa_don",
            "thue_hoa_don",
            "phieu_chi_uuids",
            "giay_bao_no_uuids",
            "phieu_nhap_kho_uuid",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "entity_model_data",
            "ma_kh_data",
            "ma_nvmh_data",
            "tk_data",
            "ma_tt_data",
            "ma_nk_pn_data",
            "so_ct0_data",
            "so_ct2_data",
            "ma_nt_data",
            "chi_tiet_hoa_don",
            "chi_phi_hoa_don",
            "chi_phi_chi_tiet_hoa_don",
            "thue_hoa_don",
            "phieu_chi_uuids",
            "giay_bao_no_uuids",
            "phieu_nhap_kho_uuid",
            "created",
            "updated",
        ]

    def get_ck_yn(self, obj):
        """Get discount flag."""
        if obj.loai_ck not in [1, 2, 3]:
            return False
        return True

    def get_ma_kh_data(self, obj):  # noqa: C901
        """
        Get basic information about the customer
        """
        if not obj.ma_kh or not hasattr(obj.ma_kh, 'uuid'):
            return None

        return {
            "uuid": str(obj.ma_kh.uuid),
            "customer_number": getattr(obj.ma_kh, "customer_number", ""),
            "customer_name": getattr(obj.ma_kh, "customer_name", ""),
            "address": getattr(obj.ma_kh, "address", ""),
            "tax_id": getattr(obj.ma_kh, "tax_id", ""),
            "customer_code": getattr(obj.ma_kh, "customer_code", ""),
        }

    def get_ma_nvmh_data(self, obj):  # noqa: C901
        """
        Get basic information about the purchasing staff
        """
        if not obj.ma_nvmh or not hasattr(obj.ma_nvmh, 'uuid'):
            return None

        return {
            "uuid": str(obj.ma_nvmh.uuid),
            "ho_ten_nhan_vien": getattr(obj.ma_nvmh, "ho_ten_nhan_vien", ""),
            "ma_nhan_vien": getattr(obj.ma_nvmh, "ma_nhan_vien", ""),
        }

    def get_tk_data(self, obj):  # noqa: C901
        """
        Get basic information about the account
        """
        if not obj.tk or not hasattr(obj.tk, 'uuid'):
            return None

        return {
            "uuid": str(obj.tk.uuid),
            "code": getattr(obj.tk, "code", ""),
            "name": getattr(obj.tk, "name", ""),
            "account_type": getattr(obj.tk, "role", ""),
        }

    def get_ma_tt_data(self, obj):  # noqa: C901
        """
        Get basic information about the payment method
        """
        if not obj.ma_tt or not hasattr(obj.ma_tt, 'uuid'):
            return None

        return {
            "uuid": str(obj.ma_tt.uuid),
            "ma_tt": getattr(obj.ma_tt, "ma_tt", ""),
            # "ten_tt": getattr(obj.ma_tt, "ten_tt", ""),  # Field removed from HanThanhToanModel
        }

    def get_ma_nk_pn_data(self, obj):  # noqa: C901
        """
        Get basic information about the receipt document book
        """
        if not obj.ma_nk_pn or not hasattr(obj.ma_nk_pn, 'uuid'):
            return None

        return {
            "uuid": str(obj.ma_nk_pn.uuid),
            "ma_nk": getattr(obj.ma_nk_pn, "ma_nk", ""),
            "ten_nk": getattr(obj.ma_nk_pn, "ten_nk", ""),
        }

    def get_so_ct_data(self, obj):  # noqa: C901
        """
        Get basic information about the document
        """
        if not obj.so_ct:
            return None

        # so_ct is a CharField, not a ForeignKey
        return {
            "value": str(obj.so_ct),
        }

    def get_so_ct0_data(self, obj):  # noqa: C901
        """
        Get basic information about the reference document
        """
        if not obj.so_ct0:
            return None

        # so_ct0 is a CharField, not a ForeignKey
        return {
            "value": str(obj.so_ct0),
        }

    def get_so_ct2_data(self, obj):  # noqa: C901
        """
        Get basic information about the secondary reference document
        """
        if not obj.so_ct2:
            return None

        # so_ct2 is a CharField, not a ForeignKey
        return {
            "value": str(obj.so_ct2),
        }

    def get_ma_nt_data(self, obj):  # noqa: C901
        """
        Get basic information about the currency
        """
        if not obj.ma_nt or not hasattr(obj.ma_nt, 'uuid'):
            return None

        return {
            "uuid": str(obj.ma_nt.uuid),
            "ma_nt": getattr(obj.ma_nt, "ma_nt", ""),
            "ten_nt": getattr(obj.ma_nt, "ten_nt", ""),
        }

    def get_chi_tiet_hoa_don(self, obj):  # noqa: C901
        """
        Get the chi_tiet_hoa_don (details) for the HoaDonMuaHangTrongNuoc.
        Uses service method like phieu_xuat_kho pattern to handle data filtering.
        """
        chi_tiet_hoa_don_qs = obj.chi_tiet_hoa_don.filter(deleted__isnull=True)
        return ChiTietHoaDonMuaHangTrongNuocModelSerializer(
            chi_tiet_hoa_don_qs, many=True
        ).data

    def get_chi_phi_hoa_don(self, obj):  # noqa: C901
        """
        Get the chi_phi_hoa_don (expenses) for the HoaDonMuaHangTrongNuoc.
        """
        chi_phi = obj.chi_phi_hoa_don.filter(deleted__isnull=True)
        return ChiPhiHoaDonMuaHangTrongNuocModelSerializer(chi_phi, many=True).data

    def get_chi_phi_chi_tiet_hoa_don(self, obj):  # noqa: C901
        """
        Get the chi_phi_chi_tiet_hoa_don (expense details) for the HoaDonMuaHangTrongNuoc.  # noqa: E501
        """
        chi_phi_chi_tiet = obj.chi_phi_chi_tiet_hoa_don.filter(deleted__isnull=True)
        return ChiPhiChiTietHoaDonMuaHangTrongNuocModelSerializer(
            chi_phi_chi_tiet, many=True
        ).data

    def get_thue_hoa_don(self, obj):  # noqa: C901
        """
        Get the thue_hoa_don (taxes) for the HoaDonMuaHangTrongNuoc.
        """
        thue = obj.thue_hoa_don.filter(deleted__isnull=True)
        return ThueHoaDonMuaHangTrongNuocModelSerializer(thue, many=True).data

    def get_unit_id_data(self, obj):  # noqa: C901
        if obj.unit_id:
            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None


class HoaDonMuaHangTrongNuocModelCreateUpdateSerializer(
    ChungTuSerializerMixin, serializers.ModelSerializer
):
    """
    Serializer for the HoaDonMuaHangTrongNuocModel.
    Used for create and update operations.

    This serializer handles the conversion between JSON data and HoaDonMuaHangTrongNuocModel instances,  # noqa: E501
    supporting both creation of new instances and updating existing ones.

    Key features:
    - Accepts nested data for related collections (chi_tiet_hoa_don, chi_phi_hoa_don, etc.)  # noqa: E501
    - Performs validation on the entire data structure
    - Accepts UUID references for foreign key fields
    """

    chi_tiet_hoa_don = ChiTietHoaDonMuaHangTrongNuocModelCreateUpdateSerializer(
        many=True, required=False
    )
    chi_phi_hoa_don = ChiPhiHoaDonMuaHangTrongNuocModelCreateUpdateSerializer(
        many=True, required=False
    )
    chi_phi_chi_tiet_hoa_don = (
        ChiPhiChiTietHoaDonMuaHangTrongNuocModelCreateUpdateSerializer(
            many=True, required=False
        )
    )
    thue_hoa_don = ThueHoaDonMuaHangTrongNuocModelCreateUpdateSerializer(
        many=True, required=False
    )

    # Related document UUID arrays for purchase invoices
    phieu_chi_uuids = serializers.ListField(
        child=serializers.CharField(), read_only=True, default=list
    )
    giay_bao_no_uuids = serializers.ListField(
        child=serializers.CharField(), read_only=True, default=list
    )

    # Related PhieuNhapKho UUID for purchase invoices
    phieu_nhap_kho_uuid = serializers.CharField(
        read_only=True, allow_blank=True, default=''
    )

    class Meta:
        model = HoaDonMuaHangTrongNuocModel
        fields = [
            "uuid",
            "entity_model",
            "hdmh_yn",
            "pn_yn",
            "pc_tao_yn",
            "ma_httt",
            "xt_yn",
            "ck_yn",
            "loai_ck",
            "ck_tl_nt",
            "ma_ngv",
            "ma_kh",
            "ten_kh",
            "ong_ba",
            "ma_nvmh",
            "e_mail",
            "tk",
            "ma_tt",
            "dien_giai",
            "unit_id",
            "i_so_ct",
            "ma_nk",
            "so_ct",
            "ngay_ct",
            "ngay_lct",
            "so_ct0",
            "ngay_ct0",
            "so_ct2",
            "ma_nk_pn",
            "so_pn",
            "ngay_pn",
            "i_so_pn",
            "ma_nt",
            "ty_gia",
            "status",
            "transfer_yn",
            "ma_gd",
            "pc_ngay_ct",
            "pc_ma_ct",
            "pc_ma_nk",
            "pc_tknh",
            "pc_tk",
            "pc_t_tt_nt",
            "t_so_luong",
            "t_cp_nt",
            "t_cp",
            "t_ck_nt",
            "t_ck",
            "t_thue_nt",
            "t_thue",
            "t_tien_nt0",
            "t_tien0",
            "t_ck_nt_ex",
            "t_ck_ex",
            "t_tien_nt",
            "t_tien",
            "t_tt_nt",
            "t_tt",
            "nguoi_tao",
            "ngay_tao",
            "chi_tiet_hoa_don",
            "chi_phi_hoa_don",
            "chi_phi_chi_tiet_hoa_don",
            "thue_hoa_don",
            "phieu_chi_uuids",
            "giay_bao_no_uuids",
            "phieu_nhap_kho_uuid",
        ]
        read_only_fields = [
            "uuid",
            "entity_model",
            "phieu_chi_uuids",
            "giay_bao_no_uuids",
            "phieu_nhap_kho_uuid",
            "created",
            "updated",
            "nguoi_tao",
            "ngay_tao",
        ]
        extra_kwargs = {
            'pc_ngay_ct': {'required': False},
            'pc_ma_ct': {'required': False},
            'pc_ma_nk': {'required': False},
            'pc_tk': {'required': False},
            'pc_t_tt_nt': {'required': False},
            'i_so_pn': {'required': False},
            'so_ct0': {'required': False},
            'so_ct2': {'required': False},
            'ma_nk_pn': {'required': False},
            # Make most fields optional for partial updates
            'hdmh_yn': {'required': False},
            'pn_yn': {'required': False},
            'pc_tao_yn': {'required': False},
            'ma_httt': {'required': False},
            'xt_yn': {'required': False},
            'ck_yn': {'required': False},
            'loai_ck': {'required': False},
            'ck_tl_nt': {'required': False},
            'ma_ngv': {'required': False},
            'ten_kh': {'required': False},
            'ong_ba': {'required': False},
            'ma_nvmh': {'required': False},
            'e_mail': {'required': False},
            'tk': {'required': False},
            'ma_tt': {'required': False},
            'dien_giai': {'required': False},
            'unit_id': {'required': False},
            'ma_nk': {'required': False},
            'ngay_ct': {'required': False},
            'ngay_lct': {'required': False},
            'ngay_ct0': {'required': False},
            'so_pn': {'required': False},
            'ngay_pn': {'required': False},
            'ty_gia': {'required': False},
            'status': {'required': False},
            'transfer_yn': {'required': False},
            'ma_gd': {'required': False},
        }

    def validate(self, data):  # noqa: C901
        """
        Validate the data before creating or updating the model instance.

        Parameters
        ----------
        data : dict
            The data to validate.

        Returns
        -------
        dict
            The validated data.
        """
        # Handle empty string fields that should be excluded from updates
        # These fields have blank=True but may have NOT NULL constraints in DB
        empty_string_fields = ['pc_ma_ct', 'pc_ma_nk', 'pc_tknh', 'pc_tk']
        for field in empty_string_fields:
            if field in data and data[field] == "":
                # Remove empty string fields to avoid NOT NULL constraint errors
                data.pop(field, None)

        # Clean nested data by removing UUID fields that are auto-generated

        # Clean chi_phi_hoa_don data
        if 'chi_phi_hoa_don' in data:
            for chi_phi_data in data['chi_phi_hoa_don']:
                # Remove UUID field that is auto-generated
                chi_phi_data.pop('uuid', None)

        # Clean thue_hoa_don data
        if 'thue_hoa_don' in data:
            for thue_data in data['thue_hoa_don']:
                # Remove UUID field that is auto-generated
                thue_data.pop('uuid', None)

        # Clean chi_tiet_hoa_don data
        if 'chi_tiet_hoa_don' in data:
            for chi_tiet_data in data['chi_tiet_hoa_don']:
                # Remove UUID field that is auto-generated
                chi_tiet_data.pop('uuid', None)

        # Auto-populate entity_model from context if not provided
        if not data.get('entity_model') and 'entity_slug' in self.context:
            from django_ledger.models.entity import EntityModel

            try:
                entity_model = EntityModel.objects.get(slug=self.context['entity_slug'])
                data['entity_model'] = entity_model
            except EntityModel.DoesNotExist:
                pass

        return data

    # ChungTuSerializerMixin provides built-in validation for so_ct
    # No need for manual validation methods

    def to_internal_value(self, data):
        """
        Clean nested data before validation.
        """
        # Clean nested data by removing UUID fields that are auto-generated

        # Clean chi_phi_hoa_don data
        if 'chi_phi_hoa_don' in data:
            for chi_phi_data in data['chi_phi_hoa_don']:
                # Remove UUID field that is auto-generated
                chi_phi_data.pop('uuid', None)

        # Clean thue_hoa_don data
        if 'thue_hoa_don' in data:
            for thue_data in data['thue_hoa_don']:
                # Remove UUID field that is auto-generated
                thue_data.pop('uuid', None)

        # Clean chi_tiet_hoa_don data
        if 'chi_tiet_hoa_don' in data:
            for chi_tiet_data in data['chi_tiet_hoa_don']:
                # Remove UUID field that is auto-generated
                chi_tiet_data.pop('uuid', None)

        return super().to_internal_value(data)
