"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for ChiTietDonMuaHang (Purchase Order Detail) model.
"""

from decimal import Decimal

from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from django_ledger.api.serializers.base import GlobalModelSerializer  # noqa: E402
from django_ledger.models.mua_hang.don_mua_hang.chi_tiet_don_mua_hang import (  # noqa: E402
    ChiTietDonMuaHangModel,
)


class ChiTietDonMuaHangSerializer(GlobalModelSerializer):
    """
    Serializer for ChiTietDonMuaHangModel (Purchase Order Detail)
    """

    entity_model = serializers.UUIDField(read_only=True)
    don_mua_hang = serializers.UUIDField(read_only=True)
    # Nested data fields for reference fields
    ma_vt_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)
    don_mua_hang_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietDonMuaHangModel
        fields = [
            "uuid",
            "entity_model",
            "don_mua_hang",
            "don_mua_hang_data",
            "line",
            "ma_vt",
            "ma_vt_data",
            "dvt",
            "dvt_data",
            "so_luong",
            "gia_nt",
            "tien_nt",
            "ngay_giao",
            "ma_thue",
            "ma_thue_data",
            "thue_suat",
            "thue_nt",
            "ma_bp",
            "ma_bp_data",
            "ma_vv",
            "ma_vv_data",
            "ma_hd",
            "ma_hd_data",
            "ma_dtt",
            "ma_dtt_data",
            "ma_ku",
            "ma_ku_data",
            "ma_phi",
            "ma_phi_data",
            "ma_sp",
            "ma_sp_data",
            "ma_lsx",
            "ma_cp0",
            "ma_cp0_data",
            "sl_hd",
            "sl_pn",
            "so_ct_dh",
            "so_ct_ss1",
            "line_dh",
            "line_ss1",
            "active",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "created",
            "updated",
            "entity_model",
            "don_mua_hang",
            "ma_vt_data",
            "dvt_data",
            "ma_thue_data",
            "ma_bp_data",
            "ma_vv_data",
            "ma_hd_data",
            "ma_dtt_data",
            "ma_ku_data",
            "ma_phi_data",
            "ma_sp_data",
            "ma_cp0_data",
        ]

    def validate(self, data):  # noqa: C901
        """Custom validation for the entire object"""
        # Validate quantity
        so_luong = data.get("so_luong")
        if so_luong is not None and so_luong <= 0:
            raise serializers.ValidationError(_("Số lượng phải lớn hơn 0"))

        # Validate price
        gia_nt = data.get("gia_nt")
        if gia_nt is not None and gia_nt < 0:
            raise serializers.ValidationError(_("Giá không được âm"))

        # Validate tax rate
        thue_suat = data.get("thue_suat")
        if thue_suat is not None and (thue_suat < 0 or thue_suat > 100):
            raise serializers.ValidationError(
                _("Thuế suất phải nằm trong khoảng từ 0 đến 100")
            )

        # Calculate tien_nt if not provided
        if "so_luong" in data and "gia_nt" in data and "tien_nt" not in data:
            data["tien_nt"] = Decimal(str(data["so_luong"])) * Decimal(
                str(data["gia_nt"])
            )

        # Calculate thue_nt if not provided
        if "tien_nt" in data and "thue_suat" in data and "thue_nt" not in data:
            data["thue_nt"] = Decimal(str(data["tien_nt"])) * (
                Decimal(str(data["thue_suat"])) / 100
            )

        return data

    def create(self, validated_data):  # noqa: C901
        """
        Create a new ChiTietDonMuaHang instance
        The entity_model and don_mua_hang will be handled by the service layer
        """
        return super().create(validated_data)

    def update(self, instance, validated_data):  # noqa: C901
        """
        Update a ChiTietDonMuaHang instance
        """
        return super().update(instance, validated_data)

    def get_ma_vt_data(self, obj):  # noqa: C901
        """Get nested data for ma_vt field"""
        if obj.ma_vt:
            return {
                "uuid": obj.ma_vt.uuid,
                "ma_vt": obj.ma_vt.ma_vt,
                "ten_vt": obj.ma_vt.ten_vt,
                "ma_dvt": (
                    getattr(obj.ma_vt.dvt, "uuid", None) if obj.ma_vt.dvt else None
                ),
                "ma_dvt_data": (
                    {
                        "uuid": obj.ma_vt.dvt.uuid,
                        "ma_dvt": obj.ma_vt.dvt.dvt,
                        "ten_dvt": obj.ma_vt.dvt.ten_dvt,
                    }
                    if obj.ma_vt.dvt
                    else None
                ),
                "ma_kho": obj.ma_vt.ma_kho.uuid,
                "ma_kho_data": (
                    {
                        "uuid": obj.ma_vt.ma_kho.uuid,
                        "ma_kho": obj.ma_vt.ma_kho.ma_kho,
                        "ten_kho": obj.ma_vt.ma_kho.ten_kho,
                    }
                ),
                "tk_khcp": obj.ma_vt.tk_khcp.uuid,
                "tk_khcp_data": (
                    {
                        "uuid": obj.ma_vt.tk_khcp.uuid,
                        "code": obj.ma_vt.tk_khcp.code,
                        "name": obj.ma_vt.tk_khcp.name,
                    }
                ),
            }
        return None

    def get_dvt_data(self, obj):  # noqa: C901
        """Get nested data for dvt field"""
        if obj.dvt:
            return {
                "uuid": obj.dvt.uuid,
                "ma_dvt": obj.dvt.dvt,
                "ten_dvt": obj.dvt.ten_dvt,
            }
        return None

    def get_ma_thue_data(self, obj):  # noqa: C901
        """Get nested data for ma_thue field"""
        if obj.ma_thue:
            from django_ledger.api.serializers.tax import TaxModelSerializer

            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_ma_bp_data(self, obj):  # noqa: C901
        """Get nested data for ma_bp field"""
        if obj.ma_bp:
            return {
                "uuid": obj.ma_bp.uuid,
                "ma_bp": obj.ma_bp.ma_bp,
                "ten_bp": obj.ma_bp.ten_bp,
            }
        return None

    def get_ma_vv_data(self, obj):  # noqa: C901
        """Get nested data for ma_vv field"""
        if obj.ma_vv:
            return {
                "uuid": obj.ma_vv.uuid,
                "ma_vv": obj.ma_vv.ma_vu_viec,
                "ten_vv": obj.ma_vv.ten_vu_viec,
            }
        return None

    def get_ma_hd_data(self, obj):  # noqa: C901
        """Get nested data for ma_hd field"""
        if obj.ma_hd:
            return {
                "uuid": obj.ma_hd.uuid,
                "ma_hd": obj.ma_hd.ma_hd,
                "ten_hd": obj.ma_hd.ten_hd,
            }
        return None

    def get_ma_dtt_data(self, obj):  # noqa: C901
        """Get nested data for ma_dtt field"""
        if obj.ma_dtt:
            return {
                "uuid": obj.ma_dtt.uuid,
                "ma_dtt": obj.ma_dtt.ma_dtt,
                "ten_dtt": obj.ma_dtt.ten_dtt,
            }
        return None

    def get_ma_ku_data(self, obj):  # noqa: C901
        """Get nested data for ma_ku field"""
        if obj.ma_ku:
            return {
                "uuid": obj.ma_ku.uuid,
                "ma_ku": obj.ma_ku.ma_ku,
                "ten_ku": obj.ma_ku.ten_ku,
            }
        return None

    def get_ma_phi_data(self, obj):  # noqa: C901
        """Get nested data for ma_phi field"""
        if obj.ma_phi:
            return {
                "uuid": obj.ma_phi.uuid,
                "ma_phi": obj.ma_phi.ma_phi,
                "ten_phi": obj.ma_phi.ten_phi,
            }
        return None

    def get_ma_sp_data(self, obj):  # noqa: C901
        """Get nested data for ma_sp field"""
        if obj.ma_sp:
            return {
                "uuid": obj.ma_sp.uuid,
                "ma_vt": obj.ma_sp.ma_vt,
                "ten_vt": obj.ma_sp.ten_vt,
                "ma_dvt": (
                    getattr(obj.ma_sp.dvt, "uuid", None) if obj.ma_sp.dvt else None
                ),
                "ma_dvt_data": (
                    {
                        "uuid": obj.ma_sp.dvt.uuid,
                        "ma_dvt": obj.ma_sp.dvt.dvt,
                        "ten_dvt": obj.ma_sp.dvt.ten_dvt,
                    }
                    if obj.ma_sp.dvt
                    else None
                ),
            }
        return None

    def get_ma_cp0_data(self, obj):  # noqa: C901
        """Get nested data for ma_cp0 field"""
        if obj.ma_cp0:
            return {
                "uuid": obj.ma_cp0.uuid,
                "ma_cpkhl": getattr(obj.ma_cp0, "ma_cpkhl", ""),
                "ten_cpkhl": getattr(obj.ma_cp0, "ten_cpkhl", ""),
            }
        return None

    def get_don_mua_hang_data(self, obj):  # noqa: C901
        if obj.don_mua_hang:
            so_ct = None
            if hasattr(obj.don_mua_hang, 'so_ct'):
                so_ct = obj.don_mua_hang.so_ct
            elif (
                hasattr(obj.don_mua_hang, 'chung_tu_item')
                and obj.don_mua_hang.chung_tu_item
            ):
                so_ct = getattr(obj.don_mua_hang.chung_tu_item, 'so_ct', None)

            return {'uuid': obj.don_mua_hang.uuid, 'so_ct': so_ct}
        return None
