"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietDoiTuongHachToanTSCD (Fixed Asset Accounting Object Detail) model
"""

from rest_framework import serializers

from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer  # noqa: E402
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import (  # noqa: E402
    KheUocModelSerializer,
)
from django_ledger.api.serializers.organization import (  # noqa: E402
    BoPhanModelSerializer,
)
from django_ledger.api.serializers.vat_tu import VatTuSerializer  # noqa: E402
from django_ledger.api.serializers.vu_viec import (  # noqa: E402
    VuViecModelSerializer,
)
from django_ledger.models import ChiTietDoiTuongHachToanTSCDModel  # noqa: E402


class ChiTietDoiTuongHachToanTSCDSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietDoiTuongHachToanTSCDModel
    """

    ma_bp_data = serializers.SerializerMethodField()
    ma_vv_data = serializers.SerializerMethodField()
    ma_hd_data = serializers.SerializerMethodField()
    ma_ku_data = serializers.SerializerMethodField()
    ma_phi_data = serializers.SerializerMethodField()
    ma_sp_data = serializers.SerializerMethodField()
    tk_kh_data = serializers.SerializerMethodField()
    tk_cp_data = serializers.SerializerMethodField()

    class Meta:
        model = ChiTietDoiTuongHachToanTSCDModel
        fields = [
            'uuid',
            'khai_bao_thong_tin_tai_san_co_dinh',
            'line',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_lsx',
            'tk_kh',
            'tk_kh_data',
            'tk_cp',
            'tk_cp_data',
            'he_so',
            'ngay_hl1',
            'ngay_hl2',
            'created',
            'updated',
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'khai_bao_thong_tin_tai_san_co_dinh']

    def get_ma_bp_data(self, instance):  # noqa: C901
        """
        Get department data
        """
        if instance.ma_bp:
            return BoPhanModelSerializer(instance.ma_bp).data
        return None

    def get_ma_vv_data(self, instance):  # noqa: C901
        """
        Get case data
        """
        if instance.ma_vv:
            return VuViecModelSerializer(instance.ma_vv).data
        return None

    def get_ma_hd_data(self, instance):  # noqa: C901
        """
        Get contract data
        """
        if instance.ma_hd:
            return ContractModelSerializer(instance.ma_hd).data
        return None

    def get_ma_ku_data(self, instance):  # noqa: C901
        """
        Get agreement data
        """
        if instance.ma_ku:
            return KheUocModelSerializer(instance.ma_ku).data
        return None

    def get_ma_phi_data(self, instance):  # noqa: C901
        """
        Get fee data
        """
        if instance.ma_phi:
            return PhiSerializer(instance.ma_phi).data
        return None

    def get_ma_sp_data(self, instance):  # noqa: C901
        """
        Get product data
        """
        if instance.ma_sp:
            return VatTuSerializer(instance.ma_sp).data
        return None

    def get_tk_kh_data(self, instance):  # noqa: C901
        """
        Get depreciation account data
        """
        if instance.tk_kh:
            return AccountModelSerializer(instance.tk_kh).data
        return None

    def get_tk_cp_data(self, instance):  # noqa: C901
        """
        Get expense account data
        """
        if instance.tk_cp:
            return AccountModelSerializer(instance.tk_cp).data
        return None
