"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietDonHang model.
"""

from rest_framework import serializers

from django_ledger.api.serializers.danh_muc import PhiSerializer  # noqa: F401,
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import (  # noqa: F401,
    KheUocModelSerializer as KheUocSerializer,
)
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer  # noqa: F401,
from django_ledger.api.serializers.organization import (  # noqa: F401,
    BoPhanModelSerializer as BoPhanSerializer,
)
from django_ledger.api.serializers.tax import TaxModelSerializer
from django_ledger.api.serializers.tien_do_thanh_toan import (  # noqa: F401,
    DotThanhToanModelSerializer as DotThanhToanSerializer,
)
from django_ledger.api.serializers.vat_tu import VatTuSerializer  # noqa: F401,
from django_ledger.api.serializers.vu_viec import (  # noqa: F401,
    VuViecModelSerializer as VuViecSerializer,
)
from django_ledger.api.serializers.warehouse import (  # noqa: F401,
    KhoHangModelSerializer as KhoSerializer,
)
from django_ledger.models import ChiTietDonHangModel  # noqa: F401,


class ChiTietDonHangSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietDonHang model.
    """

    # Related data fields (read-only)
    don_hang_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    ma_kho_data = serializers.SerializerMethodField(read_only=True)
    ma_lo_data = serializers.SerializerMethodField(read_only=True)
    ma_vi_tri_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietDonHangModel
        fields = [
            'uuid',
            'don_hang',
            'don_hang_data',
            'line',
            'ma_vt',
            'ma_vt_data',
            'dvt',
            'dvt_data',
            'ten_dvt',
            'ma_kho',
            'ma_kho_data',
            'ten_kho',
            'ma_lo',
            'ma_lo_data',
            'ten_lo',
            'lo_yn',
            'ma_vi_tri',
            'ma_vi_tri_data',
            'ten_vi_tri',
            'vi_tri_yn',
            'he_so',
            'qc_yn',
            'so_luong',
            'ct_km',
            'ma_loai_gb',
            'gia_nt1',
            'gia_nt2',
            'tien_nt2',
            'ngay_giao',
            'tl_ck',
            'ck_nt',
            'ma_thue',
            'ma_thue_data',
            'thue_suat',
            'thue_nt',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_lsx',
            'ma_cp0',
            'ma_cp0_data',
            'gia1',
            'gia2',
            'tien2',
            'ck',
            'thue',
            'sl_don_hang',
            'sl_hd',
            'sl_px',
            'id_hd',
            'line_hd',
            'id_bg',
            'line_bg',
            'created',
            'updated',
            'sl_cl',
        ]
        read_only_fields = [
            'uuid',
            'don_hang_data',
            'ma_vt_data',
            'dvt_data',
            'ma_kho_data',
            'ma_lo_data',
            'ma_vi_tri_data',
            'ma_thue_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_dtt_data',
            'ma_ku_data',
            'ma_phi_data',
            'ma_sp_data',
            'ma_cp0_data',
            'created',
            'updated',
        ]

    def get_don_hang_data(self, obj):  # noqa: C901
        """
        Get parent DonHang data (basic info only to avoid circular reference).
        """
        if obj.don_hang:
            return {
                'uuid': str(obj.don_hang.uuid),
                'so_ct': obj.don_hang.so_ct,
                'ngay_ct': obj.don_hang.ngay_ct,
                'ong_ba': obj.don_hang.ong_ba,
            }
        return None

    def get_ma_vt_data(self, obj):  # noqa: C901
        """
        Get material data.
        """
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_dvt_data(self, obj):  # noqa: C901
        """
        Get unit of measure data.
        """
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_kho_data(self, obj):  # noqa: C901
        """
        Get warehouse data.
        """
        if obj.ma_kho:
            return KhoSerializer(obj.ma_kho).data
        return None

    def get_ma_lo_data(self, obj):  # noqa: C901
        """
        Get batch data.
        """
        if obj.ma_lo:
            return {
                'uuid': str(obj.ma_lo.uuid),
                'ma_lo': obj.ma_lo.ma_lo,
                'ten_lo': getattr(obj.ma_lo, 'ten_lo', ''),
            }
        return None

    def get_ma_vi_tri_data(self, obj):  # noqa: C901
        """
        Get position data.
        """
        if obj.ma_vi_tri:
            return {
                'uuid': str(obj.ma_vi_tri.uuid),
                'ma_vi_tri': obj.ma_vi_tri.ma_vi_tri,
                'ten_vi_tri': getattr(obj.ma_vi_tri, 'ten_vi_tri', ''),
            }
        return None

    def get_ma_thue_data(self, obj):  # noqa: C901
        """
        Get tax data.
        """
        if obj.ma_thue:
            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_ma_bp_data(self, obj):  # noqa: C901
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):  # noqa: C901
        """
        Get case data.
        """
        if obj.ma_vv:
            return VuViecSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):  # noqa: C901
        """
        Get contract data.
        """
        if obj.ma_hd:
            return {
                'uuid': str(obj.ma_hd.uuid),
                'ma_hd': obj.ma_hd.ma_hd,
                'ten_hd': getattr(obj.ma_hd, 'ten_hd', ''),
            }
        return None

    def get_ma_dtt_data(self, obj):  # noqa: C901
        """
        Get payment term data.
        """
        if obj.ma_dtt:
            return DotThanhToanSerializer(obj.ma_dtt).data
        return None

    def get_ma_ku_data(self, obj):  # noqa: C901
        """
        Get area data.
        """
        if obj.ma_ku:
            return KheUocSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):  # noqa: C901
        """
        Get fee data.
        """
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_sp_data(self, obj):  # noqa: C901
        """
        Get product data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):  # noqa: C901
        """
        Get cost data.
        """
        if obj.ma_cp0:
            return {
                'uuid': str(obj.ma_cp0.uuid),
                'ma_cp0': obj.ma_cp0.ma_cpkhl,
                'ten_cp0': getattr(obj.ma_cp0, 'ten_cpkhl', ''),
            }
        return None
