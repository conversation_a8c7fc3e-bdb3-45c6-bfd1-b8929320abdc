"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/GeneralTab.tsx":
/*!****************************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/GeneralTab.tsx ***!
  \****************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/.pnpm/react-hook-form@7.56.1_react@18.3.1/node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/custom/arito/form/custom-search-field */ \"(app-pages-browser)/./src/components/custom/arito/form/custom-search-field/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/arito/form/form-field */ \"(app-pages-browser)/./src/components/custom/arito/form/form-field/index.tsx\");\n/* harmony import */ var _constants_search_columns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants/search-columns */ \"(app-pages-browser)/./src/constants/search-columns/index.tsx\");\n/* harmony import */ var _constants_query_keys__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/query-keys */ \"(app-pages-browser)/./src/constants/query-keys.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst GeneralTab = (param)=>{\n    let { mode, donVi, setDonVi } = param;\n    _s();\n    const isViewMode = mode === \"view\";\n    const { setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useFormContext)();\n    const handleDonViSelect = (selectedDonVi)=>{\n        setDonVi(selectedDonVi);\n        setValue(\"unit_id\", selectedDonVi.uuid);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-h-[calc(100vh-250px)] overflow-y-auto p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            className: \"min-w-40\",\n                            children: \"Số chứng từ mẫu\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                            name: \"so_ct_mau\",\n                            type: \"text\",\n                            disabled: isViewMode\n                        }, void 0, false, {\n                            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            className: \"min-w-40\",\n                            children: \"Đơn vị\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                            displayRelatedField: \"ten_unit\",\n                            columnDisplay: \"ma_unit\",\n                            searchEndpoint: \"/\".concat(_constants_query_keys__WEBPACK_IMPORTED_MODULE_5__[\"default\"].DON_VI),\n                            searchColumns: _constants_search_columns__WEBPACK_IMPORTED_MODULE_4__.donViSearchColumns,\n                            dialogTitle: \"Danh mục đơn vị\",\n                            value: (donVi === null || donVi === void 0 ? void 0 : donVi.ma_unit) || \"\",\n                            onRowSelection: handleDonViSelect,\n                            disabled: isViewMode\n                        }, void 0, false, {\n                            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            className: \"min-w-40\",\n                            children: \"Kiểm tra tr\\xf9ng số\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                            name: \"kieu_trung_so\",\n                            type: \"select\",\n                            disabled: isViewMode,\n                            options: [\n                                {\n                                    label: \"0. Kh\\xf4ng kiểm tra\",\n                                    value: \"0\"\n                                },\n                                {\n                                    label: \"1. Theo ng\\xe0y\",\n                                    value: \"1\"\n                                },\n                                {\n                                    label: \"2. Theo th\\xe1ng\",\n                                    value: \"2\"\n                                },\n                                {\n                                    label: \"3. Theo qu\\xfd\",\n                                    value: \"3\"\n                                },\n                                {\n                                    label: \"4. Theo năm\",\n                                    value: \"4\"\n                                }\n                            ]\n                        }, void 0, false, {\n                            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            className: \"min-w-40\",\n                            children: \"Số hiện tại\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                            name: \"i_so_ct_ht\",\n                            type: \"number\",\n                            disabled: isViewMode\n                        }, void 0, false, {\n                            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            className: \"min-w-40\",\n                            children: \"Hiệu lực từ/đến\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    name: \"ngay_hl1\",\n                                    type: \"date\",\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"/\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    name: \"ngay_hl2\",\n                                    type: \"date\",\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            className: \"min-w-40\",\n                            children: \"K\\xfd hiệu\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                            name: \"so_ct2\",\n                            type: \"text\",\n                            disabled: isViewMode\n                        }, void 0, false, {\n                            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            htmlFor: \"status\",\n                            className: \"min-w-40\",\n                            children: \"Trạng th\\xe1i\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                            id: \"status\",\n                            type: \"select\",\n                            name: \"status\",\n                            disabled: isViewMode,\n                            options: [\n                                {\n                                    label: \"1. C\\xf2n sử dụng\",\n                                    value: \"1\"\n                                },\n                                {\n                                    label: \"0. Kh\\xf4ng sử dụng\",\n                                    value: \"0\"\n                                }\n                            ]\n                        }, void 0, false, {\n                            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\GeneralTab.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GeneralTab, \"HxEjxW/HjRepjG3LcW3pTxr0hq8=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useFormContext\n    ];\n});\n_c = GeneralTab;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GeneralTab);\nvar _c;\n$RefreshReg$(_c, \"GeneralTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/GeneralTab.tsx\n"));

/***/ })

});