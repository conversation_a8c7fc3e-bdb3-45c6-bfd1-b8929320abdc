"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Hoa Don Ban Hang (Sales Invoice) serializer implementation.
"""

from django.utils.translation import gettext_lazy as _
from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers._utils.chung_tu_fields import ChungTuSerializerMixin
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang.chi_tiet_hoa_don_ban_hang import (  # noqa: F401
    ChiTietHoaDonBanHangSerializer,
)
from django_ledger.api.serializers.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang.thong_tin_thanh_toan_hoa_don_ban_hang import (  # noqa: F401,
    ThongTinThanhToanHoaDonBanHangSerializer,
)
from django_ledger.api.serializers.bank_account import BankAccountModelSerializer

# Import serializers for foreign key fields
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer
from django_ledger.api.serializers.dia_chi import DiaChiSerializer
from django_ledger.api.serializers.nhan_vien import NhanVienModelSerializer
from django_ledger.api.serializers.phuong_tien_giao_hang import (
    PhuongTienGiaoHangModelSerializer,
)
from django_ledger.api.serializers.phuong_tien_van_chuyen import (
    PhuongTienVanChuyenModelSerializer,
)
from django_ledger.api.serializers.unit import EntityUnitModelSimpleSerializer
from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import (  # noqa: F401,
    HoaDonBanHangModel,
)
from django_ledger.services.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import (  # noqa: F401,
    HoaDonBanHangService,
)


class HoaDonBanHangSerializer(serializers.ModelSerializer):
    """
    Serializer for HoaDonBanHangModel.
    """

    # Explicit field definitions for ChungTu fields (properties)
    i_so_ct = serializers.IntegerField(required=False, allow_null=True)
    ma_nk = serializers.UUIDField(required=False, allow_null=True)
    so_ct = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    ngay_ct = serializers.DateField(required=False, allow_null=True)
    ngay_lct = serializers.DateField(required=False, allow_null=True)

    ma_kh_data = serializers.SerializerMethodField()
    ma_nvbh_data = serializers.SerializerMethodField()
    tk_data = serializers.SerializerMethodField()
    ma_tt_data = serializers.SerializerMethodField()
    ma_nk_data = serializers.SerializerMethodField()

    ma_nt_data = serializers.SerializerMethodField()
    ma_dc_data = serializers.SerializerMethodField()
    ma_ptvc_data = serializers.SerializerMethodField()
    ma_ptgh_data = serializers.SerializerMethodField()
    tknh_data = serializers.SerializerMethodField()
    ma_kh9_data = serializers.SerializerMethodField()
    unit_id_data = serializers.SerializerMethodField()

    # Use SerializerMethodField to properly handle SafeDelete filtering
    chi_tiet = serializers.SerializerMethodField()
    thong_tin_thanh_toan = serializers.SerializerMethodField()

    # Related document UUID arrays for sales invoices
    phieu_thu_uuids = serializers.ListField(
        child=serializers.CharField(), read_only=True, default=list
    )
    giay_bao_co_uuids = serializers.ListField(
        child=serializers.CharField(), read_only=True, default=list
    )

    # Related PhieuXuatKho UUID for sales invoices
    phieu_xuat_kho_uuid = serializers.CharField(
        read_only=True, allow_blank=True, default=''
    )

    ck_yn = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = HoaDonBanHangModel
        fields = "__all__"
        read_only_fields = ["uuid", "created", "updated", "entity_model"]

    def get_ma_kh_data(self, obj):  # noqa: C901
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_ma_nvbh_data(self, obj):  # noqa: C901
        if obj.ma_nvbh:
            return NhanVienModelSerializer(obj.ma_nvbh).data
        return None

    def get_tk_data(self, obj):  # noqa: C901
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    def get_ma_tt_data(self, obj):  # noqa: C901
        if obj.ma_tt:
            return {
                "uuid": str(obj.ma_tt.uuid),
                "ma_tt": obj.ma_tt.ma_tt,
                "ten_tt": obj.ma_tt.ten_tt,
            }
        return None

    def get_ma_nk_data(self, obj):  # noqa: C901
        if obj.ma_nk:
            return {
                "uuid": str(obj.ma_nk.uuid),
                "ma_nk": obj.ma_nk.ma_nk,
                "ten_nk": obj.ma_nk.ten_nk,
            }
        return None

    def get_so_px_data(self, obj):  # noqa: C901
        if obj.so_px:
            return ChungTuSerializer(obj.so_px).data
        return None

    def get_ma_nt_data(self, obj):  # noqa: C901
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_ma_dc_data(self, obj):  # noqa: C901
        if obj.ma_dc:
            return DiaChiSerializer(obj.ma_dc).data
        return None

    def get_ma_ptvc_data(self, obj):  # noqa: C901
        if obj.ma_ptvc:
            return PhuongTienVanChuyenModelSerializer(obj.ma_ptvc).data
        return None

    def get_ma_ptgh_data(self, obj):  # noqa: C901
        if obj.ma_ptgh:
            return PhuongTienGiaoHangModelSerializer(obj.ma_ptgh).data
        return None

    def get_tknh_data(self, obj):  # noqa: C901
        if obj.tknh:
            return BankAccountModelSerializer(obj.tknh).data
        return None

    def get_ma_kh9_data(self, obj):  # noqa: C901
        if obj.ma_kh9:
            return CustomerModelSerializer(obj.ma_kh9).data
        return None

    def get_unit_id_data(self, obj):  # noqa: C901
        if obj.unit_id:
            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None

    def get_chi_tiet(self, obj):  # noqa: C901
        """Get detail items (excluding soft deleted)."""
        # Explicitly filter soft deleted items to ensure they are excluded
        chi_tiet_qs = obj.chi_tiet.filter(deleted__isnull=True)
        return ChiTietHoaDonBanHangSerializer(chi_tiet_qs, many=True).data

    def get_thong_tin_thanh_toan(self, obj):  # noqa: C901
        """Get payment information (excluding soft deleted)."""
        # Explicitly filter soft deleted items to ensure they are excluded
        thanh_toan_qs = obj.thong_tin_thanh_toan.filter(deleted__isnull=True)
        return ThongTinThanhToanHoaDonBanHangSerializer(thanh_toan_qs, many=True).data

    def get_ck_yn(self, obj):
        """Get discount flag."""
        if (obj.loai_ck not in [1,2,3]):
            return False
        return True


class HoaDonBanHangCreateUpdateSerializer(
    ChungTuSerializerMixin, serializers.ModelSerializer
):
    """
    Serializer for creating and updating HoaDonBanHangModel.
    Uses ChungTuSerializerMixin to handle ChungTu fields properly.
    Simple validation: just check if chi_tiet field exists.
    """

    chi_tiet = serializers.ListField(required=True, write_only=True)

    class Meta:
        model = HoaDonBanHangModel
        fields = "__all__"
        read_only_fields = ["uuid", "created", "updated", "entity_model"]

    def validate_chi_tiet(self, value):
        """
        Validate chi_tiet field:
        1. Must be a list
        2. Each item must have a valid ma_vt (not empty/null)
        """
        if not isinstance(value, list):
            raise serializers.ValidationError(_('Chi tiết phải là danh sách.'))

        # Allow empty chi_tiet list
        if not value:
            return value

        # Check each item in chi_tiet
        for i, item in enumerate(value):
            if not isinstance(item, dict):
                raise serializers.ValidationError(
                    _('Chi tiết thứ {} phải là object.').format(i + 1)
                )

            # ma_vt is required and must not be empty
            ma_vt = item.get('ma_vt')
            if not ma_vt or (isinstance(ma_vt, str) and ma_vt.strip() == ''):
                raise serializers.ValidationError(
                    _('Thiếu validation: Trường "Mã sản phẩm" không được bỏ trống')
                )

        return value

    def to_internal_value(self, data):
        """
        Override to handle chi_tiet input for CREATE/UPDATE operations and allow empty string for date fields.
        """
        # Handle empty strings for optional date fields
        if isinstance(data, dict):
            date_fields = ['ngay_ct_hddt', 'ngay_px', 'ngay_bk']
            for field in date_fields:
                if data.get(field, None) == '':
                    data[field] = None

        # Handle both chi_tiet and chi_tiet_items field names
        # Don't pop chi_tiet yet - let DRF validate it first
        chi_tiet_data = []
        if isinstance(data, dict):
            # Get chi_tiet data but don't remove it from data yet
            chi_tiet_data = data.get('chi_tiet', []) or data.get('chi_tiet_items', [])

            # Ensure chi_tiet is in data for DRF validation
            if 'chi_tiet' not in data and 'chi_tiet_items' in data:
                data['chi_tiet'] = data.pop('chi_tiet_items')

        validated_data = super().to_internal_value(data)

        # Override the chi_tiet in validated_data with our extracted data
        validated_data['chi_tiet'] = chi_tiet_data

        return validated_data
