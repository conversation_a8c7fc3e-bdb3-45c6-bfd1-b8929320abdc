"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Repository for PhieuChiThue model.
"""

from typing import Any, Dict, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import QuerySet  # noqa: F401

from django_ledger.models import PhieuChiModel, PhieuChiThueModel  # noqa: F401,
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class PhieuChiThueRepository(BaseRepository):
    """
    Repository class for handling PhieuChiThueModel database operations.
    Implements the Repository pattern for PhieuChiThueModel.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the repository with the PhieuChiThueModel.
        """
        super().__init__(model_class=PhieuChiThueModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for PhieuChiThueModel.

        Returns
        -------
        QuerySet
            The base queryset for PhieuChiThueModel.
        """
        return self.model_class.objects.all().select_related(
            'phieu_chi',
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_cp0',
        )

    def get_by_uuid(self, uuid: Union[str, UUID]) -> PhieuChiThueModel:  # noqa: C901
        """
        Gets a PhieuChiThueModel instance by UUID.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the PhieuChiThueModel.

        Returns
        -------
        PhieuChiThueModel
            The PhieuChiThueModel instance.

        Raises
        ------
        PhieuChiThueModel.DoesNotExist
            If the instance does not exist.
        """
        return self.get_queryset().get(uuid=uuid)

    def get_by_phieu_chi(
        self, phieu_chi_uuid: Union[str, UUID]
    ) -> QuerySet:  # noqa: C901
        """
        Gets PhieuChiThueModel instances for a specific payment voucher.

        Parameters
        ----------
        phieu_chi_uuid : Union[str, UUID]
            The PhieuChiModel UUID.

        Returns
        -------
        QuerySet
            QuerySet of PhieuChiThueModel instances for the payment voucher.
        """
        return self.get_queryset().filter(phieu_chi__uuid=phieu_chi_uuid)

    def create(
        self, parent_field: PhieuChiModel, data: Dict[str, Any]
    ) -> PhieuChiThueModel:  # noqa: C901
        """
        Creates a new PhieuChiThueModel instance.

        Parameters
        ----------
        parent_field : PhieuChiModel
            The parent PhieuChiModel instance.
        data : Dict[str, Any]
            The data for the new PhieuChiThueModel.

        Returns
        -------
        PhieuChiThueModel
            The created PhieuChiThueModel instance.
        """
        # Convert UUIDs to model instances
        processed_data = self.convert_uuids_to_model_instances(data.copy())
        # Create the instance
        instance = self.model_class(phieu_chi=parent_field, **processed_data)
        instance.save()

        return instance

    def update(
        self, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> PhieuChiThueModel:  # noqa: C901
        """
        Updates an existing PhieuChiThueModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the PhieuChiThueModel to update.
        data : Dict[str, Any]
            The data to update.

        Returns
        -------
        PhieuChiThueModel
            The updated PhieuChiThueModel instance.

        Raises
        ------
        PhieuChiThueModel.DoesNotExist
            If the instance does not exist.
        """
        # Get the instance
        instance = self.get_by_uuid(uuid)
        # Update the instance
        for key, value in data.items():
            setattr(instance, key, value)

        instance.save()

        return instance

    def delete(self, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a PhieuChiThueModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the PhieuChiThueModel to delete.

        Returns
        -------
        bool
            True if the instance was deleted, False otherwise.

        Raises
        ------
        PhieuChiThueModel.DoesNotExist
            If the instance does not exist.
        """
        # Get the instance
        instance = self.get_by_uuid(uuid)
        # Delete the instance
        instance.delete()
        return True

    def bulk_create(
        self, parent_field: PhieuChiModel, data_list: list
    ) -> list:  # noqa: C901
        """
        Creates multiple PhieuChiThueModel instances.

        Parameters
        ----------
        parent_field : PhieuChiModel
            The parent PhieuChiModel instance.
        data_list : list
            List of data dictionaries for the new instances.

        Returns
        -------
        list
            List of created PhieuChiThueModel instances.
        """
        instances = []
        for data in data_list:
            # Convert UUIDs to model instances
            processed_data = self.convert_uuids_to_model_instances(data.copy())
            instance = self.model_class(phieu_chi=parent_field, **processed_data)
            instances.append(instance)

        # Bulk create the instances
        created_instances = self.model_class.objects.bulk_create(instances)
        return created_instances

    def delete_by_phieu_chi(
        self, phieu_chi_uuid: Union[str, UUID]
    ) -> int:  # noqa: C901
        """
        Deletes all PhieuChiThueModel instances for a specific payment voucher.

        Parameters
        ----------
        phieu_chi_uuid : Union[str, UUID]
            The PhieuChiModel UUID.

        Returns
        -------
        int
            Number of deleted instances.
        """
        # Use safe_bulk_delete to avoid "Cannot call delete() after .distinct()" error
        return self.safe_bulk_delete(phieu_chi__uuid=phieu_chi_uuid)

    def get_by_tax_code(self, tax_code: str) -> QuerySet:  # noqa: C901
        """
        Gets PhieuChiThueModel instances for a specific tax code.

        Parameters
        ----------
        tax_code : str
            The tax code.

        Returns
        -------
        QuerySet
            QuerySet of PhieuChiThueModel instances for the tax code.
        """
        return self.get_queryset().filter(ma_thue=tax_code)

    def get_by_customer(self, customer_code: str) -> QuerySet:  # noqa: C901
        """
        Gets PhieuChiThueModel instances for a specific customer.

        Parameters
        ----------
        customer_code : str
            The customer code.

        Returns
        -------
        QuerySet
            QuerySet of PhieuChiThueModel instances for the customer.
        """
        return self.get_queryset().filter(ma_kh=customer_code)

    def get_by_department(
        self, department_uuid: Union[str, UUID]
    ) -> QuerySet:  # noqa: C901
        """
        Gets PhieuChiThueModel instances for a specific department.

        Parameters
        ----------
        department_uuid : Union[str, UUID]
            The department UUID.

        Returns
        -------
        QuerySet
            QuerySet of PhieuChiThueModel instances for the department.
        """
        return self.get_queryset().filter(ma_bp__uuid=department_uuid)

    def get_by_task(self, task_uuid: Union[str, UUID]) -> QuerySet:  # noqa: C901
        """
        Gets PhieuChiThueModel instances for a specific task.

        Parameters
        ----------
        task_uuid : Union[str, UUID]
            The task UUID.

        Returns
        -------
        QuerySet
            QuerySet of PhieuChiThueModel instances for the task.
        """
        return self.get_queryset().filter(ma_vv__uuid=task_uuid)

    def get_by_contract(
        self, contract_uuid: Union[str, UUID]
    ) -> QuerySet:  # noqa: C901
        """
        Gets PhieuChiThueModel instances for a specific contract.

        Parameters
        ----------
        contract_uuid : Union[str, UUID]
            The contract UUID.

        Returns
        -------
        QuerySet
            QuerySet of PhieuChiThueModel instances for the contract.
        """
        return self.get_queryset().filter(ma_hd__uuid=contract_uuid)

    def convert_uuids_to_model_instances(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert UUID strings to model instances for foreign key fields.

        Parameters
        ----------
        data : Dict[str, Any]
            The data dictionary containing UUID strings.

        Returns
        -------
        Dict[str, Any]
            The data dictionary with model instances.
        """
        from django_ledger.models import (
            AccountModel,
            BoPhanModel,
            ChiPhiKhongHopLeModel,
            ContractModel,
            CustomerModel,
            DotThanhToanModel,
            KheUocModel,
            PhiModel,
            TinhChatThueModel,
            VatTuModel,
            VuViecModel,
        )

        data_copy = data.copy()

        # Mapping of field names to their corresponding model classes
        field_model_mapping = {
            'ma_kh': CustomerModel,
            'ma_bp': BoPhanModel,
            'ma_vv': VuViecModel,
            'ma_hd': ContractModel,
            'ma_dtt': DotThanhToanModel,
            'ma_ku': KheUocModel,
            'ma_phi': PhiModel,
            'ma_sp': VatTuModel,
            'ma_cp0': ChiPhiKhongHopLeModel,
            'ma_tc_thue': TinhChatThueModel,
            'tk_thue_no': AccountModel,
            'tk_du': AccountModel,
        }

        # Convert UUID strings to model instances for all foreign key fields
        for field_name, model_class in field_model_mapping.items():
            if (
                field_name in data_copy
                and isinstance(data_copy[field_name], str)
                and data_copy[field_name]
            ):
                try:
                    data_copy[field_name] = model_class.objects.get(
                        uuid=data_copy[field_name]
                    )
                except model_class.DoesNotExist:
                    # Set to None for invalid UUIDs to avoid errors
                    data_copy[field_name] = None

        return data_copy
