"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietPhieuNhapDieuChinhGiaHangMua model.
"""

from django.utils.translation import gettext_lazy as _  # noqa: F401
from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers.accounts import AccountModelSerializer  # noqa: F401
from django_ledger.api.serializers.contract import (  # noqa: F401,
    ContractModelSerializer,
)
from django_ledger.api.serializers.danh_muc import (  # noqa: F401,
    ChiPhiKhongHopLeSerializer,
    PhiSerializer,
)
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import (  # noqa: F401,
    KheUocModelSerializer,
)
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer  # noqa: F401,
from django_ledger.api.serializers.entity import EntityModelSerializer  # noqa: F401,
from django_ledger.api.serializers.lo import LoModelSerializer  # noqa: F401,
from django_ledger.api.serializers.organization import (  # noqa: F401,
    BoPhanModelSerializer,
)
from django_ledger.api.serializers.tax import TaxModelSerializer  # noqa: F401,
from django_ledger.api.serializers.tien_do_thanh_toan import (  # noqa: F401,
    DotThanhToanModelSerializer,
)
from django_ledger.api.serializers.vat_tu import VatTuSerializer  # noqa: F401,
from django_ledger.api.serializers.vi_tri_kho_hang import (  # noqa: F401,
    ViTriKhoHangModelSerializer as ViTriModelSerializer,
)
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer  # noqa: F401,
from django_ledger.api.serializers.warehouse import (  # noqa: F401,
    KhoHangModelSerializer,
)
from django_ledger.models import ChiTietPhieuNhapDieuChinhGiaHangMuaModel  # noqa: F401,
from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_tiet_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401
    ChiTietHoaDonMuaHangTrongNuocModel,
)
from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401
    HoaDonMuaHangTrongNuocModel,
)


class ChiTietPhieuNhapDieuChinhGiaHangMuaSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietPhieuNhapDieuChinhGiaHangMua model.
    """

    # Read-only fields for related objects
    phieu_nhap_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    ma_kho_data = serializers.SerializerMethodField(read_only=True)
    tk_vt_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_lo_data = serializers.SerializerMethodField(read_only=True)
    ma_vi_tri_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)

    # Custom object responses
    id_hd = serializers.SerializerMethodField(read_only=True)
    line_hd = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietPhieuNhapDieuChinhGiaHangMuaModel
        fields = [
            'uuid',
            'phieu_nhap',
            'phieu_nhap_data',
            'id_hd',
            'line_hd',
            'ma_vt',
            'ma_vt_data',
            'dvt',
            'dvt_data',
            'ma_kho',
            'ma_kho_data',
            'ma_lo',
            'ma_lo_data',
            'ma_vi_tri',
            'ma_vi_tri_data',
            'ma_thue',
            'ma_thue_data',
            'tk_vt',
            'tk_vt_data',
            'tk_thue',
            'tk_thue_data',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_cp0',
            'ma_cp0_data',
            'entity_model',
            'line',
            'lo_yn',
            'vi_tri_yn',
            'qc_yn',
            'id_hd4',
            'id_hd5',
            'id_hd7',
            'line_hd',
            'he_so',
            'so_luong',
            'gia_nt',
            'tien_nt',
            'thue_suat',
            'thue_nt',
            'thue',
            'gia',
            'tien',
            'ten_tk_vt',
            'ten_tk_thue',
            'ma_lsx',
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'phieu_nhap_data',
            'id_hd',
            'line_hd',
            'ma_vt_data',
            'dvt_data',
            'ma_kho_data',
            'tk_vt_data',
            'tk_thue_data',
            'ma_bp_data',
            'ma_lo_data',
            'ma_vi_tri_data',
            'ma_thue_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_dtt_data',
            'ma_ku_data',
            'ma_phi_data',
            'ma_sp_data',
            'ma_cp0_data',
            'created',
            'updated',
        ]

    def get_phieu_nhap_data(self, obj):  # noqa: C901
        """
        Get parent phieu nhap data (minimal to avoid circular reference).
        """
        if obj.phieu_nhap:
            return {
                'uuid': str(obj.phieu_nhap.uuid),
                'i_so_ct': obj.phieu_nhap.i_so_ct,
                'ngay_ct': obj.phieu_nhap.ngay_ct,
                'dien_giai': obj.phieu_nhap.dien_giai,
            }
        return None

    def get_ma_vt_data(self, obj):  # noqa: C901
        """
        Get material data.
        """
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_dvt_data(self, obj):  # noqa: C901
        """
        Get unit of measure data.
        """
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_kho_data(self, obj):  # noqa: C901
        """
        Get warehouse data.
        """
        if obj.ma_kho:
            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def get_tk_vt_data(self, obj):  # noqa: C901
        """
        Get material account data.
        """
        if obj.tk_vt:
            return AccountModelSerializer(obj.tk_vt).data
        return None

    def get_tk_thue_data(self, obj):  # noqa: C901
        """
        Get tax account data.
        """
        if obj.tk_thue:
            return AccountModelSerializer(obj.tk_thue).data
        return None

    def get_ma_bp_data(self, obj):  # noqa: C901
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_lo_data(self, obj):  # noqa: C901
        """
        Get lot data.
        """
        if obj.ma_lo:
            return LoModelSerializer(obj.ma_lo).data
        return None

    def get_ma_vi_tri_data(self, obj):  # noqa: C901
        """
        Get location data.
        """
        if obj.ma_vi_tri:
            return ViTriModelSerializer(obj.ma_vi_tri).data
        return None

    def get_ma_thue_data(self, obj):  # noqa: C901
        """
        Get tax data.
        """
        if obj.ma_thue:
            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_ma_vv_data(self, obj):  # noqa: C901
        """
        Get case data.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):  # noqa: C901
        """
        Get contract data.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_dtt_data(self, obj):  # noqa: C901
        """
        Get payment batch data.
        """
        if obj.ma_dtt:
            return DotThanhToanModelSerializer(obj.ma_dtt).data
        return None

    def get_ma_ku_data(self, obj):  # noqa: C901
        """
        Get loan agreement data.
        """
        if obj.ma_ku:
            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):  # noqa: C901
        """
        Get fee data.
        """
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_sp_data(self, obj):  # noqa: C901
        """
        Get product data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):  # noqa: C901
        """
        Get invalid expense data.
        """
        if obj.ma_cp0:
            return ChiPhiKhongHopLeSerializer(obj.ma_cp0).data
        return None

    def get_id_hd(self, obj):  # noqa: C901
        """
        Return object for invoice id: { uuid, so_ct }
        """
        if not getattr(obj, 'id_hd', None):
            return None
        try:
            hd = HoaDonMuaHangTrongNuocModel.objects.select_related(
                'chung_tu_item'
            ).get(uuid=obj.id_hd)
            so_ct = None
            if hasattr(hd, 'so_ct'):
                so_ct = hd.so_ct
            elif hasattr(hd, 'chung_tu_item') and hd.chung_tu_item:
                so_ct = getattr(hd.chung_tu_item, 'so_ct', None)
            return {"uuid": str(hd.uuid), "so_ct": so_ct}
        except HoaDonMuaHangTrongNuocModel.DoesNotExist:
            return None

    def get_line_hd(self, obj):  # noqa: C901
        """
        Return object for invoice line: { uuid, line }
        """
        if not getattr(obj, 'line_hd', None):
            return None
        try:
            line = ChiTietHoaDonMuaHangTrongNuocModel.objects.only('uuid', 'line').get(
                uuid=obj.line_hd
            )
            return {"uuid": str(line.uuid), "line": line.line, "sl_cl": line.sl_cl}
        except ChiTietHoaDonMuaHangTrongNuocModel.DoesNotExist:
            return None


class ChiTietPhieuNhapDieuChinhGiaHangMuaCreateUpdateSerializer(
    serializers.ModelSerializer
):
    """
    Write serializer to allow setting id_hd and line_hd as UUID strings (CharFields).
    """

    class Meta:
        model = ChiTietPhieuNhapDieuChinhGiaHangMuaModel
        fields = [
            'uuid',
            'phieu_nhap',
            'line',
            'id_hd',
            'line_hd',
            'ma_vt',
            'dvt',
            'ma_kho',
            'tk_vt',
            'ma_lo',
            'ma_vi_tri',
            'ma_thue',
            'tk_thue',
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_lsx',
            'ma_cp0',
            'entity_model',
            'lo_yn',
            'vi_tri_yn',
            'qc_yn',
            'id_hd4',
            'id_hd5',
            'id_hd7',
            'he_so',
            'so_luong',
            'gia_nt',
            'tien_nt',
            'thue_suat',
            'thue_nt',
            'thue',
            'gia',
            'tien',
            'ten_tk_vt',
            'ten_tk_thue',
        ]
        read_only_fields = ['uuid']

    def validate(self, data):  # noqa: C901
        # Normalize empty strings -> None for optional char fields
        for f in ['id_hd', 'line_hd']:
            if f in data and data[f] == "":
                data[f] = None
        return data

    def validate(self, attrs):  # noqa: C901
        """
        Validate the serializer data.
        """
        # Check if this is an update or create operation
        is_update = self.instance is not None
        # Validate required fields for create operations
        if not is_update:
            required_fields = [
                'ma_vt',
                'dvt',
                'ma_kho',
                'tk_vt',
                'line',
                'he_so',
                'so_luong',
                'ten_tk_vt',
            ]
            for field in required_fields:
                if field not in attrs:
                    raise serializers.ValidationError(
                        {field: _('This field is required.')}
                    )

        # Validate line number is positive
        if 'line' in attrs and attrs['line'] <= 0:
            raise serializers.ValidationError(
                {'line': _('Line number must be positive.')}
            )

        # Validate decimal fields are positive
        decimal_fields = [
            'he_so',
            'so_luong',
            'gia_nt',
            'tien_nt',
            'thue_suat',
            'thue_nt',
            'thue',
            'gia',
            'tien',
        ]
        for field in decimal_fields:
            if field in attrs and attrs[field] is not None and attrs[field] < 0:
                raise serializers.ValidationError(
                    {field: _('This field must be positive.')}
                )

        # Validate tax rate is between 0 and 100
        if 'thue_suat' in attrs and attrs['thue_suat'] is not None:
            if attrs['thue_suat'] < 0 or attrs['thue_suat'] > 100:
                raise serializers.ValidationError(
                    {'thue_suat': _('Tax rate must be between 0 and 100.')}
                )

        # Validate quantity is positive
        if (
            'so_luong' in attrs
            and attrs['so_luong'] is not None
            and attrs['so_luong'] <= 0
        ):
            raise serializers.ValidationError(
                {'so_luong': _('Quantity must be positive.')}
            )

        # Validate conversion factor is positive
        if 'he_so' in attrs and attrs['he_so'] is not None and attrs['he_so'] <= 0:
            raise serializers.ValidationError(
                {'he_so': _('Conversion factor must be positive.')}
            )

        # Validate integer flags are 0 or 1
        flag_fields = ['lo_yn', 'vi_tri_yn', 'qc_yn']
        for field in flag_fields:
            if field in attrs and attrs[field] is not None:
                if attrs[field] not in [0, 1]:
                    raise serializers.ValidationError(
                        {field: _('This field must be 0 or 1.')}
                    )

        return attrs
