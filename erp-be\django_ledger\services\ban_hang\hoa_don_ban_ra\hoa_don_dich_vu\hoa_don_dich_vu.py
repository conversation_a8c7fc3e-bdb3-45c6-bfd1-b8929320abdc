"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for HoaDonDichVu model.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401

from django.db import transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401
from django.shortcuts import get_object_or_404  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu import (  # noqa: F401,
    ChiTietHoaDonModel,
    HoaDonDichVuModel,
    ThongTinThanhToanHoaDonDichVuModel,
)
from django_ledger.models.entity import EntityModel  # noqa: F401,
from django_ledger.repositories.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu import (  # noqa: F401,
    ChiTietHoaDonRepository,
    HoaDonDichVuRepository,
    ThongTinThanhToanHoaDonDichVuRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,
from django_ledger.utils_new.debt_management import CongNoCreation
from django_ledger.utils_new.lookup_uuid_utils import (
    lookup_phieu_xuat_kho_uuid,
    lookup_related_document_uuids_ban_hang,
)
from django_ledger.utils_new.xoa_chung_tu import XoaChungTu


class HoaDonDichVuService(BaseService):
    """
    Service class for handling HoaDonDichVu model business logic.

    ✅ ENHANCED: Integrated with Unified Accounting Service
    ERP Expert - 20 năm kinh nghiệm

    Parameters
    ----------
    entity_slug : str
        The entity slug to filter by.
    user_model : UserModel
        The user model to check permissions.
    """

    # ✅ PREDEFINED CONFIGURATION: Hóa đơn dịch vụ accounting mappings
    SALES_INVOICE_ACCOUNTING_CONFIG = [
       {
            'journal_type': 'DT0CK',  # Doanh thu
            'debit_account_field': 'tk',  # Tài khoản công nợ - DEBIT
            'credit_account_field': 'tk_dt',  # Tài khoản doanh thu - CREDIT
            'debit_account_source': 'header',
            'credit_account_source': 'detail',
            'amount_field': 'tien_nt2',  # Trường tạm thời
            'detail_source': 'chi_tiet',  # Nguồn chi tiết tạm thời
            'detail_conditions': {
                'tien_nt2': {'gt': 0},
                'tk_dt': {'is_not_null': True},
                'tk_ck': {'is_null': True},
            },
            'canCreate': True,
        },
         {
            'journal_type': 'DTCK',  # Doanh thu
            'debit_account_field': 'tk',  # Tài khoản công nợ - DEBIT
            'credit_account_field': 'tk_dt',  # Tài khoản doanh thu - CREDIT
            'debit_account_source': 'header',
            'credit_account_source': 'detail',
            'amount_field': 'tien_nt2',  # Trường tạm thời
            'detail_source': 'chi_tiet',  # Nguồn chi tiết tạm thời
            'detail_conditions': {
                'tien_nt2': {'gt': 0},
                'tk_dt': {'is_not_null': True},
                'tk_ck': {'is_not_null': True},
            },
            'canCreate': True,
        },
         {
            'journal_type': 'CK',  # Doanh thu
            'debit_account_field': 'tk_ck',  # Tài khoản công nợ - DEBIT
            'credit_account_field': 'tk',  # Tài khoản doanh thu - CREDIT
            'debit_account_source': 'detail',
            'credit_account_source': 'header',
            'amount_field': 'ck_nt',  # Trường tạm thời
            'detail_source': 'chi_tiet',  # Nguồn chi tiết tạm thời
            'detail_conditions': {
                'ck_nt': {'gt': 0},
                'tk_ck': {'is_not_null': True},
            },
            'canCreate': True,
        },
        {
            'journal_type': 'THUE',
            'debit_account_field': 'tk',
            'credit_account_field': 'tk_thue_co',
            'debit_account_source': 'header',
            'credit_account_source': 'detail',
            'amount_field': 'thue_nt',
            'detail_source': 'chi_tiet',
            'detail_conditions': {
                'thue_nt': {'gt': 0},
                'tk_thue_co': {'is_not_null': True},
            },
            'canCreate': True,
        },
    ]

    def __init__(self, entity_slug: str, user_model):  # noqa: C901
        self.entity_slug = entity_slug
        self.user_model = user_model
        self.repository = HoaDonDichVuRepository()
        self.chi_tiet_repository = ChiTietHoaDonRepository()
        self.thanh_toan_repository = ThongTinThanhToanHoaDonDichVuRepository()
        self.model = HoaDonDichVuModel
        self.model_verbose_name = _('Hóa đơn dịch vụ')
        self._cong_no_service = CongNoCreation()
        self.xoa_chung_tu = XoaChungTu()

        # Logger
        import logging

        self.logger = logging.getLogger(__name__)

        super().__init__()

    def _determine_accounting_mappings(
        self, hoa_don: HoaDonDichVuModel
    ) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định accounting mappings dựa trên status và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Chỉ tạo bút toán khi status = 3 hoặc 5 (đã duyệt/hoàn thành)
        - Conditional accounting entry creation dựa trên document status
        - Flexible business rules cho different scenarios
        - Support complex approval workflows

        Parameters
        ----------
        hoa_don : HoaDonDichVuModel
            Hóa đơn dịch vụ để analyze

        Returns
        -------
        List[Dict[str, Any]]
            Danh sách accounting mappings với canCreate được set theo business logic
        """
        # Get base configuration
        mappings = self.SALES_INVOICE_ACCOUNTING_CONFIG.copy()
        has_valid_status = getattr(hoa_don, 'status', '1') in ['1', '3', '5']

        if has_valid_status:
            for mapping in mappings:
                mapping['canCreate'] = True
        else:
            for mapping in mappings:
                mapping['canCreate'] = False

        return mappings

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the base queryset for the model.

        Returns
        -------
        QuerySet
            Base queryset for the model.
        """
        return self.repository.get_for_entity(
            entity_slug=self.entity_slug, user_model=self.user_model
        )

    def list_with_related_uuids(
        self, entity_slug=None, user_model=None, **kwargs
    ) -> List[HoaDonDichVuModel]:
        """
        Lists HoaDonBanHangModel instances with related UUIDs mapped.

        This method efficiently maps related document UUIDs for multiple invoices
        without causing N+1 query problems.

        Parameters
        ----------
        entity_slug : str, optional
            The entity slug to filter by, by default None.
        user_model : UserModel, optional
            The user model to check permissions, by default None.
        **kwargs : dict
            Additional filters to apply to the queryset.

        Returns
        -------
        List[HoaDonBanHangModel]
            A list of HoaDonBanHangModel instances with related UUIDs mapped.
        """

        # Get the base queryset
        queryset = self.repository.list(entity_slug, **kwargs)

        # Convert to list to add attributes
        hoa_don_list = list(queryset)

        # Map related UUIDs for each invoice
        for hoa_don in hoa_don_list:
            try:
                related_uuids = lookup_related_document_uuids_ban_hang(
                    hoa_don_uuid=str(hoa_don.uuid),
                    pt_tao_yn=hoa_don.pt_tao_yn,
                    ma_httt=hoa_don.ma_httt,
                )

                # Add the related UUIDs as attributes to the model instance
                hoa_don.phieu_thu_uuids = related_uuids.get('phieu_thu_uuids', [])
                hoa_don.giay_bao_co_uuids = related_uuids.get('giay_bao_co_uuids', [])

                # Lookup PhieuXuatKho UUID
                try:
                    phieu_xuat_kho_uuid = lookup_phieu_xuat_kho_uuid(
                        hoa_don_uuid=str(hoa_don.uuid)
                    )
                    hoa_don.phieu_xuat_kho_uuid = phieu_xuat_kho_uuid
                except Exception:
                    hoa_don.phieu_xuat_kho_uuid = ''

            except Exception as e:
                # Set empty arrays on error
                hoa_don.phieu_thu_uuids = []
                hoa_don.giay_bao_co_uuids = []
                hoa_don.phieu_xuat_kho_uuid = ''

        return hoa_don_list

    def get_entity_model(self) -> EntityModel:  # noqa: C901
        """
        Get the entity model for the service.

        Returns
        -------
        EntityModel
            The entity model.
        """
        return get_object_or_404(EntityModel, slug=self.entity_slug)

    def get_by_uuid(self, uuid: str) -> HoaDonDichVuModel:  # noqa: C901
        """
        Get a HoaDonDichVuModel by UUID with related document UUIDs.

        Parameters
        ----------
        uuid : str
            The UUID of the HoaDonDichVuModel.

        Returns
        -------
        HoaDonDichVuModel
            The HoaDonDichVuModel with the specified UUID.
            The model will have additional attributes:
            - phieu_thu_uuid: UUID of related PhieuThu if exists (when pt_tao_yn=True and ma_httt='TMB')
            - giay_bao_co_uuid: UUID of related GiayBaoCo if exists (when pt_tao_yn=True and ma_httt='CKB')
        """
        hoa_don = self.repository.get_by_uuid(uuid)

        # Lookup related document UUIDs based on pt_tao_yn and ma_httt
        related_uuids = lookup_related_document_uuids_ban_hang(
            hoa_don_uuid=str(uuid), pt_tao_yn=hoa_don.pt_tao_yn, ma_httt=hoa_don.ma_httt
        )

        # Add the related UUIDs as attributes to the model instance
        hoa_don.phieu_thu_uuids = related_uuids.get('phieu_thu_uuids', [])
        hoa_don.giay_bao_co_uuids = related_uuids.get('giay_bao_co_uuids', [])

        return hoa_don

    @transaction.atomic
    def create(self, data: Dict[str, Any]) -> HoaDonDichVuModel:  # noqa: C901
        """
        Create a new HoaDonDichVuModel.
        Handles ChungTuMixIn logic for document number generation.

        Parameters
        ----------
        data : Dict[str, Any]
            The data for the new HoaDonDichVuModel.

        Returns
        -------
        HoaDonDichVuModel
            The created HoaDonDichVuModel.
        """
        # Extract chi_tiet data if present
        chi_tiet_data = data.pop('chi_tiet', [])

        # Validate and process ChungTuMixIn fields
        self._validate_chung_tu_fields(data)

        # Create the HoaDonDichVuModel
        hoa_don = self.repository.create(entity_slug=self.entity_slug, data=data)

        # Create ChiTietHoaDonModel instances if provided
        for chi_tiet in chi_tiet_data:
            self.chi_tiet_repository.create(hoa_don=hoa_don, **chi_tiet)


        # ✅ ENHANCED: Auto-create accounting entries after successful creation
        try:
            self._cong_no_service.create_document_accounting_entries(
                source_document=hoa_don,
                document_type="hóa đơn bán dịch vụ",
                account_mappings=self._determine_accounting_mappings(hoa_don),
            )
        except Exception as e:
            # Log the error but don't fail the creation process
            # This allows the invoice to be created even if accounting fails
            print(
                f"Warning: Failed to create accounting entries for invoice {hoa_don.so_ct}: {str(e)}"
            )

        return hoa_don

    def _validate_chung_tu_fields(self, data: Dict[str, Any]) -> None:
        """
        Validate and process ChungTuMixIn fields.
        According to ChungTuMixIn logic, so_ct and i_so_ct are required when ma_nk is provided.

        Parameters
        ----------
        data : Dict[str, Any]
            The data dictionary to validate and modify.
        """
        # If ma_nk is provided, ensure so_ct and i_so_ct are present
        if data.get('ma_nk'):
            if not data.get('so_ct'):
                raise ValueError("so_ct là bắt buộc khi có ma_nk (quyển chứng từ)")
            if not data.get('i_so_ct'):
                raise ValueError("i_so_ct là bắt buộc khi có ma_nk (quyển chứng từ)")

        # Set default values if not provided
        if 'status' not in data:
            data['status'] = "1"
        if 'ty_gia' not in data:
            data['ty_gia'] = 1.0

    @transaction.atomic
    def update(
        self, uuid: str, data: Dict[str, Any]
    ) -> HoaDonDichVuModel:  # noqa: C901
        """
        Update an existing HoaDonDichVuModel.

        Parameters
        ----------
        uuid : str
            The UUID of the HoaDonDichVuModel to update.
        data : Dict[str, Any]
            The data to update the HoaDonDichVuModel with.

        Returns
        -------
        HoaDonDichVuModel
            The updated HoaDonDichVuModel.
        """
        # Extract chi_tiet data if present
        chi_tiet_data = data.pop('chi_tiet', None)
        # Update the HoaDonDichVuModel
        hoa_don = self.repository.update(
            entity_slug=self.entity_slug, uuid=uuid, data=data
        )
        # Update ChiTietHoaDonModel instances if provided
        if chi_tiet_data is not None:
            # Delete existing chi_tiet
            self.chi_tiet_repository.delete_for_hoa_don(hoa_don_uuid=uuid)
            # Create new chi_tiet
            for chi_tiet in chi_tiet_data:
                self.chi_tiet_repository.create(hoa_don=hoa_don, **chi_tiet)


        try:
            account_mappings = self._determine_accounting_mappings(hoa_don)
            if hoa_don.ledger:
                # UPDATE existing entries
                self._cong_no_service.update_document_accounting_entries(
                    source_document=hoa_don,
                    document_type="hóa đơn bán dịch vụ",
                    account_mappings=account_mappings,
                )
            else:
                # CREATE new entries if no ledger exists
                self._cong_no_service.create_document_accounting_entries(
                    source_document=hoa_don,
                    document_type="hóa đơn bán dịch vụ",
                    account_mappings=account_mappings,
                )
        except Exception as e:
            # Log the error but don't fail the update process
            # This allows the invoice to be updated even if accounting fails
            print(
                f"Warning: Failed to update accounting entries for invoice {hoa_don.so_ct}: {str(e)}"
            )

        # ✅ CLEANUP WHEN pt_tao_yn IS FALSE (delegated to utils)
        # If pt_tao_yn is False: delete all PhieuThu and GiayBaoCo linked via detail.id_hd == invoice uuid
        if not getattr(hoa_don, 'pt_tao_yn', False):
            self.xoa_chung_tu.delete_phieu_thu_by_hoa_don(hoa_don)
            self.xoa_chung_tu.delete_giay_bao_co_by_hoa_don(hoa_don)

        return hoa_don

    @transaction.atomic
    def delete(self, uuid: str) -> bool:  # noqa: C901
        """
        Delete a HoaDonDichVuModel with accounting integration.

        ✅ ENHANCED: Uses CongNoCreation for proper accounting cleanup
        ERP Expert - 20 năm kinh nghiệm

        Parameters
        ----------
        uuid : str
            The UUID of the HoaDonDichVuModel to delete.

        Returns
        -------
        bool
            True if the deletion was successful, False otherwise.
        """
        # Get instance BEFORE deletion
        instance = self.repository.get_by_id(entity_slug=self.entity_slug, uuid=uuid)
        if not instance:
            self.logger.warning(f"HoaDonDichVu {uuid} not found")
            return False

        # ✅ DELETE RELATED DOCUMENTS: Always attempt to delete PT and GBC linked to this invoice
        try:
            self.xoa_chung_tu.delete_phieu_thu_by_hoa_don(instance)
        except Exception as e:
            self.logger.warning(f"⚠️ Could not delete related PhieuThu: {str(e)}")

        try:
            self.xoa_chung_tu.delete_giay_bao_co_by_hoa_don(instance)
        except Exception as e:
            self.logger.warning(f"⚠️ Could not delete related GiayBaoCo: {str(e)}")

        # ✅ CASCADE PATTERN: Delete accounting entries if ledger exists
        # This will automatically delete the HoaDonDichVu due to CASCADE
        if instance.ledger:
            try:
                self._cong_no_service.delete_document_accounting_entries(
                    source_document=instance
                )
                # HoaDonDichVu is automatically deleted by CASCADE
                return True
            except Exception as e:
                self.logger.error(f"Failed to delete accounting entries: {str(e)}")
                raise Exception(f"Failed to delete accounting entries: {str(e)}") from e
        else:
            # No ledger, delete manually
            # Delete associated ChiTietHoaDonModel instances
            self.chi_tiet_repository.delete_for_hoa_don(hoa_don_uuid=uuid)
            # Delete associated ThongTinThanhToanHoaDonDichVuModel instances
            thanh_toan_instances = list(
                self.thanh_toan_repository.model_class.objects.filter(
                    hoa_don_dich_vu_id=uuid
                )
            )
            for thanh_toan_instance in thanh_toan_instances:
                thanh_toan_instance.delete()

            # Delete the HoaDonDichVuModel
            result = self.repository.delete(entity_slug=self.entity_slug, uuid=uuid)
            return result

    def get_chi_tiet(self, hoa_don_uuid: str) -> QuerySet:  # noqa: C901
        """
        Get ChiTietHoaDonModel instances for a HoaDonDichVuModel.

        Parameters
        ----------
        hoa_don_uuid : str
            The UUID of the HoaDonDichVuModel.

        Returns
        -------
        QuerySet
            QuerySet of ChiTietHoaDonModel instances.
        """
        return self.chi_tiet_repository.get_for_hoa_don(hoa_don_uuid=hoa_don_uuid)

    def get_thanh_toan(self, hoa_don_uuid: str) -> QuerySet:  # noqa: C901
        """
        Get ThongTinThanhToanHoaDonDichVuModel instances for a HoaDonDichVuModel.

        Parameters
        ----------
        hoa_don_uuid : str
            The UUID of the HoaDonDichVuModel.

        Returns
        -------
        QuerySet
            QuerySet of ThongTinThanhToanHoaDonDichVuModel instances.
        """
        return self.thanh_toan_repository.get_by_hoa_don(hoa_don_id=hoa_don_uuid)

    def get_accounting_configuration(self) -> List[Dict[str, Any]]:
        """
        Lấy cấu hình kế toán cho hóa đơn dịch vụ.

        Returns:
            List[Dict[str, Any]]: Danh sách mapping configuration
        """
        return self.SALES_INVOICE_ACCOUNTING_CONFIG.copy()
