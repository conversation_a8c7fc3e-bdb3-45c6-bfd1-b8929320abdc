"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

HoaDonDichVuTraLaiGiamGia (Service Invoice Return/Discount) repository implementation.
"""

from typing import Any, Dict, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import QuerySet  # noqa: F401
from django.shortcuts import get_object_or_404  # noqa: F401,

from django_ledger.models import EntityModel  # noqa: F401,
from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dich_vu_tra_lai_giam_gia import (  # noqa: F401,
    HoaDonDichVuTraLaiGiamGiaModel,
)
from django_ledger.repositories._utils.chung_tu_item_utils import (
    process_chung_tu_fields_extraction_and_conversion,
    update_instance_with_chung_tu_fields,
    create_instance_with_chung_tu_fields,
    process_chung_tu_fields_centralized,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class HoaDonDichVuTraLaiGiamGiaRepository(BaseRepository):
    """
    Repository class for HoaDonDichVuTraLaiGiamGiaModel.
    Handles database operations for the model.
    """

    def __init__(self):  # noqa: C901
        super().__init__(model_class=HoaDonDichVuTraLaiGiamGiaModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for HoaDonDichVuTraLaiGiamGiaModel.

        Returns
        -------
        QuerySet
            The base queryset for HoaDonDichVuTraLaiGiamGiaModel.
        """
        return (
            self.model_class.objects.all()
            .select_related(
                'entity_model',
                'unit_id',
                'ma_nt',
                'ma_kh',
                # ChungTu fields from ChungTuMixIn
                'chung_tu_item',
                'chung_tu_item__ma_nk',
                'chung_tu',
            )
            .prefetch_related('chi_tiet')
        )

    def get_by_id(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> Optional[HoaDonDichVuTraLaiGiamGiaModel]:  # noqa: C901
        """
        Retrieves a HoaDonDichVuTraLaiGiamGiaModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the HoaDonDichVuTraLaiGiamGiaModel to retrieve.

        Returns
        -------
        Optional[HoaDonDichVuTraLaiGiamGiaModel]
            The HoaDonDichVuTraLaiGiamGiaModel with the given UUID, or None if not found.
        """
        try:
            return (
                self.get_queryset()
                .filter(entity_model__slug=entity_slug)
                .get(uuid=uuid)
            )
        except self.model_class.DoesNotExist:
            return None

    def list(self, entity_slug: str, **kwargs) -> QuerySet:  # noqa: C901
        """
        Lists HoaDonDichVuTraLaiGiamGiaModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of HoaDonDichVuTraLaiGiamGiaModel instances.
        """
        return self.get_queryset().filter(entity_model__slug=entity_slug, **kwargs)

    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> HoaDonDichVuTraLaiGiamGiaModel:  # noqa: C901
        """
        Creates a new HoaDonDichVuTraLaiGiamGiaModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new HoaDonDichVuTraLaiGiamGiaModel.

        Returns
        -------
        HoaDonDichVuTraLaiGiamGiaModel
            The created HoaDonDichVuTraLaiGiamGiaModel instance.
        """
        # Get entity model
        entity_model = EntityModel.objects.get(slug=entity_slug)

        # Use the centralized utility to handle ChungTu fields
        chung_tu_fields, data_copy = process_chung_tu_fields_centralized(data)

        # Convert UUIDs for the remaining fields
        processed_data = self.convert_uuids_to_model_instances(data_copy)
        
        # Remove entity_model from processed_data to avoid duplicate parameter error
        processed_data.pop('entity_model', None)

        # Create the instance using the centralized utility
        return create_instance_with_chung_tu_fields(
            model_class=self.model_class,
            entity_model=entity_model,
            chung_tu_fields=chung_tu_fields,
            processed_data=processed_data,
        )


    def update(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[HoaDonDichVuTraLaiGiamGiaModel]:  # noqa: C901
        """
        Updates an existing HoaDonDichVuTraLaiGiamGiaModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the HoaDonDichVuTraLaiGiamGiaModel to update.
        data : Dict[str, Any]
            The data to update the HoaDonDichVuTraLaiGiamGiaModel with.

        Returns
        -------
        Optional[HoaDonDichVuTraLaiGiamGiaModel]
            The updated HoaDonDichVuTraLaiGiamGiaModel instance, or None if not found.
        """
        # Get the instance
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if instance:
            # Use utility function to handle ChungTu field updates
            return update_instance_with_chung_tu_fields(
                instance, data, self.convert_uuids_to_model_instances
            )
        return None

    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a HoaDonDichVuTraLaiGiamGiaModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the HoaDonDichVuTraLaiGiamGiaModel to delete.

        Returns
        -------
        bool
            True if the HoaDonDichVuTraLaiGiamGiaModel was deleted, False otherwise.
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if instance:
            instance.delete()
            return True
        return False

    # def convert_uuids_to_model_instances(
    #     self, data: Dict[str, Any]
    # ) -> Dict[str, Any]:  # noqa: C901
    #     """
    #     Convert UUID strings to model instances for foreign key fields.
    #     Override base implementation completely to avoid UUID conversion issues.

    #     Parameters
    #     ----------
    #     data : Dict[str, Any]
    #         The data containing UUID strings

    #     Returns
    #     -------
    #     Dict[str, Any]
    #         The data with UUID strings converted to model instances
    #     """
    #     # Create a copy to avoid modifying original data
    #     data_copy = data.copy()

    #     # Import required models
    #     from django_ledger.models import (
    #         AccountModel,
    #         CustomerModel,
    #         EntityUnitModel,
    #         NgoaiTeModel,
    #     )

    #     # Only convert specific UUID fields that we know are foreign keys
    #     uuid_field_mappings = {
    #         'ma_kh': CustomerModel,
    #         'ma_nt': NgoaiTeModel,
    #         'unit_id': EntityUnitModel,
    #         'tk': AccountModel,
    #     }

    #     # Convert only the specific UUID fields
    #     for field_name, model_class in uuid_field_mappings.items():
    #         if field_name in data_copy and isinstance(data_copy[field_name], str):
    #             try:
    #                 # Validate it's a proper UUID first
    #                 from uuid import UUID

    #                 UUID(
    #                     data_copy[field_name]
    #                 )  # This will raise ValueError if not valid UUID

    #                 # If valid UUID, try to get the model instance
    #                 data_copy[field_name] = model_class.objects.get(
    #                     uuid=data_copy[field_name]
    #                 )
    #             except (ValueError, model_class.DoesNotExist):
    #                 # If not a valid UUID or model doesn't exist, leave as is
    #                 pass

    #     return data_copy
