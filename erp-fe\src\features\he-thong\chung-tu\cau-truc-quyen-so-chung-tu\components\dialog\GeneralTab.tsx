import { useFormContext } from 'react-hook-form';
import React from 'react';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { donViSearchColumns } from '@/constants/search-columns';
import type { DonViCoSo } from '@/types/schemas';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface GeneralTabProps {
  mode: FormMode;
  donVi: DonViCoSo | null;
  setDonVi: (donVi: DonViCoSo) => void;
}

const GeneralTab = ({ mode, donVi, setDonVi }: GeneralTabProps) => {
  const isViewMode = mode === 'view';
  const { setValue } = useFormContext();

  const handleDonViSelect = (selectedDonVi: DonViCoSo) => {
    setDonVi(selectedDonVi);
    setValue('unit_id', selectedDonVi.uuid);
  };

  return (
    <div className='max-h-[calc(100vh-250px)] overflow-y-auto p-4'>
      <div className='flex flex-col space-y-3'>
        {/* Số chứng từ mẫu */}
        <div className='flex flex-col sm:flex-row sm:items-center'>
          <Label className='min-w-40'>Số chứng từ mẫu</Label>
          <FormField name='so_ct_mau' type='text' disabled={isViewMode} />
        </div>

        {/* Đơn vị */}
        <div className='flex flex-col sm:flex-row sm:items-center'>
          <Label className='min-w-40'>Đơn vị</Label>
          <SearchField<DonViCoSo>
            displayRelatedField={'ten_unit'}
            columnDisplay={'ma_unit'}
            searchEndpoint={`/${QUERY_KEYS.DON_VI}`}
            searchColumns={donViSearchColumns}
            dialogTitle='Danh mục đơn vị'
            value={donVi?.ma_unit || ''}
            onRowSelection={handleDonViSelect}
            disabled={isViewMode}
          />
        </div>

        {/* Kiểm tra trùng số */}
        <div className='flex flex-col sm:flex-row sm:items-center'>
          <Label className='min-w-40'>Kiểm tra trùng số</Label>
          <FormField
            name='kieu_trung_so'
            type='select'
            disabled={isViewMode}
            options={[
              { label: '0. Không kiểm tra', value: '0' },
              { label: '1. Theo ngày', value: '1' },
              { label: '2. Theo tháng', value: '2' },
              { label: '3. Theo quý', value: '3' },
              { label: '4. Theo năm', value: '4' }
            ]}
          />
        </div>

        {/* Số hiện tại */}
        <div className='flex flex-col sm:flex-row sm:items-center'>
          <Label className='min-w-40'>Số hiện tại</Label>
          <FormField name='i_so_ct_ht' type='number' disabled={isViewMode} />
        </div>

        {/* Hiệu lực từ/đến */}
        <div className='flex flex-col sm:flex-row sm:items-center'>
          <Label className='min-w-40'>Hiệu lực từ/đến</Label>
          <div className='flex items-center gap-2'>
            <FormField name='ngay_hl1' type='date' disabled={isViewMode} />
            <span>/</span>
            <FormField name='ngay_hl2' type='date' disabled={isViewMode} />
          </div>
        </div>

        {/* Ký hiệu */}
        <div className='flex flex-col sm:flex-row sm:items-center'>
          <Label className='min-w-40'>Ký hiệu</Label>
          <FormField name='so_ct2' type='text' disabled={isViewMode} />
        </div>

        {/* Trạng thái */}
        <div className='flex flex-col sm:flex-row sm:items-center'>
          <Label htmlFor='status' className='min-w-40'>
            Trạng thái
          </Label>
          <FormField
            id='status'
            type='select'
            name='status'
            disabled={isViewMode}
            options={[
              { label: '1. Còn sử dụng', value: '1' },
              { label: '0. Không sử dụng', value: '0' }
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default GeneralTab;
