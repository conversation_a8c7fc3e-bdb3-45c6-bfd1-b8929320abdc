"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Income Statement Calculator for Multi-Period Income Statement Report.
Expert ERP Implementation - 20+ years experience.

Calculates all income statement indicators using transaction data.
Enhanced to inherit BaseTransactionReportService for better architecture.
"""

import logging
from typing import Dict, Any, List, Union
from datetime import date
from decimal import Decimal
from django_ledger.utils_new.accounting.audit_utils import get_total_movement_for_period
from .income_statement_mapping import IncomeStatementMapping
from .period_calculator import PeriodCalculator

logger = logging.getLogger(__name__)


class IncomeStatementCalculator:
    """
    Calculator for Income Statement (Báo cáo Kết quả Hoạt động Kinh doanh) indicators.
    
    Expert ERP Implementation - Calculates all income statement indicators
    according to Thông tư 200/2014/TT-BTC using transaction data.
    """

    def __init__(self, entity_uuid: str):
        """
        Initialize calculator for specific entity.
        
        Parameters
        ----------
        entity_uuid : str
            Entity UUID
        """
        self.entity_uuid = entity_uuid
        self.mapping = IncomeStatementMapping()

    def calculate_multi_period_income_statement(
        self, ngay_ct: Union[str, date], loai_ky: int, so_ky: int
    ) -> Dict[str, Any]:
        """
        Calculate complete multi-period income statement.
        
        Parameters
        ----------
        ngay_ct : Union[str, date]
            Starting date (YYYY-MM-DD format or date object)
        loai_ky : int
            Period type (0=daily, 1=weekly, 2=monthly, 3=quarterly, 4=semi-annual, 5=yearly)
        so_ky : int
            Number of periods (1-12)
            
        Returns
        -------
        Dict[str, Any]
            Complete income statement data with dynamic columns
        """
        try:
            # Validate parameters
            if not PeriodCalculator.validate_period_parameters_from_start_date(ngay_ct, loai_ky, so_ky):
                return {'results': [], 'count': 0, 'error': 'Invalid period parameters'}

            # Calculate period dates
            period_dates = PeriodCalculator.calculate_period_end_dates_from_start_date(ngay_ct, loai_ky, so_ky)
            if not period_dates:
                return {'results': [], 'count': 0, 'error': 'Could not calculate period dates'}

            # Get period labels
            period_labels = PeriodCalculator.get_period_labels_from_start_date(ngay_ct, loai_ky, so_ky)

            # Calculate indicators for each period
            all_periods_data = {}
            for i, (from_date, to_date) in enumerate(period_dates):
                period_data = self._calculate_period_indicators(from_date, to_date)
                all_periods_data[f'period_{i+1}'] = period_data

            # Transform to response format
            response_data = self._transform_to_response_format(
                all_periods_data, so_ky
            )

            return {
                'results': response_data,
                'count': len(response_data),
                'period_labels': period_labels,
                'metadata': {
                    'ngay_ct': str(ngay_ct),
                    'loai_ky': loai_ky,
                    'so_ky': so_ky,
                    'period_dates': [(str(f), str(t)) for f, t in period_dates]
                }
            }

        except Exception as e:
            logger.error(
                f"Error calculating multi-period income statement: {str(e)}",
                exc_info=True
            )
            return {'results': [], 'count': 0, 'error': f'Calculation error: {str(e)}'}

    def _calculate_period_indicators(
        self, from_date: date, to_date: date
    ) -> Dict[str, Decimal]:
        """
        Calculate all income statement indicators for a specific period.

        Parameters
        ----------
        from_date : date
            Period start date
        to_date : date
            Period end date

        Returns
        -------
        Dict[str, Decimal]
            All calculated indicators for the period
        """
        indicators = {}

        # Get all indicators in proper order
        ordered_indicators = self.mapping.get_ordered_indicators()
        all_indicators = self.mapping.get_all_indicators()

        # Calculate indicators in order (account-based first, then formulas)
        for indicator_code in ordered_indicators:
            indicator_info = all_indicators.get(indicator_code, {})

            if indicator_info.get('type') == 'account_query':
                # Calculate from account data
                account_codes = indicator_info.get('accounts', [])
                opposite_account_codes = indicator_info.get('tk_doi_ung', [])
                value = self._get_account_balance(account_codes, opposite_account_codes, from_date, to_date)
                indicators[indicator_code] = value

            elif indicator_info.get('type') == 'formula':
                # Calculate from formula
                formula = indicator_info.get('formula')
                if formula:
                    value = self._calculate_formula_new(formula, indicators)
                    indicators[indicator_code] = value
                else:
                    indicators[indicator_code] = Decimal('0')
            else:
                # Default to zero for unknown types
                indicators[indicator_code] = Decimal('0')

        return indicators

    def _get_account_balance(
        self, account_codes: List[str], opposite_account_codes: List[str], from_date: date, to_date: date
    ) -> Decimal:
        """
        Gets the total movement for a list of accounts for a specific period using the audit utility.

        This function is now a wrapper around the centralized audit utility, replacing the
        direct transaction query for improved performance and consistency.

        Args:
            account_codes (List[str]): The list of account codes to calculate the movement for.
            from_date (date): The start date of the period.
            to_date (date): The end date of the period.

        Returns:
            Decimal: The total movement (net change) for the accounts in the period, with expenses
                     represented as positive numbers.
        """
        if not account_codes:
            return Decimal('0')

        total_movement = get_total_movement_for_period(
            entity_uuid=self.entity_uuid,
            account_codes=account_codes,
            from_date=from_date,
            to_date=to_date,
            opposite_account_codes=opposite_account_codes
        )

        # The convention for financial reports is to show both revenues and expenses as positive numbers.
        # The formulas (e.g., Revenue - Expense) handle the arithmetic.
        # get_total_movement_for_period returns (credits - debits).
        # - For Revenue accounts (5xx, 7xx), this is positive.
        # - For Expense accounts (6xx, 8xx), this is negative.
        # Therefore, we flip the sign for expense accounts to make them positive.
        if account_codes[0].startswith(('6', '8')):
            return -total_movement

        return total_movement

    def _calculate_formula_new(self, formula: str, indicators: Dict[str, Decimal]) -> Decimal:
        """
        Calculate formula-based indicator using new format [XX]+[YY]-[ZZ].

        Parameters
        ----------
        formula : str
            Formula string (e.g., '[01A]+[01C]', '[20]+[21]-[22]-[25]-[26]')
        indicators : Dict[str, Decimal]
            Previously calculated indicators

        Returns
        -------
        Decimal
            Calculated value
        """
        try:
            import re

            # Extract indicator codes from brackets [XX]
            pattern = r'\[([^\]]+)\]'
            matches = re.findall(pattern, formula)

            # Replace each [XX] with its value
            formula_str = formula
            for code in matches:
                if code in indicators:
                    value = float(indicators[code])
                    formula_str = formula_str.replace(f'[{code}]', str(value))
                else:
                    # If indicator not found, use 0
                    formula_str = formula_str.replace(f'[{code}]', '0')

            # Evaluate the formula
            result = eval(formula_str)
            return Decimal(str(result))

        except Exception as e:
            logger.error(
                f"Error calculating formula '{formula}': {str(e)}",
                exc_info=True
            )
            return Decimal('0')

    def _calculate_formula(self, formula: str, indicators: Dict[str, Decimal]) -> Decimal:
        """
        Calculate formula-based indicator (legacy method).

        Parameters
        ----------
        formula : str
            Formula string (e.g., '01 - 02', '20 + 21 - 22 - 25 - 26')
        indicators : Dict[str, Decimal]
            Previously calculated indicators

        Returns
        -------
        Decimal
            Calculated value
        """
        try:
            # Replace indicator codes with their values
            formula_str = formula
            for code in sorted(indicators.keys(), key=len, reverse=True):
                value = float(indicators[code])
                formula_str = formula_str.replace(code, str(value))

            # Evaluate the formula
            result = eval(formula_str)
            return Decimal(str(result))

        except Exception as e:
            logger.error(
                f"Error calculating formula '{formula}': {str(e)}",
                exc_info=True
            )
            return Decimal('0')

    def _transform_to_response_format(
        self, all_periods_data: Dict[str, Dict[str, Decimal]],
        so_ky: int
    ) -> List[Dict[str, Any]]:
        """
        Transform calculated data to API response format with production business structure.

        Parameters
        ----------
        all_periods_data : Dict[str, Dict[str, Decimal]]
            All periods calculated data
        so_ky : int
            Number of periods

        Returns
        -------
        List[Dict[str, Any]]
            Transformed response data with production business fields
        """
        response_data = []
        all_indicators = self.mapping.get_all_indicators()

        # Use ordered indicators to maintain proper display order
        ordered_indicators = self.mapping.get_ordered_indicators()

        stt_counter = 1
        for indicator_code in ordered_indicators:
            indicator_info = all_indicators.get(indicator_code, {})

            # Create response item with production business structure
            response_item = {
                'stt_in': stt_counter,
                'ma_so': indicator_info.get('ma_so', indicator_code),
                'chi_tieu': indicator_info.get('chi_tieu', ''),
                'thuyet_minh': indicator_info.get('thuyet_minh', ''),
                'xchi_tieu': '',  # Extended indicator (not used in current mapping)
                'xchi_tieu2': '', # Extended indicator 2 (not used in current mapping)
                'tk': ','.join(indicator_info.get('accounts', [])),  # Account codes
                'tk_du': ','.join(indicator_info.get('tk_doi_ung', [])),  # Corresponding accounts
                'dau_cuoi': indicator_info.get('dau_cuoi', ''),
                'systotal': 1 if indicator_info.get('is_total', False) else 0,
            }

            # Add period columns dynamically
            total_amount = Decimal('0')
            for i in range(so_ky):
                period_key = f'period_{i+1}'
                period_data = all_periods_data.get(period_key, {})
                value = float(period_data.get(indicator_code, Decimal('0')))

                # Use period column naming
                column_key = f'period_{i+1}'
                response_item[column_key] = value
                total_amount += Decimal(str(value))

            # Add total column
            response_item['total_amount'] = float(total_amount)

            response_data.append(response_item)
            stt_counter += 1

        return response_data
