"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for PhieuXuatTraLaiNhaCungCap (Supplier Return Note) model.
"""

from decimal import Decimal  # noqa: F401
from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import transaction  # noqa: F401,
from django.db.models import QuerySet  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_xuat_tra_lai_nha_cung_cap import (  # noqa: F401,
    ChiTietPhieuXuatTraLaiNhaCungCapModel,
    PhieuXuatTraLaiNhaCungCapModel,
)
from django_ledger.repositories.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_xuat_tra_lai_nha_cung_cap import (  # noqa: F401,
    ChiTietPhieuXuatTraLaiNhaCungCapRepository,
    PhieuXuatTraLaiNhaCungCapRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,
from django_ledger.utils_new.debt_management.cong_no_creation import (  # noqa: F401,
    CongNoCreation,
)


class PhieuXuatTraLaiNhaCungCapService(BaseService):
    """
    Service for handling PhieuXuatTraLaiNhaCungCap business logic

    Parameters
    ----------
    entity_slug : str
        The entity slug to filter by
    user_model : UserModel
        The user model to check permissions
    """

    # ✅ PREDEFINED CONFIGURATION: Phiếu xuất trả lại nhà cung cấp accounting mappings
    PAYMENT_ACCOUNTING_CONFIG = [
        {
            'journal_type': 'CONGNO',
            'debit_account_field': 'tk',  # Tài khoản chi phí/công nợ - DEBIT (header)
            'credit_account_field': 'tk_vt',  # Tài khoản vật tư - CREDIT (detail)
            'debit_account_source': 'header',  # Lấy debit account từ header
            'credit_account_source': 'detail',  # Lấy credit account từ detail
            'amount_field': 'tien_nt',  # Số tiền từ detail
            'detail_source': 'chi_tiet',  # Related name to detail
            'detail_conditions': {
                'tien_nt': {'gt': 0},
                'tk_vt': {'is_not_null': True},
            },
            'canCreate': True,  # Default: always create entry
        },
        {
            'journal_type': 'THUE',
            'debit_account_field': 'tk',
            'credit_account_field': 'tk_thue',
            'debit_account_source': 'header',
            'credit_account_source': 'detail',
            'amount_field': 'thue_nt',
            'detail_source': '_temp_thue',
            'detail_conditions': {
                'thue_nt': {'gt': 0},
                'tk_thue': {'is_not_null': True},
            },
            'canCreate': True,
        },
    ]

    def __init__(self, entity_slug: str = None, user_model=None):  # noqa: F811,
        self.entity_slug = entity_slug
        self.user_model = user_model
        self.repository = PhieuXuatTraLaiNhaCungCapRepository()
        self.chi_tiet_repository = ChiTietPhieuXuatTraLaiNhaCungCapRepository()
        self.model = PhieuXuatTraLaiNhaCungCapModel
        self.model_verbose_name = _('Phiếu Xuất Trả Lại Nhà Cung Cấp')
        self._cong_no_service = CongNoCreation()
        super().__init__()

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the base queryset for the model

        Returns
        -------
        QuerySet
            Base queryset for the model
        """
        if self.entity_slug and self.user_model:
            return self.repository.get_for_entity(
                entity_slug=self.entity_slug, user_model=self.user_model
            )
        return self.repository.get_queryset()

    def _create_temp_thue_details(self, phieu_xuat_tra_lai: PhieuXuatTraLaiNhaCungCapModel):
        """
        ✅ TEMP DETAIL LOGIC: Creates a temporary detail source for tax entries.

        This method generates a virtual detail from the parent invoice's total tax fields
        (t_thue_nt and tk_thue) so that CongNoCreation can process it like a regular detail line.

        Parameters
        ----------
        phieu_xuat_tra_lai : PhieuXuatTraLaiNhaCungCapModel
            The invoice instance.

        Returns
        -------
        List[object]
            A list containing a temporary tax detail object if conditions are met.
        """
        temp_details = []
        t_thue_nt = getattr(phieu_xuat_tra_lai, 't_thue_nt', 0) or 0
        tk_thue = getattr(phieu_xuat_tra_lai, 'tk_thue', None)

        if t_thue_nt > 0 and tk_thue:
            # Create a temporary detail object with the required fields for the accounting config
            temp_detail = type(
                'TempThueDetail',
                (object,),
                {
                    'tk_thue': tk_thue,
                    'thue_nt': t_thue_nt,
                },
            )()
            temp_details.append(temp_detail)
        return temp_details

    def _determine_accounting_mappings(
        self, phieu_xuat_tra_lai: PhieuXuatTraLaiNhaCungCapModel
    ) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định accounting mappings dựa trên status và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - CONGNO cho phiếu xuất trả lại nhà cung cấp
        - Chỉ tạo bút toán khi status = '3' hoặc status = '5'

        Parameters
        ----------
        phieu_xuat_tra_lai : PhieuXuatTraLaiNhaCungCapModel
            Phiếu xuất trả lại để analyze

        Returns
        -------
        List[Dict[str, Any]]
            Danh sách accounting mappings với canCreate được set theo business logic
        """
        # ✅ BUSINESS RULE: Chỉ tạo bút toán khi status = 3 hoặc status = 5
        if phieu_xuat_tra_lai.status not in ['3', '5']:
            return []

        # CongNoCreation will handle the detail_conditions
        return self.PAYMENT_ACCOUNTING_CONFIG.copy()

    def get(self, uuid: UUID) -> Optional[PhieuXuatTraLaiNhaCungCapModel]:  # noqa: C901
        """
        Get a supplier return note by UUID

        Parameters
        ----------
        uuid : UUID
            The UUID of the supplier return note

        Returns
        -------
        Optional[PhieuXuatTraLaiNhaCungCapModel]
            The supplier return note if found, None otherwise
        """
        return self.repository.get_by_uuid(uuid)



    def get_list(self, **kwargs) -> QuerySet:  # noqa: C901
        """
        Get a list of supplier return notes with optional filters

        Parameters
        ----------
        **kwargs : dict
            Additional filters to apply

        Returns
        -------
        QuerySet
            QuerySet of PhieuXuatTraLaiNhaCungCapModel instances
        """
        queryset = self.get_queryset()
        if kwargs:
            queryset = queryset.filter(**kwargs)
        return queryset

    def validate_data(
        self, data: Dict[str, Any], for_update: bool = False
    ) -> Dict[str, Any]:  # noqa: C901
        """
        Validate supplier return note data

        Parameters
        ----------
        data : Dict[str, Any]
            The data to validate
        for_update : bool
            Whether the validation is for an update operation

        Returns
        -------
        Dict[str, Any]
            The validated data
        """
        # Extract and remove chi_tiet data if present (it should be handled separately)
        chi_tiet_data = data.pop('chi_tiet', None)
        # Validate required fields for create operation
        if not for_update:
            required_fields = ['so_ct', 'ngay_ct', 'ngay_lct', 'status']
            for field in required_fields:
                if field not in data:
                    raise ValueError(f'{field} is required')

        # Return validated data without chi_tiet (it's handled separately)
        return data

    def calculate_totals(
        self, instance: PhieuXuatTraLaiNhaCungCapModel
    ) -> PhieuXuatTraLaiNhaCungCapModel:  # noqa: C901
        """
        Calculate totals for a supplier return note

        Parameters
        ----------
        instance : PhieuXuatTraLaiNhaCungCapModel
            The supplier return note to calculate totals for

        Returns
        -------
        PhieuXuatTraLaiNhaCungCapModel
            The updated supplier return note
        """
        # Get all details for this supplier return note
        chi_tiet_list = self.chi_tiet_repository.get_by_phieu_xuat_tra_lai(
            instance.uuid
        )

        # Initialize totals
        t_so_luong = Decimal('0.00')
        t_tien_nt = Decimal('0.00')
        t_tien = Decimal('0.00')
        t_thue_nt = Decimal('0.00')
        t_thue = Decimal('0.00')
        t_tt_nt = Decimal('0.00')
        t_tt = Decimal('0.00')
        # Calculate totals
        for chi_tiet in chi_tiet_list:
            t_so_luong += chi_tiet.so_luong
            t_tien_nt += chi_tiet.tien_nt
            t_tien += chi_tiet.tien
            t_thue_nt += chi_tiet.thue_nt
            t_thue += chi_tiet.thue
        # Calculate total payment
        t_tt_nt = t_tien_nt + t_thue_nt
        t_tt = t_tien + t_thue
        # Update instance
        instance.t_so_luong = t_so_luong
        instance.t_tien_nt = t_tien_nt
        instance.t_tien = t_tien
        instance.t_thue_nt = t_thue_nt
        instance.t_thue = t_thue
        instance.t_tt_nt = t_tt_nt
        instance.t_tt = t_tt
        instance.save()

        return instance

    def create(
        self,
        entity_model,
        data: Dict[str, Any],
        chi_tiet_data: List[Dict[str, Any]] = None,
    ) -> PhieuXuatTraLaiNhaCungCapModel:  # noqa: C901
        """
        Create a new supplier return note

        Parameters
        ----------
        entity_model : EntityModel
            The entity model
        data : Dict[str, Any]
            The data to create the supplier return note with
        chi_tiet_data : List[Dict[str, Any]], optional
            The detail items data

        Returns
        -------
        PhieuXuatTraLaiNhaCungCapModel
            The created supplier return note
        """
        # Add entity_model to data
        data['entity_model'] = entity_model

        # Validate data
        validated_data = self.validate_data(data)

        # Create supplier return note
        with transaction.atomic():
            instance = self.repository.create(
                entity_slug=self.entity_slug,
                user_model=self.user_model,
                data=validated_data,
            )

            # Create chi_tiet if present
            if chi_tiet_data:
                self.chi_tiet_repository.create_many(
                    phieu_xuat_tra_lai=instance, data_list=chi_tiet_data
                )

                # Calculate totals
                instance = self.calculate_totals(instance)

            # ✅ UNIFIED ACCOUNTING: Tạo bút toán kế toán
            # Only create accounting if ledger is not already assigned to avoid UNIQUE constraint
            if not instance.ledger:
                try:
                      # Create and attach the temporary tax detail source
                    temp_thue_details = self._create_temp_thue_details(instance)
                    class TempThueWrapper:
                        def __init__(self, details_list):
                            self.details_list = details_list
                        def all(self):
                            return self.details_list
                        def filter(self, **kwargs):
                            return self.details_list
                    setattr(instance, '_temp_thue', TempThueWrapper(temp_thue_details))
                        
                    self._cong_no_service.create_document_accounting_entries(
                        source_document=instance,
                        document_type="phiếu xuất trả lại nhà cung cấp",
                        account_mappings=self._determine_accounting_mappings(instance),
                    )
                except Exception as e:
                    # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                    # to maintain data consistency between PhieuXuatTraLai and accounting entries
                    raise Exception(
                        f"Failed to create accounting entry for PhieuXuatTraLai {instance.so_ct}: {str(e)}"
                    ) from e
            # Note: If ledger already exists, skip accounting creation to avoid duplicates

        return instance

    def get_with_details(self, entity_slug: str, uuid: UUID) -> Optional[PhieuXuatTraLaiNhaCungCapModel]:
        """
        Get a PhieuXuatTraLaiNhaCungCap with its related details.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : UUID
            The UUID of the PhieuXuatTraLaiNhaCungCap

        Returns
        -------
        Optional[PhieuXuatTraLaiNhaCungCapModel]
            The PhieuXuatTraLaiNhaCungCap instance with related details or None if not found
        """
        try:
            # Get PhieuXuatTraLaiNhaCungCap with prefetched chi_tiet
            phieu_xuat = (
                PhieuXuatTraLaiNhaCungCapModel.objects
                .filter(entity_model__slug=entity_slug)
                .prefetch_related('chi_tiet')
                .get(uuid=uuid)
            )

            # Add chi_tiet as list for compatibility
            phieu_xuat.chi_tiet_list = list(phieu_xuat.chi_tiet.all())

            return phieu_xuat

        except PhieuXuatTraLaiNhaCungCapModel.DoesNotExist:
            return None

    def update(
        self,
        uuid: UUID,
        data: Dict[str, Any],
        chi_tiet_data: List[Dict[str, Any]] = None,
    ) -> Optional[PhieuXuatTraLaiNhaCungCapModel]:  # noqa: C901
        """
        Update an existing supplier return note

        Parameters
        ----------
        uuid : UUID
            The UUID of the supplier return note to update
        data : Dict[str, Any]
            The data to update the supplier return note with
        chi_tiet_data : List[Dict[str, Any]], optional
            The detail items data

        Returns
        -------
        Optional[PhieuXuatTraLaiNhaCungCapModel]
            The updated supplier return note if found, None otherwise
        """
        # Validate data
        validated_data = self.validate_data(data, for_update=True)

        # Update supplier return note
        with transaction.atomic():
            instance = self.repository.update(uuid, validated_data)
            if instance and chi_tiet_data is not None:
                # Delete existing chi_tiet
                self.chi_tiet_repository.delete_by_phieu_xuat_tra_lai(uuid)

                # Create new chi_tiet
                self.chi_tiet_repository.create_many(
                    phieu_xuat_tra_lai=instance, data_list=chi_tiet_data
                )

                # Calculate totals
                instance = self.calculate_totals(instance)

            # ✅ UNIFIED ACCOUNTING: Create or update accounting entries
            if instance:
                try:
                      # Create and attach the temporary tax detail source
                    temp_thue_details = self._create_temp_thue_details(instance)
                    class TempThueWrapper:
                        def __init__(self, details_list):
                            self.details_list = details_list
                        def all(self):
                            return self.details_list
                        def filter(self, **kwargs):
                            return self.details_list
                    setattr(instance, '_temp_thue', TempThueWrapper(temp_thue_details))

                    account_mappings = self._determine_accounting_mappings(instance)
                    if instance.ledger:
                        self._cong_no_service.update_document_accounting_entries(
                            source_document=instance,
                            document_type="phiếu xuất trả lại nhà cung cấp",
                            account_mappings=account_mappings,
                        )
                    else:
                        self._cong_no_service.create_document_accounting_entries(
                            source_document=instance,
                            document_type="phiếu xuất trả lại nhà cung cấp",
                            account_mappings=account_mappings,
                        )
                except Exception as e:
                    # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                    raise Exception(
                        f"Failed to create/update accounting entry for PhieuXuatTraLai {instance.so_ct}: {str(e)}"
                    ) from e

        return instance

    @transaction.atomic
    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Delete a PhieuXuatTraLaiNhaCungCap instance with accounting cleanup.

        ✅ CASCADE PATTERN: Ledger deletion will automatically delete PhieuXuatTraLaiNhaCungCap
        due to on_delete=models.CASCADE relationship.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuXuatTraLaiNhaCungCap to delete.

        Returns
        -------
        bool
            True if the PhieuXuatTraLaiNhaCungCap was deleted, False otherwise.
        """
        # Get instance BEFORE deletion
        instance = self.get(uuid=uuid)
        if not instance:
            return False

        # ✅ CASCADE PATTERN: Delete accounting entries if ledger exists
        # This will automatically delete the PhieuXuatTraLaiNhaCungCap due to CASCADE
        if instance.ledger:
            self._cong_no_service.delete_document_accounting_entries(
                source_document=instance
            )
            # PhieuXuatTraLaiNhaCungCap is automatically deleted by CASCADE
            return True
        else:
            # No ledger, delete chi_tiet first then the PhieuXuatTraLaiNhaCungCap directly
            self.chi_tiet_repository.delete_by_phieu_xuat_tra_lai(uuid)
            return self.repository.delete(entity_slug=entity_slug, uuid=uuid)
