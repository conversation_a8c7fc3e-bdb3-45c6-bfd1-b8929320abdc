"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Repository for QuyenChungTu (Document Permission) model database operations
"""

from typing import Any, Dict, List, Optional, Tuple  # noqa: F401

from django.core.paginator import Paginator  # noqa: F401
from django.db.models import QuerySet  # noqa: F401
from django.shortcuts import get_object_or_404  # noqa: F401,

from django_ledger.models.entity import EntityModel  # noqa: F401,
from django_ledger.models.quyen_chung_tu import QuyenChungTu  # noqa: F401,
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class QuyenChungTuRepository(BaseRepository):
    """
    Repository for handling QuyenChungTu (Document Permission) model database operations
    """

    def __init__(self):  # noqa: C901
        self.model = QuyenChungTu

    def _convert_uuids_to_model_instances(
        self, data: Dict[str, Any]
    ) -> Dict[str, Any]:  # noqa: C901
        """
        Convert UUID strings to model instances for foreign key fields

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing UUID strings

        Returns
        -------
        Dict[str, Any]
            The data with UUID strings converted to model instances
        """
        processed_data = data.copy()
        # Convert unit_id if present and is a string (UUID)
        if 'unit_id' in processed_data and processed_data['unit_id']:
            from django_ledger.models import EntityUnitModel  # noqa: F401,

            # Skip conversion if unit_id is already an EntityUnitModel instance
            if not isinstance(processed_data['unit_id'], EntityUnitModel):
                try:
                    unit_id = processed_data['unit_id']
                    unit = EntityUnitModel.objects.get(uuid=unit_id)
                    processed_data['unit_id'] = unit
                except (EntityUnitModel.DoesNotExist, ValueError, TypeError):
                    # If unit doesn't exist or unit_id is not a valid UUID, keep as is
                    pass

        return processed_data

    def get_quyen_chung_tu_by_uuid(
        self, uuid: str
    ) -> Optional[QuyenChungTu]:  # noqa: C901
        """
        Get a document permission by UUID

        Parameters
        ----------
        uuid : str
            The UUID of the document permission to retrieve

        Returns
        -------
        Optional[QuyenChungTu]
            The document permission model if found, None otherwise
        """
        try:
            return self.model.objects.get(uuid=uuid)
        except self.model.DoesNotExist:
            return None

    def get_quyen_chung_tu_by_ma_nk(
        self, ma_nk: str
    ) -> Optional[QuyenChungTu]:  # noqa: C901
        """
        Get a document permission by ma_nk

        Parameters
        ----------
        ma_nk : str
            The document permission code

        Returns
        -------
        Optional[QuyenChungTu]
            The document permission model if found, None otherwise
        """
        try:
            return self.model.objects.get(ma_nk=ma_nk)
        except self.model.DoesNotExist:
            return None

    def get_quyen_chung_tu(self, **kwargs) -> QuerySet:  # noqa: C901
        """
        Get all document permissions with optional filters

        Parameters
        ----------
        **kwargs : dict
            Optional filters to apply to the query

        Returns
        -------
        QuerySet
            A queryset of document permissions
        """
        queryset = self.model.objects.all()
        if kwargs:
            queryset = queryset.filter(**kwargs)
        return queryset

    def get_quyen_chung_tu_by_chung_tu_filters(
        self,
        ma_ct: str = None,
        chung_tu_uuid: str = None,
        ngay_hl: str = None
    ) -> QuerySet:  # noqa: C901
        """
        Get document permissions that have chi tiet pointing to a specific chung tu
        with flexible filtering options

        Parameters
        ----------
        ma_ct : str, optional
            The document type code (ma_ct)
        chung_tu_uuid : str, optional
            The UUID of the chung tu (document type)
        ngay_hl : str, optional
            The effective date filter (ngay_hl)

        Returns
        -------
        QuerySet
            A queryset of document permissions that match the specified filters
        """
        queryset = self.model.objects.all()

        # Filter by ma_ct or chung_tu_uuid (one of them is required)
        if chung_tu_uuid:
            queryset = queryset.filter(chi_tiet__ma_ct__uuid=chung_tu_uuid)
        elif ma_ct:
            queryset = queryset.filter(chi_tiet__ma_ct__ma_ct=ma_ct)

        # Filter by ngay_hl if provided
        if ngay_hl:
            # Assuming ngay_hl should be within the validity period of QuyenChungTu
            queryset = queryset.filter(
                ngay_hl1__lte=ngay_hl,
                ngay_hl2__gte=ngay_hl
            )

        return queryset.prefetch_related('chi_tiet', 'chi_tiet__ma_ct').distinct()

    def create_quyen_chung_tu(
        self, data: Dict[str, Any], entity_model=None
    ) -> QuyenChungTu:  # noqa: C901
        """
        Create a new document permission

        Parameters
        ----------
        data : Dict[str, Any]
            The data for the new document permission
        entity_model : EntityModel, optional
            The entity model to associate with the document permission. If provided, it will override  # noqa: E501
            any entity_model in the data.

        Returns
        -------
        QuyenChungTu
            The created document permission model
        """
        # Convert UUID strings to model instances for foreign key fields
        processed_data = self._convert_uuids_to_model_instances(data)
        # If entity_model is provided, use it
        if entity_model:
            processed_data['entity_model'] = entity_model
        return self.model.objects.create(**processed_data)

    def update_quyen_chung_tu(
        self, uuid: str, data: Dict[str, Any], entity_model=None
    ) -> Optional[QuyenChungTu]:  # noqa: C901
        """
        Update an existing document permission

        Parameters
        ----------
        uuid : str
            The UUID of the document permission to update
        data : Dict[str, Any]
            The data to update the document permission with
        entity_model : EntityModel, optional
            The entity model to associate with the document permission. If provided, it will override  # noqa: E501
            any entity_model in the data.

        Returns
        -------
        Optional[QuyenChungTu]
            The updated document permission model, or None if not found
        """
        try:
            # Convert UUID strings to model instances for foreign key fields
            processed_data = self._convert_uuids_to_model_instances(data)
            # If entity_model is provided, use it
            if entity_model:
                processed_data['entity_model'] = entity_model
            quyen_chung_tu = self.get_quyen_chung_tu_by_uuid(uuid)
            if quyen_chung_tu:
                for key, value in processed_data.items():
                    setattr(quyen_chung_tu, key, value)
                quyen_chung_tu.save()
            return quyen_chung_tu
        except self.model.DoesNotExist:
            return None

    def delete_quyen_chung_tu(self, uuid: str) -> bool:  # noqa: C901
        """
        Delete a document permission by UUID

        Parameters
        ----------
        uuid : str
            The UUID of the document permission to delete

        Returns
        -------
        bool
            True if deletion was successful, False otherwise
        """
        try:
            quyen_chung_tu = self.get_quyen_chung_tu_by_uuid(uuid)
            if quyen_chung_tu:
                quyen_chung_tu.delete()
                return True
            return False
        except self.model.DoesNotExist:
            return False

    def get_quyen_chung_tu_for_entity(self, entity_model: EntityModel) -> QuerySet:
        """
        Get document permissions for a specific entity.

        Parameters
        ----------
        entity_model : EntityModel
            The entity model to filter by.

        Returns
        -------
        QuerySet
            A queryset of document permissions.
        """
        return (
            self.get_quyen_chung_tu()
            .filter(entity_model=entity_model)
            .prefetch_related('chi_tiet', 'chi_tiet__ma_ct')
        )

    def get_active_quyen_chung_tu(self, entity_model: EntityModel) -> QuerySet:
        """
        Get active document permissions for a specific entity.

        Parameters
        ----------
        entity_model : EntityModel
            The entity model to filter by.

        Returns
        -------
        QuerySet
            A queryset of active document permissions.
        """
        return (
            self.get_quyen_chung_tu()
            .filter(entity_model=entity_model, status='1')
            .prefetch_related('chi_tiet', 'chi_tiet__ma_ct')
        )

    def get_quyen_chung_tu_by_ma_ct(
        self, entity_model: EntityModel, ma_ct: str
    ) -> QuerySet:
        """
        Get document permissions for a specific document type.

        Parameters
        ----------
        entity_model : EntityModel
            The entity model to filter by.
        ma_ct : str
            The document type code to filter by.

        Returns
        -------
        QuerySet
            A queryset of document permissions.
        """
        return (
            self.get_quyen_chung_tu()
            .filter(entity_model=entity_model, chi_tiet__ma_ct__ma_ct=ma_ct)
            .prefetch_related('chi_tiet', 'chi_tiet__ma_ct')
            .distinct()
        )

    def get_quyen_chung_tu_by_ma_ct_list(
        self, entity_model: EntityModel, ma_ct_list: List[str]
    ) -> QuerySet:
        """
        Get document permissions for multiple document types.

        Parameters
        ----------
        entity_model : EntityModel
            The entity model to filter by.
        ma_ct_list : List[str]
            List of document type codes to filter by.

        Returns
        -------
        QuerySet
            A queryset of document permissions.
        """
        return (
            self.get_quyen_chung_tu()
            .filter(entity_model=entity_model, chi_tiet__ma_ct__ma_ct__in=ma_ct_list)
            .prefetch_related('chi_tiet', 'chi_tiet__ma_ct')
            .distinct()
        )
