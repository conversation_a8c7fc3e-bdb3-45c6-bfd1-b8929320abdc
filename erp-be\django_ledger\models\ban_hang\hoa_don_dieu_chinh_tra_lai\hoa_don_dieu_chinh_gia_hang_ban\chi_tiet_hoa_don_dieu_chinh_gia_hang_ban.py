"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiTietHoaDonDieuChinhGiaHangBan model, which represents the details  # noqa: E501
of a Price Adjustment Invoice for goods sold.
"""

from uuid import uuid4  # noqa: F401

from django.core.exceptions import ValidationError  # noqa: F401,
from django.db import models  # noqa: F401,
from django.utils import timezone  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ChiTietHoaDonDieuChinhGiaHangBanModelQueryset(models.QuerySet):
    """
    A custom defined ChiTietHoaDonDieuChinhGiaHangBan QuerySet.
    """

    def for_invoice(self, invoice_uuid):  # noqa: C901
        """
        Returns details for a specific invoice.
        """
        return self.filter(hoa_don__uuid=invoice_uuid)


class ChiTietHoaDonDieuChinhGiaHangBanModelManager(models.Manager):
    """
    A custom defined ChiTietHoaDonDieuChinhGiaHangBan Manager that will act as an interface to handle the  # noqa: E501
    ChiTietHoaDonDieuChinhGiaHangBan database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom ChiTietHoaDonDieuChinhGiaHangBanModelQueryset.
        """
        return ChiTietHoaDonDieuChinhGiaHangBanModelQueryset(self.model, using=self._db)


class ChiTietHoaDonDieuChinhGiaHangBanModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiTietHoaDonDieuChinhGiaHangBan database will inherit from.  # noqa: E501
    The ChiTietHoaDonDieuChinhGiaHangBan inherits functionality from the following MixIns:  # noqa: E501

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUIDField
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    hoa_don : ForeignKey
        The related HoaDonDieuChinhGiaHangBan.
    id : IntegerField
        The ID.
    line : IntegerField
        The line number.
    ma_vt : ForeignKey
        The material code.
    dvt : CharField
        The unit of measure.
    ten_dvt : CharField
        The unit name.
    ma_kho : CharField
        The warehouse code.
    ten_kho : CharField
        The warehouse name.
    ma_lo : CharField
        The lot code.
    ten_lo : CharField
        The lot name.
    lo_yn : IntegerField
        Has lot.
    ma_vi_tri : CharField
        The location code.
    ten_vi_tri : CharField
        The location name.
    vi_tri_yn : IntegerField
        Has location.
    he_so : DecimalField
        The factor.
    qc_yn : IntegerField
        Has specification.
    so_luong : DecimalField
        The quantity.
    gia_nt2 : DecimalField
        The price in foreign currency 2.
    tien_nt2 : DecimalField
        The amount in foreign currency 2.
    tl_ck : DecimalField
        The discount rate.
    ck_nt : DecimalField
        The discount in foreign currency.
    ma_thue : CharField
        The tax code.
    tk_thue_no : ForeignKey
        The tax debit account.
    ten_tk_thue_no : CharField
        The tax debit account name.
    thue_suat : DecimalField
        The tax rate.
    thue_nt : DecimalField
        The tax in foreign currency.
    tk_du : ForeignKey
        The balance account.
    ten_tk_du : CharField
        The balance account name.
    tk_vt : ForeignKey
        The material account.
    tk_ck : ForeignKey
        The discount account.
    ten_tk_ck : CharField
        The discount account name.
    ma_bp : ForeignKey
        The department code.
    ma_vv : ForeignKey
        The job code.
    ma_hd : ForeignKey
        The contract code.
    ma_dtt : ForeignKey
        The payment object code.
    ma_ku : ForeignKey
        The area code.
    ma_phi : ForeignKey
        The fee code.
    ma_sp : ForeignKey
        The product code.
    ma_lsx : CharField
        The production order code.
    ma_cp0 : ForeignKey
        The invalid cost code.
    gia2 : DecimalField
        The price 2.
    tien2 : DecimalField
        The amount 2.
    ck : DecimalField
        The discount.
    thue : DecimalField
        The tax.
    id_hd : IntegerField
        The contract ID.
    line_hd : IntegerField
        The contract line.
    created_by : CharField
        The creator.
    created_at : DateTimeField
        The creation time.
    """

    uuid = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        verbose_name=_("UUID"),
        help_text=_("Unique identifier for the record"),
    )

    hoa_don = models.ForeignKey(
        "django_ledger.HoaDonDieuChinhGiaHangBanModel",
        on_delete=models.CASCADE,
        related_name="chi_tiet",
        verbose_name=_("Hóa đơn"),
        help_text=_("Hóa đơn"),
    )

    id = models.IntegerField(
        null=True, blank=True, verbose_name=_("ID"), help_text=_("ID")
    )
    line = models.IntegerField(default=1, verbose_name=_("Dòng"), help_text=_("Dòng"))
    ma_vt = models.ForeignKey(
        "django_ledger.VatTuModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã vật tư"),
        help_text=_("Mã vật tư"),
    )

    dvt = models.ForeignKey(
        "django_ledger.DonViTinhModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Đơn vị tính"),
        help_text=_("Đơn vị tính"),
    )

    ten_dvt = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=_("Tên đơn vị tính"),
        help_text=_("Tên đơn vị tính"),
    )

    ma_kho = models.ForeignKey(
        "django_ledger.KhoHangModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã kho"),
        help_text=_("Mã kho"),
    )

    ten_kho = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Tên kho"),
        help_text=_("Tên kho"),
    )

    ma_lo = models.CharField(
        max_length=50,
        verbose_name=_("Mã lô"),
        blank=True,
        help_text=_("Mã lô"),
        null=True,
    )

    ten_lo = models.CharField(
        max_length=255,
        verbose_name=_("Tên lô"),
        blank=True,
        help_text=_("Tên lô"),
        null=True,
    )

    lo_yn = models.IntegerField(
        default=0, verbose_name=_("Có lô"), help_text=_("Có lô")
    )
    ma_vi_tri = models.ForeignKey(
        "django_ledger.ViTriKhoHangModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã vị trí"),
        help_text=_("Mã vị trí"),
    )

    ten_vi_tri = models.CharField(
        max_length=255,
        verbose_name=_("Tên vị trí"),
        blank=True,
        null=True,
        help_text=_("Tên vị trí"),
    )

    vi_tri_yn = models.IntegerField(
        default=0, verbose_name=_("Có vị trí"), help_text=_("Có vị trí")
    )

    he_so = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=1,
        verbose_name=_("Hệ số"),
        help_text=_("Hệ số"),
    )

    qc_yn = models.IntegerField(
        default=0, verbose_name=_("Có quy cách"), help_text=_("Có quy cách")
    )

    so_luong = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Số lượng"),
        help_text=_("Số lượng"),
    )

    gia_nt2 = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Giá ngoại tệ 2"),
        help_text=_("Giá ngoại tệ 2"),
    )

    tien_nt2 = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Tiền ngoại tệ 2"),
        help_text=_("Tiền ngoại tệ 2"),
    )

    tl_ck = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Tỷ lệ chiết khấu"),
        help_text=_("Tỷ lệ chiết khấu"),
    )

    ck_nt = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Chiết khấu ngoại tệ"),
        help_text=_("Chiết khấu ngoại tệ"),
    )

    ma_thue = models.ForeignKey(
        "django_ledger.TaxModel",
        related_name='chi_tiet_hoa_don_dieu_chinh_gia_hang_ban_ma_thue',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_("Mã thuế"),
        help_text=_("Mã thuế"),
    )

    tk_thue_no = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.CASCADE,
        related_name="chi_tiet_hoa_don_dieu_chinh_gia_hang_ban_tk_thue_no",
        verbose_name=_("Tài khoản thuế nợ"),
        help_text=_("Tài khoản thuế nợ"),
    )

    ten_tk_thue_no = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Tên tài khoản thuế nợ"),
        help_text=_("Tên tài khoản thuế nợ"),
    )

    thue_suat = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Thuế suất"),
        help_text=_("Thuế suất"),
    )

    thue_nt = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Thuế ngoại tệ"),
        help_text=_("Thuế ngoại tệ"),
    )

    tk_du = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.CASCADE,
        related_name="chi_tiet_hoa_don_dieu_chinh_gia_hang_ban_tk_du",
        verbose_name=_("Tài khoản dư"),
        help_text=_("Tài khoản dư"),
    )

    ten_tk_du = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Tên tài khoản dư"),
        help_text=_("Tên tài khoản dư"),
    )

    tk_vt = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="chi_tiet_hoa_don_dieu_chinh_gia_hang_ban_tk_vt",
        verbose_name=_("Tài khoản vật tư"),
        help_text=_("Tài khoản vật tư"),
    )

    tk_ck = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="chi_tiet_hoa_don_dieu_chinh_gia_hang_ban_tk_ck",
        verbose_name=_("Tài khoản chiết khấu"),
        help_text=_("Tài khoản chiết khấu"),
    )

    ten_tk_ck = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Tên tài khoản chiết khấu"),
        help_text=_("Tên tài khoản chiết khấu"),
    )

    ma_bp = models.ForeignKey(
        "django_ledger.BoPhanModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã bộ phận"),
        help_text=_("Mã bộ phận"),
    )

    ma_vv = models.ForeignKey(
        "django_ledger.VuViecModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã vụ việc"),
        help_text=_("Mã vụ việc"),
    )

    ma_hd = models.ForeignKey(
        "django_ledger.ContractModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã hợp đồng"),
        help_text=_("Mã hợp đồng"),
    )

    ma_dtt = models.ForeignKey(
        "django_ledger.DotThanhToanModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã đối tượng thanh toán"),
        help_text=_("Mã đối tượng thanh toán"),
    )

    ma_ku = models.ForeignKey(
        "django_ledger.KheUocModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã khu vực"),
        help_text=_("Mã khu vực"),
    )

    ma_phi = models.ForeignKey(
        "django_ledger.PhiModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã phí"),
        help_text=_("Mã phí"),
    )

    ma_sp = models.ForeignKey(
        "django_ledger.VatTuModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="chi_tiet_hoa_don_dieu_chinh_gia_hang_ban_sp",
        verbose_name=_("Mã sản phẩm"),
        help_text=_("Mã sản phẩm"),
    )

    ma_lsx = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Mã lệnh sản xuất"),
        help_text=_("Mã lệnh sản xuất"),
    )

    ma_cp0 = models.ForeignKey(
        "django_ledger.ChiPhiKhongHopLeModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã chi phí"),
        help_text=_("Mã chi phí"),
    )

    gia2 = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Giá 2"),
        help_text=_("Giá 2"),
    )

    tien2 = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Tiền 2"),
        help_text=_("Tiền 2"),
    )

    ck = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Chiết khấu"),
        help_text=_("Chiết khấu"),
    )

    thue = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Thuế"),
        help_text=_("Thuế"),
    )

    id_hd = models.CharField(
        max_length=36,
        verbose_name=_("ID hóa đơn"),
        help_text=_("UUID of HoaDonBanHangModel"),
        blank=True,
        null=True,
    )

    line_hd = models.CharField(
        max_length=36,
        verbose_name=_("Line hóa đơn"),
        help_text=_("UUID of ChiTietHoaDonBanHangModel"),
        blank=True,
        null=True,
    )

    # Thông tin về người tạo và thời gian
    created_by = models.CharField(
        max_length=100,
        default="qtrieukobietcode",
        verbose_name=_("Người tạo"),
        help_text=_("Người tạo"),
    )

    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name=_("Thời gian tạo"),
        help_text=_("Thời gian tạo"),
    )

    objects = ChiTietHoaDonDieuChinhGiaHangBanModelManager.from_queryset(
        ChiTietHoaDonDieuChinhGiaHangBanModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _("Chi tiết hóa đơn điều chỉnh giá hàng bán")
        verbose_name_plural = _("Chi tiết hóa đơn điều chỉnh giá hàng bán")
        indexes = [
            models.Index(fields=["hoa_don"]),
            models.Index(fields=["ma_vt"]),
            models.Index(fields=["line"]),
        ]

    def clean(self):  # noqa: C901
        """
        Custom validation for the model.
        """
        super().clean()

        # Validate ma_vi_tri based on ma_kho.vi_tri_yn
        if self.ma_kho:
            if hasattr(self.ma_kho, 'vi_tri_yn') and self.ma_kho.vi_tri_yn:
                # If warehouse requires location management, ma_vi_tri is required
                if not self.ma_vi_tri or self.ma_vi_tri.strip() == '':
                    raise ValidationError(
                        {
                            'ma_vi_tri': _(
                                'Mã vị trí là bắt buộc khi kho hàng yêu cầu quản lý vị trí.'
                            )
                        }
                    )

    def __str__(self):  # noqa: C901
        return f"{self.hoa_don.so_ct} - Line {self.line}: {self.ma_vt}"


class ChiTietHoaDonDieuChinhGiaHangBanModel(
    ChiTietHoaDonDieuChinhGiaHangBanModelAbstract
):
    """
    Base ChiTietHoaDonDieuChinhGiaHangBan Model Implementation
    """

    class Meta(ChiTietHoaDonDieuChinhGiaHangBanModelAbstract.Meta):
        abstract = False
        db_table = "chi_tiet_hoa_don_dieu_chinh_gia_hang_ban"
