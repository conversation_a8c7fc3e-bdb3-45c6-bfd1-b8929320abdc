"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietDoiTuongHachToanCCDC (Tool Accounting Object Detail) serializer implementation.
"""

from rest_framework import serializers  # noqa: F401

# Import serializers for foreign key fields
from django_ledger.api.serializers.account import (  # noqa: F401
    AccountModelSerializer,
)
from django_ledger.api.serializers.contract import (  # noqa: F401,
    ContractModelSerializer,
)

# Avoid circular import
# from django_ledger.api.serializers.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc.khai_bao_thong_tin_ccdc import KhaiBaoThongTinCCDCModelSerializer  # noqa: E501
from django_ledger.api.serializers.erp import (  # noqa: F401,
    BoPhanModelSerializer,
    PhiModelSerializer,
)
from django_ledger.api.serializers.vat_tu import (  # noqa: F401,
    VatTuSerializer,
)
from django_ledger.api.serializers.vu_viec import (  # noqa: F401,
    VuViecModelSerializer,
)
from django_ledger.api.serializers.warehouse import (  # noqa: F401,
    KhoHangModelSerializer,
)
from django_ledger.models.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc.chi_tiet_doi_tuong_hach_toan_ccdc import (  # noqa: F401,
    ChiTietDoiTuongHachToanCCDCModel,
)


class ChiTietDoiTuongHachToanCCDCModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the ChiTietDoiTuongHachToanCCDCModel.

    This serializer handles the conversion between ChiTietDoiTuongHachToanCCDCModel instances and JSON representations,  # noqa: E501
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields
    - Adds additional fields with "_data" suffix that contain the complete nested object data with all available fields  # noqa: E501
    - When deserializing, accepts UUIDs for reference fields
    """

    # Add nested serializers for foreign key fields
    khai_bao_thong_tin_ccdc_data = serializers.SerializerMethodField(
        read_only=True
    )
    tk_kh_data = AccountModelSerializer(source='tk_kh', read_only=True)
    tk_cp_data = AccountModelSerializer(source='tk_cp', read_only=True)
    # Use SerializerMethodField for other related models to avoid import errors
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietDoiTuongHachToanCCDCModel
        fields = [
            'uuid',
            'khai_bao_thong_tin_ccdc',
            'khai_bao_thong_tin_ccdc_data',
            'ma_cc',
            'line',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_lsx',
            'tk_kh',
            'tk_kh_data',
            'tk_cp',
            'tk_cp_data',
            'he_so',
            'ngay_hl1',
            'ngay_hl2',
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'khai_bao_thong_tin_ccdc',
            'khai_bao_thong_tin_ccdc_data',
            'tk_kh_data',
            'tk_cp_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_ku_data',
            'ma_phi_data',
            'ma_sp_data',
            'created',
            'updated',
        ]

    def get_khai_bao_thong_tin_ccdc_data(self, obj):  # noqa: C901
        """
        Gets basic data for the khai_bao_thong_tin_ccdc field.

        Parameters
        ----------
        obj : ChiTietDoiTuongHachToanCCDCModel
            The ChiTietDoiTuongHachToanCCDCModel instance.

        Returns
        -------
        dict or None
            Dictionary with basic information about the khai_bao_thong_tin_ccdc or None if khai_bao_thong_tin_ccdc is None.  # noqa: E501
        """
        if obj.khai_bao_thong_tin_ccdc:
            # Return basic information to avoid circular import
            return {
                'uuid': obj.khai_bao_thong_tin_ccdc.uuid,
                'ngay_ct': obj.khai_bao_thong_tin_ccdc.ngay_ct,
                'status': obj.khai_bao_thong_tin_ccdc.status,
                'created': obj.khai_bao_thong_tin_ccdc.created,
                'updated': obj.khai_bao_thong_tin_ccdc.updated,
            }
        return None

    def get_ma_bp_data(self, obj):  # noqa: C901
        """
        Gets complete data for the ma_bp field.

        Parameters
        ----------
        obj : ChiTietDoiTuongHachToanCCDCModel
            The ChiTietDoiTuongHachToanCCDCModel instance.

        Returns
        -------
        dict or None
            Complete serialized data of the ma_bp or None if ma_bp is None.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):  # noqa: C901
        """
        Gets complete data for the ma_vv field.

        Parameters
        ----------
        obj : ChiTietDoiTuongHachToanCCDCModel
            The ChiTietDoiTuongHachToanCCDCModel instance.

        Returns
        -------
        dict or None
            Complete serialized data of the ma_vv or None if ma_vv is None.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):  # noqa: C901
        """
        Gets complete data for the ma_hd field.

        Parameters
        ----------
        obj : ChiTietDoiTuongHachToanCCDCModel
            The ChiTietDoiTuongHachToanCCDCModel instance.

        Returns
        -------
        dict or None
            Complete serialized data of the ma_hd or None if ma_hd is None.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_ku_data(self, obj):  # noqa: C901
        """
        Gets complete data for the ma_ku field.

        Parameters
        ----------
        obj : ChiTietDoiTuongHachToanCCDCModel
            The ChiTietDoiTuongHachToanCCDCModel instance.

        Returns
        -------
        dict or None
            Complete serialized data of the ma_ku or None if ma_ku is None.
        """
        if obj.ma_ku:
            return KhoHangModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):  # noqa: C901
        """
        Gets complete data for the ma_phi field.

        Parameters
        ----------
        obj : ChiTietDoiTuongHachToanCCDCModel
            The ChiTietDoiTuongHachToanCCDCModel instance.

        Returns
        -------
        dict or None
            Complete serialized data of the ma_phi or None if ma_phi is None.
        """
        if obj.ma_phi:
            return PhiModelSerializer(obj.ma_phi).data
        return None

    def get_ma_sp_data(self, obj):  # noqa: C901
        """
        Gets complete data for the ma_sp field.

        Parameters
        ----------
        obj : ChiTietDoiTuongHachToanCCDCModel
            The ChiTietDoiTuongHachToanCCDCModel instance.

        Returns
        -------
        dict or None
            Complete serialized data of the ma_sp or None if ma_sp is None.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None
