"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for Bao Cao Ket Qua Hoat Dong Kinh Doanh Nhieu Ky (Multi-Period Income Statement Report).
Expert ERP Implementation - 20+ years experience.

Complete implementation using Transaction data with all income statement line items.
"""

import logging
from typing import Dict, Any, List
from datetime import datetime
from django_ledger.models import EntityModel
from django_ledger.services.transaction_base.transaction_report_service import BaseTransactionReportService
from .utils.income_statement_calculator import IncomeStatementCalculator
from .utils.income_statement_mapping import IncomeStatementMapping

logger = logging.getLogger(__name__)


class BaoCaoKetQuaHoatDongKinhDoanhNhieuKyService(BaseTransactionReportService):
    """
    Service class for Multi-Period Income Statement Report using Transaction data.

    Expert ERP Implementation - 20+ years experience

    This service generates complete income statement reports with:
    - All 16 main income statement indicators according to Thông tư 200
    - Transaction data for accurate calculations
    - Multi-period comparison functionality
    - Complete formula-based calculations
    - Enterprise-grade validation and error handling
    """

    def __init__(self):
        """Initialize the service."""
        super().__init__()
        self.mapping = IncomeStatementMapping()

    def generate_report(
        self, entity_slug: str, filters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate complete multi-period income statement report using Transaction data.

        Expert ERP Implementation - Generates all 16 income statement indicators with proper
        transaction calculations and formula evaluations for multiple periods.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        filters : Dict[str, Any]
            Filter parameters (required):
            - ngay_ct: Start date (YYYY-MM-DD or date object)
            - loai_ky: Period type (0=daily, 1=weekly, 2=monthly, 3=quarterly, 4=semi-annual, 5=yearly)
            - so_ky: Number of periods (1-12)

            Note: Report dates are automatically calculated from these parameters

        Returns
        -------
        Dict[str, Any]
            Complete multi-period income statement data with dynamic columns
        """
        try:
            # Validate entity exists
            entity = self._get_entity(entity_slug)
            if not entity:
                return {'results': [], 'count': 0, 'error': 'Entity not found'}

            # Parse multi-period parameters - new format only
            ngay_ct = filters.get('ngay_ct')
            loai_ky = filters.get('loai_ky')
            so_ky = filters.get('so_ky')

            # Validate required parameters
            if not all([ngay_ct, loai_ky is not None, so_ky]):
                return {'results': [], 'count': 0, 'error': 'Missing required parameters: ngay_ct, loai_ky, so_ky'}

            # Use new period calculator
            from .utils.period_calculator import PeriodCalculator
            if not PeriodCalculator.validate_period_parameters_from_start_date(ngay_ct, loai_ky, so_ky):
                return {'results': [], 'count': 0, 'error': 'Invalid period parameters'}

            # Initialize calculator for this entity
            calculator = IncomeStatementCalculator(str(entity.uuid))

            # Calculate multi-period income statement using unified loai_ky format
            result = calculator.calculate_multi_period_income_statement(
                ngay_ct, loai_ky, so_ky
            )

            return result

        except Exception as e:
            logger.error(
                f"Error generating income statement report: {str(e)}",
                exc_info=True
            )
            # Return error response
            return {
                'results': [],
                'count': 0,
                'error': f'Error generating report: {str(e)}'
            }

    def _get_entity(self, entity_slug: str) -> EntityModel:
        """
        Get entity by slug with error handling.

        Parameters
        ----------
        entity_slug : str
            Entity slug

        Returns
        -------
        EntityModel
            Entity instance or None if not found
        """
        try:
            return EntityModel.objects.get(slug=entity_slug)
        except EntityModel.DoesNotExist:
            return None

    def get_supported_indicators(self) -> List[Dict[str, Any]]:
        """
        Get list of all supported income statement indicators.

        Returns
        -------
        List[Dict[str, Any]]
            List of all supported indicators with metadata
        """
        try:
            all_indicators = self.mapping.get_all_indicators()
            indicators = []

            # Use ordered indicators to maintain proper sequence
            ordered_indicators = self.mapping.get_ordered_indicators()

            for code in ordered_indicators:
                item_info = all_indicators.get(code, {})
                indicators.append({
                    'ma_so': item_info.get('ma_so', code),
                    'chi_tieu': item_info.get('chi_tieu', ''),
                    'thuyet_minh': item_info.get('thuyet_minh', ''),
                    'type': item_info.get('type', ''),
                    'is_total': item_info.get('is_total', False),
                    'formula': item_info.get('formula'),
                    'account_codes': item_info.get('accounts', []),
                    'tk_doi_ung': item_info.get('tk_doi_ung', []),
                    'dau_cuoi': item_info.get('dau_cuoi', '')
                })

            return indicators

        except Exception as e:
            logger.error(f"Error getting supported indicators: {str(e)}", exc_info=True)
            return []

    def validate_request_parameters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate request parameters for income statement report.

        Parameters
        ----------
        filters : Dict[str, Any]
            Request parameters to validate

        Returns
        -------
        Dict[str, Any]
            Validation result with errors if any
        """
        errors = {}

        try:
            # Check required parameters - new format
            required_params = ['ngay_ct', 'loai_ky', 'so_ky']
            for param in required_params:
                if param not in filters or filters[param] is None:
                    errors[param] = f'{param} is required'

            # Validate ngay_ct
            ngay_ct = filters.get('ngay_ct')
            if ngay_ct is not None:
                try:
                    if isinstance(ngay_ct, str):
                        from datetime import datetime
                        datetime.strptime(ngay_ct, '%Y-%m-%d')
                except ValueError:
                    errors['ngay_ct'] = 'ngay_ct must be in YYYY-MM-DD format'

            # Validate loai_ky - new format (0-5)
            loai_ky = filters.get('loai_ky')
            if loai_ky is not None:
                if not isinstance(loai_ky, int) or not (0 <= loai_ky <= 5):
                    errors['loai_ky'] = 'loai_ky must be an integer between 0 and 5 (0=daily, 1=weekly, 2=monthly, 3=quarterly, 4=semi-annual, 5=yearly)'

            # Validate so_ky
            so_ky = filters.get('so_ky')
            if so_ky is not None:
                if not isinstance(so_ky, int) or not (1 <= so_ky <= 12):
                    errors['so_ky'] = 'so_ky must be an integer between 1 and 12'

            return {
                'is_valid': len(errors) == 0,
                'errors': errors
            }

        except Exception as e:
            logger.error(f"Error validating parameters: {str(e)}", exc_info=True)
            return {
                'is_valid': False,
                'errors': {'general': 'Parameter validation error'}
            }

    def get_report_metadata(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get metadata for the income statement report.

        Parameters
        ----------
        filters : Dict[str, Any]
            Report parameters

        Returns
        -------
        Dict[str, Any]
            Report metadata
        """
        try:
            from .utils.period_calculator import PeriodCalculator

            ngay_ct = filters.get('ngay_ct', '2024-01-01')
            loai_ky = filters.get('loai_ky', 2)  # Default to monthly
            so_ky = filters.get('so_ky', 12)

            # Get period information using new format
            period_dates = PeriodCalculator.calculate_period_end_dates_from_start_date(ngay_ct, loai_ky, so_ky)
            period_labels = PeriodCalculator.get_period_labels_from_start_date(ngay_ct, loai_ky, so_ky)

            # Get indicator count
            all_indicators = self.mapping.get_all_indicators()

            return {
                'report_name': 'Báo cáo Kết quả Hoạt động Kinh doanh cho nhiều kỳ',
                'report_code': 'BCKQHDKD_NHIEU_KY',
                'total_indicators': len(all_indicators),
                'period_count': so_ky,
                'period_type': {
                    0: 'Ngày',
                    1: 'Tuần',
                    2: 'Tháng',
                    3: 'Quý',
                    4: 'Bán niên',
                    5: 'Năm'
                }.get(loai_ky, 'Không xác định'),
                'period_labels': period_labels,
                'date_range': {
                    'from': str(period_dates[0][0]) if period_dates else None,
                    'to': str(period_dates[-1][1]) if period_dates else None
                },
                'generated_at': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting report metadata: {str(e)}", exc_info=True)
            return {
                'report_name': 'Báo cáo Kết quả Hoạt động Kinh doanh cho nhiều kỳ',
                'error': 'Could not generate metadata'
            }
