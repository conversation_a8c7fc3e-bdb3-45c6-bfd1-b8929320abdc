"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiTietHoaDonDichVuTraLaiGiamGia model, which represents the details  # noqa: E501
of a Service Invoice Return/Discount.
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ChiTietHoaDonDichVuTraLaiGiamGiaModelQueryset(models.QuerySet):
    """
    A custom defined QuerySet for the ChiTietHoaDonDichVuTraLaiGiamGiaModel.
    """

    def for_hoa_don(self, hoa_don_id):  # noqa: C901
        """
        Returns details for a specific HoaDonDichVuTraLaiGiamGia.

        Parameters
        ----------
        hoa_don_id: UUID
            The HoaDonDichVuTraLaiGiamGiaModel UUID used for filtering the QuerySet.

        Returns
        -------
        ChiTietHoaDonDichVuTraLaiGiamGiaModelQueryset
            A QuerySet of ChiTietHoaDonDichVuTraLaiGiamGiaModel with applied filters.
        """
        return self.filter(hoa_don_id=hoa_don_id)


class ChiTietHoaDonDichVuTraLaiGiamGiaModelManager(models.Manager):
    """
    A custom defined ChiTietHoaDonDichVuTraLaiGiamGiaModel Manager that will act as an interface to handle the  # noqa: E501
    ChiTietHoaDonDichVuTraLaiGiamGiaModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom ChiTietHoaDonDichVuTraLaiGiamGiaModelQueryset.
        """
        return ChiTietHoaDonDichVuTraLaiGiamGiaModelQueryset(self.model, using=self._db)

    def for_hoa_don(self, hoa_don_uuid):  # noqa: C901
        """
        Returns a QuerySet of ChiTietHoaDonDichVuTraLaiGiamGiaModel associated with a specific HoaDonDichVuTraLaiGiamGiaModel.  # noqa: E501

        Parameters
        ----------
        hoa_don_uuid: str or UUID
            The HoaDonDichVuTraLaiGiamGiaModel UUID used for filtering the QuerySet.

        Returns
        -------
        ChiTietHoaDonDichVuTraLaiGiamGiaModelQueryset
            A QuerySet of ChiTietHoaDonDichVuTraLaiGiamGiaModel with applied filters.
        """
        qs = self.get_queryset()
        return qs.filter(hoa_don__uuid__exact=hoa_don_uuid)


class ChiTietHoaDonDichVuTraLaiGiamGiaModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiTietHoaDonDichVuTraLaiGiamGiaModel database will inherit from.  # noqa: E501
    The ChiTietHoaDonDichVuTraLaiGiamGiaModel inherits functionality from the following MixIns:  # noqa: E501

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUIDField
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    hoa_don : ForeignKey
        The HoaDonDichVuTraLaiGiamGiaModel that this detail belongs to.
    line : IntegerField
        The line number.
    tk_no : ForeignKey
        The debit account code.
    dvt : CharField
        The unit of measure.
    so_luong : DecimalField
        The quantity.
    gia_nt2 : DecimalField
        The price in foreign currency 2.
    tien_nt2 : DecimalField
        The amount in foreign currency 2.
    ma_thue : CharField
        The tax code.
    ten_thue : CharField
        The tax name.
    tk_thue_no : ForeignKey
        The tax debit account code.
    ten_tk_thue : CharField
        The tax account name.
    thue_suat : DecimalField
        The tax rate.
    thue_nt : DecimalField
        The tax in foreign currency.
    dien_giai : CharField
        The description.
    gia2 : DecimalField
        The price 2.
    tien2 : DecimalField
        The amount 2.
    thue : DecimalField
        The tax.
    ma_bp : ForeignKey
        The department code.
    ma_vv : ForeignKey
        The case code.
    ma_hd : ForeignKey
        The contract code.
    ma_dtt : ForeignKey
        The payment term code.
    ma_ku : ForeignKey
        The loan code.
    ma_phi : ForeignKey
        The fee code.
    ma_sp : ForeignKey
        The product code.
    ma_lsx : CharField
        The production order code.
    ma_cp0 : ForeignKey
        The invalid expense code.
    id_hd : IntegerField
        The contract ID.
    line_hd : IntegerField
        The contract line.
    """

    uuid = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        verbose_name=_("UUID"),
        help_text=_("Unique identifier for the record"),
    )

    hoa_don = models.ForeignKey(
        'django_ledger.HoaDonDichVuTraLaiGiamGiaModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet',
        verbose_name=_("Hóa đơn"),
        help_text=_("Hóa đơn liên quan"),
    )

    line = models.IntegerField(verbose_name=_("Số dòng"), help_text=_("Số thứ tự dòng"))
    tk_no = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet_hoa_don_dich_vu_tra_lai_giam_gia_tk_no',
        verbose_name=_("Tài khoản nợ"),
        help_text=_("Tài khoản nợ"),
    )

    dvt = models.ForeignKey(
        'django_ledger.DonViTinhModel',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_("Đơn vị tính"),
        help_text=_("Đơn vị tính của dịch vụ"),
        related_name='chi_tiet_hoa_don_dich_vu_tra_lai_giam_gia_dvt',
    )

    so_luong = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Số lượng"),
        help_text=_("Số lượng"),
    )

    gia_nt2 = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Giá ngoại tệ 2"),
        help_text=_("Giá ngoại tệ 2"),
    )

    tien_nt2 = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Tiền ngoại tệ 2"),
        help_text=_("Tiền ngoại tệ 2"),
    )

    ma_thue = models.ForeignKey(
        'django_ledger.TaxModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã thuế"),
        help_text=_("Mã thuế"),
        related_name="chi_tiet_hoa_don_dich_vu_tra_lai_giam_gia",
    )

    ten_thue = models.CharField(
        max_length=255,
        verbose_name=_("Tên thuế"),
        help_text=_("Tên thuế"),
    )

    tk_thue_no = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet_hoa_don_dich_vu_tra_lai_giam_gia_tk_thue_no',
        verbose_name=_("Tài khoản thuế nợ"),
        help_text=_("Tài khoản thuế nợ"),
    )

    ten_tk_thue = models.CharField(
        max_length=255,
        verbose_name=_("Tên tài khoản thuế"),
        help_text=_("Tên tài khoản thuế"),
    )

    thue_suat = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Thuế suất"),
        help_text=_("Thuế suất"),
        null=True,
        blank=True,
    )

    thue_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Thuế ngoại tệ"),
        help_text=_("Thuế ngoại tệ"),
        null=True,
        blank=True,
    )

    dien_giai = models.CharField(
        max_length=255,
        verbose_name=_("Diễn giải"),
        help_text=_("Diễn giải nội dung"),
        null=True,
        blank=True,
    )

    gia2 = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Giá 2"),
        help_text=_("Giá 2"),
        null=True,
        blank=True,
    )

    tien2 = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Tiền 2"),
        help_text=_("Tiền 2"),
        null=True,
        blank=True,
    )

    thue = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_("Thuế"),
        help_text=_("Thuế"),
        null=True,
        blank=True,
    )

    ma_bp = models.ForeignKey(
        'django_ledger.BoPhanModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã bộ phận"),
        help_text=_("Mã bộ phận"),
    )

    ma_vv = models.ForeignKey(
        'django_ledger.VuViecModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã vụ việc"),
        help_text=_("Mã vụ việc"),
    )

    ma_hd = models.ForeignKey(
        'django_ledger.ContractModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã hợp đồng"),
        help_text=_("Mã hợp đồng"),
    )

    ma_dtt = models.ForeignKey(
        'django_ledger.DotThanhToanModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã đối tượng thanh toán"),
        help_text=_("Mã đối tượng thanh toán"),
    )

    ma_ku = models.ForeignKey(
        'django_ledger.KheUocModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã khu"),
        help_text=_("Mã khu"),
    )

    ma_phi = models.ForeignKey(
        'django_ledger.PhiModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã phí"),
        help_text=_("Mã phí"),
    )

    ma_sp = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã sản phẩm"),
        help_text=_("Mã sản phẩm"),
    )

    ma_lsx = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_("Mã lệnh sản xuất"),
        help_text=_("Mã lệnh sản xuất"),
    )

    ma_cp0 = models.ForeignKey(
        'django_ledger.ChiPhiKhongHopLeModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã chi phí"),
        help_text=_("Mã chi phí"),
    )

    id_hd = models.CharField(
        max_length=36,
        verbose_name=_("ID hóa đơn"),
        help_text=_("UUID of HoaDonDichVuModel"),
        blank=True,
        null=True,
    )

    line_hd = models.CharField(
        max_length=36,
        verbose_name=_("Line hóa đơn"),
        help_text=_("UUID of ChiTietHoaDonDichVuModel"),
        blank=True,
        null=True,
    )

    objects = ChiTietHoaDonDichVuTraLaiGiamGiaModelManager.from_queryset(
        ChiTietHoaDonDichVuTraLaiGiamGiaModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _('Chi tiết hóa đơn dịch vụ trả lại giảm giá')
        verbose_name_plural = _('Chi tiết hóa đơn dịch vụ trả lại giảm giá')
        indexes = [
            models.Index(fields=['hoa_don']),
            models.Index(fields=['tk_no']),
            models.Index(fields=['ma_thue']),
        ]
        ordering = ['line']

    def __str__(self):  # noqa: C901
        return f'{self.hoa_don} - {self.line}: {self.dien_giai}'

    def save(self, *args, **kwargs):  # noqa: C901
        # Auto-calculate amount if not provided
        if not self.tien_nt2 and self.so_luong and self.gia_nt2:
            self.tien_nt2 = self.so_luong * self.gia_nt2
        # Auto-calculate amount in local currency if not provided
        if not self.tien2 and self.so_luong and self.gia2:
            self.tien2 = self.so_luong * self.gia2
        # Auto-calculate tax amount if not provided
        if not self.thue_nt and self.tien_nt2 and self.thue_suat:
            self.thue_nt = self.tien_nt2 * (self.thue_suat / 100)
        # Auto-calculate tax amount in local currency if not provided
        if not self.thue and self.tien2 and self.thue_suat:
            self.thue = self.tien2 * (self.thue_suat / 100)
        super().save(*args, **kwargs)


class ChiTietHoaDonDichVuTraLaiGiamGiaModel(
    ChiTietHoaDonDichVuTraLaiGiamGiaModelAbstract
):
    """
    Base ChiTietHoaDonDichVuTraLaiGiamGia Model Implementation
    """

    class Meta(ChiTietHoaDonDichVuTraLaiGiamGiaModelAbstract.Meta):
        abstract = False
        db_table = 'chi_tiet_hoa_don_dich_vu_tra_lai_giam_gia'
