"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Contributions to this module:
- <PERSON> <<EMAIL>>

This module contains the ButToanDieuChinhGiamCongNoMuaHangModel, which represents
a debt reduction adjustment accounting entry in the purchase module.
"""

from decimal import Decimal
from uuid import uuid4

from django.core.exceptions import ValidationError
from django.db import models
from django.utils.translation import gettext_lazy as _

from django_ledger.models._mixins.chung_tu_mixins import ChungTuMixIn
from django_ledger.models.mixins import CreateUpdateMixIn


class ButToanDieuChinhGiamCongNoModelQuerySet(models.QuerySet):
    """
    A custom defined QuerySet for the ButToanDieuChinhGiamCongNoModel.
    This QuerySet will be used to return a set of ButToanDieuChinhGiamCongNoModel that meet certain criteria.
    """

    def active(self):
        """
        Returns active ButToanDieuChinhGiamCongNoModel.

        Returns
        -------
        ButToanDieuChinhGiamCongNoModelQuerySet
            A QuerySet of active ButToanDieuChinhGiamCongNoModel.
        """
        return self.filter(
            status__in=["1", "2", "3", "5"]
        )  # In Review, Approved, Completed, Paid

    def inactive(self):
        """
        Returns inactive ButToanDieuChinhGiamCongNoModel.

        Returns
        -------
        ButToanDieuChinhGiamCongNoModelQuerySet
            A QuerySet of inactive ButToanDieuChinhGiamCongNoModel.
        """
        return self.filter(status__in=["0", "4"])  # Draft, Cancelled


class ButToanDieuChinhGiamCongNoModelManager(models.Manager):
    """
    A custom defined ButToanDieuChinhGiamCongNoModelManager that will act as an interface to handling the DB queries to the
    ButToanDieuChinhGiamCongNoModel.
    """

    def get_queryset(self):
        """
        Returns the custom QuerySet for the ButToanDieuChinhGiamCongNoModel.
        """
        return ButToanDieuChinhGiamCongNoModelQuerySet(self.model, using=self._db)

    def active(self):
        """
        Returns active ButToanDieuChinhGiamCongNoModel.

        Returns
        -------
        ButToanDieuChinhGiamCongNoModelQuerySet
            A QuerySet of active ButToanDieuChinhGiamCongNoModel.
        """
        return self.get_queryset().active()

    def inactive(self):
        """
        Returns inactive ButToanDieuChinhGiamCongNoModel.

        Returns
        -------
        ButToanDieuChinhGiamCongNoModelQuerySet
            A QuerySet of inactive ButToanDieuChinhGiamCongNoModel.
        """
        return self.get_queryset().inactive()

    def for_entity(self, entity_slug: str, user_model):
        """
        Returns ButToanDieuChinhGiamCongNoModel for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        user_model : User
            The user model.

        Returns
        -------
        ButToanDieuChinhGiamCongNoModelQuerySet
            A QuerySet of ButToanDieuChinhGiamCongNoModel for the entity.
        """
        qs = self.get_queryset()
        if entity_slug:
            return qs.filter(
                entity_model__slug__exact=entity_slug, entity_model__admin=user_model
            )
        return qs


class ButToanDieuChinhGiamCongNoMuaHangModelAbstract(ChungTuMixIn, CreateUpdateMixIn):
    """
    This is the main abstract class which the ButToanDieuChinhGiamCongNoModel database will inherit from.
    The ButToanDieuChinhGiamCongNoModel inherits functionality from the following MixIns:

        1. :func:`ChungTuMixIn <django_ledger.models._mixins.chung_tu_mixins.ChungTuMixIn>`
        2. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().

    entity_model : EntityModel
        The entity that this debt adjustment accounting entry belongs to.

    ma_ngv : str
        The employee code who handles the transaction.

    dien_giai : str
        Description of the debt adjustment.

    tk : str
        Account code for the adjustment.

    unit_id : str
        Unit/Department ID.

    i_so_ct : int
        Document sequence number.

    ma_nk : str
        Warehouse code.

    so_ct : str
        Document number.

    chung_tu : str
        Document type.

    ngay_ct : date
        Document date.

    ngay_lct : date
        Last document date.

    ma_nt : str
        Currency code.

    ty_gia : Decimal
        Exchange rate.

    status : str
        Status of the document (0=Inactive, 1=Active).

    transfer_yn : str
        Transfer flag (Y/N).

    hd_yn : str
        Invoice flag (Y/N).

    so_ct0 : str
        Original document number.

    ngay_ct0 : date
        Original document date.

    tg_dd : bool
        Delivery flag.

    cltg_yn : str
        Time processing flag (Y/N).

    so_ct_goc : str
        Root document number.

    dien_giai_ct_goc : str
        Root document description.

    t_tien_nt : Decimal
        Total amount in foreign currency.

    t_tien : Decimal
        Total amount in base currency.

    t_thue_nt : Decimal
        Total tax in foreign currency.

    t_thue : Decimal
        Total tax in base currency.

    chung_tu_item : str
        Document item reference.

    ma_kh : str
        Customer code.

    ma_tt : str
        Payment term code.
    """

    uuid = models.UUIDField(default=uuid4, editable=False, primary_key=True)

    # Entity relationship
    entity_model = models.ForeignKey(
        'django_ledger.EntityModel',
        on_delete=models.CASCADE,
        verbose_name=_('Entity Model'),
        related_name='but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )

    ma_ngv = models.CharField(
        max_length=20, null=True, blank=True, verbose_name=_('Employee Code')
    )
    dien_giai = models.CharField(
        max_length=255, null=True, blank=True, verbose_name=_('Description')
    )

    # Account and Unit fields - ForeignKey relationships
    tk = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Account'),
        related_name='but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )
    unit_id = models.ForeignKey(
        'django_ledger.EntityUnitModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Unit'),
        related_name='but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )

    # Currency fields - ForeignKey to NgoaiTeModel
    ma_nt = models.ForeignKey(
        'django_ledger.NgoaiTeModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Currency'),
        related_name='but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )
    ty_gia = models.DecimalField(
        max_digits=15,
        decimal_places=6,
        default=Decimal('1.000000'),
        verbose_name=_('Exchange Rate'),
    )

    # Status and flags
    status = models.CharField(
        max_length=2,
        choices=[
            ('0', 'Draft'),
            ('1', 'In Review'),
            ('2', 'Approved'),
            ('3', 'Completed'),
            ('4', 'Cancelled'),
            ('5', 'Paid'),
        ],
        default='1',
        verbose_name=_('Status'),
    )
    transfer_yn = models.BooleanField(default=False, verbose_name=_('Transfer'))
    hd_yn = models.CharField(
        max_length=1,
        choices=[('Y', 'Yes'), ('N', 'No')],
        default='N',
        verbose_name=_('Invoice'),
    )

    # Additional document fields
    so_ct0 = models.CharField(
        max_length=50, null=True, blank=True, verbose_name=_('Original Document Number')
    )
    ngay_ct0 = models.DateField(
        null=True, blank=True, verbose_name=_('Original Document Date')
    )
    tg_dd = models.BooleanField(default=False, verbose_name=_('Delivery Flag'))
    cltg_yn = models.BooleanField(
        default=False,
        verbose_name=_('Time Processing'),
        null=True,
        blank=True,
    )

    # Root document fields
    so_ct_goc = models.CharField(
        max_length=50, null=True, blank=True, verbose_name=_('Root Document Number')
    )
    dien_giai_ct_goc = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_('Root Document Description'),
    )

    # Amount fields
    t_tien_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Total Amount (Foreign Currency)'),
    )
    t_tien = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Total Amount'),
    )
    t_thue_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Total Tax (Foreign Currency)'),
    )
    t_thue = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Total Tax'),
    )
    t_tt_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Total Payment (Foreign Currency)'),
    )
    t_tt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('Total Payment'),
    )

    # Reference fields - ForeignKey relationships
    ma_kh = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Customer'),
        related_name='but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )
    ma_tt = models.ForeignKey(
        'django_ledger.HanThanhToanModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Payment Term'),
        related_name='but_toan_dieu_chinh_giam_cong_no_mua_hang_set',
    )

    ledger = models.OneToOneField(
        'django_ledger.LedgerModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_("Sổ cái"),
        help_text=_("Sổ cái được tạo cho bút toán này"),
        related_name="but_toan_dieu_chinh_giam_cong_no_mua_hang",
    )

    objects = ButToanDieuChinhGiamCongNoModelManager()

    class Meta:
        abstract = True
        verbose_name = _('Purchase Debt Reduction Adjustment')
        verbose_name_plural = _('Purchase Debt Reduction Adjustments')
        ordering = ['-updated']
        indexes = [
            models.Index(fields=['entity_model', 'chung_tu_item']),
            models.Index(fields=['entity_model', 'ma_kh']),
            models.Index(fields=['entity_model', 'status']),
        ]

    def __str__(self):
        return f'{self.so_ct} - {self.dien_giai}'

    def clean(self):
        """
        Validates the model data with comprehensive business rules.
        """
        super().clean()

        if self.t_tien_nt and self.t_tien_nt < 0:
            raise ValidationError(
                _('Total amount in foreign currency cannot be negative')
            )

        if self.t_tien and self.t_tien < 0:
            raise ValidationError(_('Total amount cannot be negative'))

        if self.ty_gia and self.ty_gia <= 0:
            raise ValidationError(_('Exchange rate must be greater than zero'))

        # NGHIỆP VỤ: Validate ngày chứng từ không được trong tương lai
        from django.utils import timezone

        if self.ngay_ct and self.ngay_ct > timezone.now().date():
            raise ValidationError(_('Document date cannot be in the future'))

        # NGHIỆP VỤ: Validate ngày lập chứng từ >= ngày chứng từ
        if self.ngay_ct and self.ngay_lct and self.ngay_lct < self.ngay_ct:
            raise ValidationError(_('Document creation date must be >= document date'))

        # NGHIỆP VỤ: Validate tỷ giá > 0 khi có ngoại tệ
        if self.ma_nt and self.ma_nt != 'VND' and (not self.ty_gia or self.ty_gia <= 0):
            raise ValidationError(_('Exchange rate must be > 0 for foreign currency'))

        # NGHIỆP VỤ: Validate số tiền ngoại tệ và VND phải nhất quán
        if self.t_tien_nt and self.ty_gia and self.t_tien:
            calculated_vnd = self.t_tien_nt * self.ty_gia
            if (
                abs(calculated_vnd - self.t_tien) > 1
            ):  # Cho phép sai lệch 1 VND do làm tròn
                raise ValidationError(
                    _('VND amount must equal foreign currency amount × exchange rate')
                )

    def save(self, *args, **kwargs):
        """
        Saves the model instance.
        """
        self.clean()
        super().save(*args, **kwargs)

    @property
    def can_edit(self):
        """
        NGHIỆP VỤ: Kiểm tra có thể chỉnh sửa không.
        Chỉ cho phép chỉnh sửa khi status = '0' (Draft)

        Returns
        -------
        bool
            True if the document can be edited, False otherwise.
        """
        return self.status == '0'

    def can_approve(self) -> bool:
        """
        NGHIỆP VỤ: Kiểm tra có thể duyệt không.
        Chỉ cho phép duyệt khi status = '1' (In Review)
        """
        return self.status == '1'

    def approve(self, user):
        """
        NGHIỆP VỤ: Duyệt bút toán điều chỉnh.
        """
        if not self.can_approve():
            raise ValidationError(_('Cannot approve: Document is not in review status'))

        self.status = '2'  # Approved
        self.save()

    def reject(self, user, reason=''):
        """
        NGHIỆP VỤ: Từ chối bút toán điều chỉnh.
        """
        if self.status not in ['1', '2']:  # Review or Approved
            raise ValidationError(_('Cannot reject: Invalid status'))

        self.status = '0'  # Back to Draft
        self.save()

    @property
    def is_active(self):
        """
        Determines if the document is active.

        Returns
        -------
        bool
            True if the document is active, False otherwise.
        """
        return self.status in [
            '1',
            '2',
            '3',
            '5',
        ]  # In Review, Approved, Completed, Paid

    def get_absolute_url(self):
        """
        Returns the absolute URL for the model instance.

        Returns
        -------
        str
            The absolute URL for the model instance.
        """
        return f'/api/entities/{self.entity_model.slug}/mua-hang/dieu-chinh-can-tru-cong-no/but-toan-dieu-chinh-giam-cong-no/{self.uuid}/'


class ButToanDieuChinhGiamCongNoMuaHangModel(
    ButToanDieuChinhGiamCongNoMuaHangModelAbstract
):
    """
    Base ButToanDieuChinhGiamCongNoMuaHangModel from Abstract.
    """

    class Meta(ButToanDieuChinhGiamCongNoMuaHangModelAbstract.Meta):
        abstract = False
        db_table = "django_ledger_but_toan_dieu_chinh_giam_cong_no_mua_hang"
