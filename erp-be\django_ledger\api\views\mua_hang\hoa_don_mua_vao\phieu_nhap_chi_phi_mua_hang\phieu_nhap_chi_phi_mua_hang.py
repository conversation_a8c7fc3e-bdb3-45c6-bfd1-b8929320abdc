"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the PhieuNhapChiPhiMuaHangViewSet, which handles API endpoints
for the PhieuNhapChiPhiMuaHangModel.
"""

from django.shortcuts import get_object_or_404  # noqa: F401
from rest_framework import status, viewsets  # noqa: F401
from rest_framework.decorators import action  # noqa: F401
from rest_framework.permissions import IsAuthenticated  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.decorators import api_exception_handler  # noqa: F401,
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import (  # noqa: F401,
    PhieuNhapChiPhiMuaHangSerializer,
)
from django_ledger.models import EntityModel  # noqa: F401
from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import (  # noqa: F401,
    PhieuNhapChiPhiMuaHangModel,
)
from django_ledger.services.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import (  # noqa: F401,
    PhieuNhapChiPhiMuaHangService,
)


class PhieuNhapChiPhiMuaHangViewSet(viewsets.ModelViewSet):
    """
    ViewSet for PhieuNhapChiPhiMuaHangModel.
    """

    queryset = PhieuNhapChiPhiMuaHangModel.objects.all()
    serializer_class = PhieuNhapChiPhiMuaHangSerializer
    permission_classes = [IsAuthenticated]
    service = PhieuNhapChiPhiMuaHangService()
    lookup_field = "uuid"

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for PhieuNhapChiPhiMuaHangModel.
        Filters by entity_slug if provided in the URL.
        """
        entity_slug = self.kwargs.get("entity_slug")
        if entity_slug:
            return self.service.list(entity_slug=entity_slug).prefetch_related(
                'chi_tiet_phieu_nhaps', 'chi_phi_phieu_nhaps', 'thue_phieu_nhaps'
            )
        return self.queryset.prefetch_related(
            'chi_tiet_phieu_nhaps', 'chi_phi_phieu_nhaps', 'thue_phieu_nhaps'
        )

    def get_serializer_context(self):  # noqa: C901
        """
        Add additional context to the serializer.
        """
        context = super().get_serializer_context()
        entity_slug = self.kwargs.get("entity_slug")
        if entity_slug:
            entity_model = get_object_or_404(EntityModel, slug=entity_slug)
            context["entity_model"] = entity_model
        # Add related data to context if available
        if self.request.data.get("chi_tiet_phieu_nhaps"):
            context["chi_tiet_phieu_nhaps"] = self.request.data.get(
                "chi_tiet_phieu_nhaps"
            )
        if self.request.data.get("chi_phi_phieu_nhaps"):
            context["chi_phi_phieu_nhaps"] = self.request.data.get(
                "chi_phi_phieu_nhaps"
            )
        if self.request.data.get("chi_phi_chi_tiet_phieu_nhaps"):
            context["chi_phi_chi_tiet_phieu_nhaps"] = self.request.data.get(
                "chi_phi_chi_tiet_phieu_nhaps"
            )
        if self.request.data.get("thue_phieu_nhaps"):
            context["thue_phieu_nhaps"] = self.request.data.get("thue_phieu_nhaps")

        return context

    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new PhieuNhapChiPhiMuaHangModel instance.
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        entity_slug = self.kwargs.get("entity_slug")
        # Get entity model
        entity_model = get_object_or_404(EntityModel, slug=entity_slug)

        # Extract related data from request data
        chi_tiet_data = request.data.get("chi_tiet_phieu_nhaps", [])
        chi_phi_data = request.data.get("chi_phi_phieu_nhaps", [])
        chi_phi_chi_tiet_data = request.data.get("chi_phi_chi_tiet_phieu_nhaps", [])
        thue_data = request.data.get("thue_phieu_nhaps", [])


        # Use service to create phieu nhap with details
        instance = self.service.create_phieu_nhap(
            entity_model=entity_model,
            phieu_nhap_data=validated_data,
            chi_tiet_data=chi_tiet_data,
            chi_phi_data=chi_phi_data,
            chi_phi_chi_tiet_data=chi_phi_chi_tiet_data,
            thue_data=thue_data,
        )

        response_serializer = self.get_serializer(instance)
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update an existing PhieuNhapChiPhiMuaHangModel instance.
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        # Extract related data from request data
        chi_tiet_data = request.data.get("chi_tiet_phieu_nhaps", [])
        chi_phi_data = request.data.get("chi_phi_phieu_nhaps", [])
        chi_phi_chi_tiet_data = request.data.get("chi_phi_chi_tiet_phieu_nhaps", [])
        thue_data = request.data.get("thue_phieu_nhaps", [])


        # Update using service - use simple update for now
        entity_slug = self.kwargs.get("entity_slug")
        updated_instance = self.service.update(
            entity_slug=entity_slug,
            uuid=instance.uuid,
            data=serializer.validated_data,
        )

        response_serializer = self.get_serializer(updated_instance)
        return Response(response_serializer.data)

    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a PhieuNhapChiPhiMuaHangModel instance.
        """
        instance = self.get_object()
        entity_slug = self.kwargs.get("entity_slug")
        success = self.service.delete(entity_slug=entity_slug, uuid=instance.uuid)
        if success:
            return Response(status=status.HTTP_204_NO_CONTENT)
        return Response(
            {"detail": "PhieuNhapChiPhiMuaHang not found."},
            status=status.HTTP_404_NOT_FOUND,
        )

    @action(detail=False, methods=["get"])
    def by_supplier(self, request, *args, **kwargs):  # noqa: C901
        """
        Get PhieuNhapChiPhiMuaHangModel instances for a specific supplier.
        """
        entity_slug = self.kwargs.get("entity_slug")
        supplier_id = request.query_params.get("supplier_id")
        if not supplier_id:
            return Response(
                {"error": "supplier_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        queryset = self.service.get_by_supplier(
            supplier_id=supplier_id,
            entity_slug=entity_slug,
        )

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def by_status(self, request, *args, **kwargs):  # noqa: C901
        """
        Get PhieuNhapChiPhiMuaHangModel instances with a specific status.
        """
        entity_slug = self.kwargs.get("entity_slug")
        status_filter = request.query_params.get("status")
        if not status_filter:
            return Response(
                {"error": "status is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        queryset = self.service.get_by_status(
            status=status_filter,
            entity_slug=entity_slug,
        )

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def by_date_range(self, request, *args, **kwargs):  # noqa: C901
        """
        Get PhieuNhapChiPhiMuaHangModel instances within a date range.
        """
        entity_slug = self.kwargs.get("entity_slug")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if not start_date or not end_date:
            return Response(
                {"error": "start_date and end_date are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        queryset = self.service.get_by_date_range(
            start_date=start_date,
            end_date=end_date,
            entity_slug=entity_slug,
        )

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
