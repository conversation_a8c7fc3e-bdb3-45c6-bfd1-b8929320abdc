"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Repository for PhieuNhapDieuChinhGiaHangMua model.
"""

import uuid
from typing import Any, Dict, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import Q, QuerySet  # noqa: F401

from django_ledger.models import (  # noqa: F401,
    ChungTu,
    EntityModel,
    EntityUnitModel,
    PhieuNhapDieuChinhGiaHangMuaModel,
    QuyenChungTu,
)
from django_ledger.repositories._utils.chung_tu_item_utils import (
    convert_chung_tu_fields_centralized,
    update_instance_with_chung_tu_fields,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class PhieuNhapDieuChinhGiaHangMuaRepository(BaseRepository):
    """
    Repository class for handling PhieuNhapDieuChinhGiaHangMua model database operations.  # noqa: E501
    Implements the Repository pattern for PhieuNhapDieuChinhGiaHangMua.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the repository with the PhieuNhapDieuChinhGiaHangMuaModel.
        """
        super().__init__(model_class=PhieuNhapDieuChinhGiaHangMuaModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for PhieuNhapDieuChinhGiaHangMuaModel.

        Returns
        -------
        QuerySet
            The base queryset for PhieuNhapDieuChinhGiaHangMuaModel.
        """
        return self.model_class.objects.all().select_related(
            'entity_model',
            'ma_kh',
            'tk',
            'ma_nt',
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'chung_tu_item',
        )

    def get_by_id(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> Optional[PhieuNhapDieuChinhGiaHangMuaModel]:  # noqa: C901
        """
        Retrieves a PhieuNhapDieuChinhGiaHangMuaModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuNhapDieuChinhGiaHangMuaModel to retrieve.

        Returns
        -------
        Optional[PhieuNhapDieuChinhGiaHangMuaModel]
            The PhieuNhapDieuChinhGiaHangMuaModel with the given UUID, or None if not found.  # noqa: E501
        """
        try:
            return self.model_class.objects.filter(entity_model__slug=entity_slug).get(
                uuid=uuid
            )
        except self.model_class.DoesNotExist:
            return None

    def list(self, entity_slug: str, **kwargs) -> QuerySet:  # noqa: C901
        """
        Lists PhieuNhapDieuChinhGiaHangMuaModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of PhieuNhapDieuChinhGiaHangMuaModel instances.
        """
        qs = self.model_class.objects.filter(entity_model__slug=entity_slug)
        # Apply search if provided
        search_query = kwargs.get('search_query')
        if search_query:
            qs = qs.filter(
                Q(i_so_ct__icontains=search_query)
                | Q(dien_giai__icontains=search_query)
                | Q(ma_kh__ten_kh__icontains=search_query)
            )

        # Apply status filter if provided
        status = kwargs.get('status')
        if status:
            qs = qs.filter(status=status)
        # Apply date range filter if provided
        from_date = kwargs.get('from_date')
        to_date = kwargs.get('to_date')
        if from_date:
            qs = qs.filter(chung_tu_item__ngay_ct__gte=from_date)
        if to_date:
            qs = qs.filter(chung_tu_item__ngay_ct__lte=to_date)
        # Apply ordering
        return qs.order_by('-chung_tu_item__ngay_ct', '-created')

    def convert_chung_tu_uuids_to_model_instances(
        self, data: dict
    ) -> None:  # noqa: C901
        """
        Convert UUID strings to model instances for ChungTu fields specifically.

        Parameters
        ----------
        data: dict
            The data dictionary containing ChungTu fields to process.
        """

        # Handle ma_nk field (QuyenChungTu reference)
        if 'ma_nk' in data and (
            isinstance(data['ma_nk'], str) or isinstance(data['ma_nk'], uuid.UUID)
        ):
            try:
                data['ma_nk'] = QuyenChungTu.objects.get(uuid__exact=data['ma_nk'])
            except QuyenChungTu.DoesNotExist:
                pass

        # Handle chung_tu field (ChungTu reference)
        if 'chung_tu' in data and (
            isinstance(data['chung_tu'], str) or isinstance(data['chung_tu'], uuid.UUID)
        ):
            try:
                data['chung_tu'] = ChungTu.objects.get(uuid__exact=data['chung_tu'])
            except ChungTu.DoesNotExist:
                pass

        # Handle unit_id field (EntityUnitModel reference)
        if 'unit_id' in data and (
            isinstance(data['unit_id'], str) or isinstance(data['unit_id'], uuid.UUID)
        ):
            try:
                data['unit_id'] = EntityUnitModel.objects.get(
                    uuid__exact=data['unit_id']
                )
            except EntityUnitModel.DoesNotExist:
                pass

    # ✅ REMOVED: convert_uuids_to_model_instances method
    # BaseRepository already handles UUID conversion properly
    # Custom override was causing JSON serialization issues
    # ChungTuSerializerMixin handles field conversion correctly

    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> PhieuNhapDieuChinhGiaHangMuaModel:  # noqa: C901
        """
        Creates a new PhieuNhapDieuChinhGiaHangMuaModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new PhieuNhapDieuChinhGiaHangMuaModel.

        Returns
        -------
        PhieuNhapDieuChinhGiaHangMuaModel
            The created PhieuNhapDieuChinhGiaHangMuaModel instance.
        """
        entity_model = EntityModel.objects.get(slug=entity_slug)

        chung_tu_fields = {}
        chung_tu_field_names = [
            'i_so_ct',
            'ma_nk',
            'so_ct',
            'ngay_ct',
            'ngay_lct',
            'chung_tu',
        ]
        data_copy = data.copy()

        for field_name in chung_tu_field_names:
            if field_name in data_copy:
                chung_tu_fields[field_name] = data_copy.pop(field_name)

        # Convert ChungTu fields using centralized function
        convert_chung_tu_fields_centralized(chung_tu_fields)

        # Convert UUID strings to model instances for remaining data
        self.convert_uuids_to_model_instances(data_copy)

        # Create the instance without ChungTu fields first
        instance = self.model_class(entity_model=entity_model, **data_copy)

        # Set ChungTu fields using property setters
        for field_name, value in chung_tu_fields.items():
            setattr(instance, field_name, value)

        # Save the instance
        instance.save()
        return instance

    def update(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> PhieuNhapDieuChinhGiaHangMuaModel:  # noqa: C901
        """
        Updates an existing PhieuNhapDieuChinhGiaHangMuaModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuNhapDieuChinhGiaHangMuaModel to update.
        data : Dict[str, Any]
            The data to update.

        Returns
        -------
        PhieuNhapDieuChinhGiaHangMuaModel
            The updated PhieuNhapDieuChinhGiaHangMuaModel instance.

        Raises
        ------
        PhieuNhapDieuChinhGiaHangMuaModel.DoesNotExist
            If the instance does not exist.
        """
        instance = self.model_class.objects.filter(entity_model__slug=entity_slug).get(
            uuid=uuid
        )

        # Extract ChungTu fields from original data BEFORE UUID conversion
        chung_tu_fields = {}
        chung_tu_field_names = ['i_so_ct', 'ma_nk', 'so_ct', 'ngay_ct', 'ngay_lct']
        data_copy = data.copy()

        for field_name in chung_tu_field_names:
            if field_name in data_copy:
                chung_tu_fields[field_name] = data_copy.pop(field_name)

        # Convert UUIDs to model instances for ChungTu fields
        self.convert_chung_tu_uuids_to_model_instances(chung_tu_fields)

        # Convert UUID strings to model instances for remaining data
        self.convert_uuids_to_model_instances(data_copy)

        # Update regular fields
        for key, value in data_copy.items():
            setattr(instance, key, value)

        # Set ChungTu fields using property setters
        for field_name, value in chung_tu_fields.items():
            setattr(instance, field_name, value)

        instance.save()
        return instance

    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> None:  # noqa: C901
        """
        Deletes a PhieuNhapDieuChinhGiaHangMuaModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuNhapDieuChinhGiaHangMuaModel to delete.

        Raises
        ------
        PhieuNhapDieuChinhGiaHangMuaModel.DoesNotExist
            If the instance does not exist.
        """
        instance = self.model_class.objects.filter(entity_model__slug=entity_slug).get(
            uuid=uuid
        )

        instance.delete()
