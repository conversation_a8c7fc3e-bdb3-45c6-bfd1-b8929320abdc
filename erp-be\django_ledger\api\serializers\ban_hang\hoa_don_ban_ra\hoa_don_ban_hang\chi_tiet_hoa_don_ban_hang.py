"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Chi Tiet Hoa Don Ban Hang (Sales Invoice Detail) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.chi_phi import ChiPhiModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import (
    KheUocModelSerializer,
)
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.lo import LoModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.tax import TaxModelSerializer
from django_ledger.api.serializers.tien_do_thanh_toan import DotThanhToanModelSerializer

# Import serializers for foreign key fields
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.vi_tri_kho_hang import ViTriKhoHangModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer
from django_ledger.models.ban_hang.don_hang.don_hang.chi_tiet_don_hang import (
    ChiTietDonHangModel,
)
from django_ledger.models.ban_hang.don_hang.don_hang.don_hang import DonHangModel
from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import (
    ChiTietHoaDonBanHangModel,
)


class ChiTietHoaDonBanHangSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietHoaDonBanHangModel.
    """

    ma_vt_data = serializers.SerializerMethodField()
    dvt_data = serializers.SerializerMethodField()
    ma_kho_data = serializers.SerializerMethodField()
    ma_lo_data = serializers.SerializerMethodField()
    ma_vi_tri_data = serializers.SerializerMethodField()
    ma_thue_data = serializers.SerializerMethodField()
    tk_thue_co_data = serializers.SerializerMethodField()
    tk_dt_data = serializers.SerializerMethodField()
    tk_gv_data = serializers.SerializerMethodField()
    tk_vt_data = serializers.SerializerMethodField()
    tk_ck_data = serializers.SerializerMethodField()
    tk_km_data = serializers.SerializerMethodField()
    ma_bp_data = serializers.SerializerMethodField()
    ma_vv_data = serializers.SerializerMethodField()
    ma_hd_data = serializers.SerializerMethodField()
    ma_dtt_data = serializers.SerializerMethodField()
    ma_ku_data = serializers.SerializerMethodField()
    ma_phi_data = serializers.SerializerMethodField()
    ma_sp_data = serializers.SerializerMethodField()
    ma_cp0_data = serializers.SerializerMethodField()
    # Custom object responses
    hoa_don_ban_hang_data = serializers.SerializerMethodField(read_only=True)
    id_dh = serializers.SerializerMethodField(read_only=True)
    line_dh = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietHoaDonBanHangModel
        fields = '__all__'
        read_only_fields = ['uuid', 'created', 'updated']

    def get_ma_vt_data(self, obj):  # noqa: C901
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_dvt_data(self, obj):  # noqa: C901
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_kho_data(self, obj):  # noqa: C901
        if obj.ma_kho:
            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def get_ma_lo_data(self, obj):  # noqa: C901
        if obj.ma_lo:
            return {
                'uuid': obj.ma_lo.uuid,
                'ma_lo': obj.ma_lo.ma_lo,
                'ten_lo': obj.ma_lo.ten_lo,
            }
        return None

    def get_ma_vi_tri_data(self, obj):  # noqa: C901
        if obj.ma_vi_tri:
            return {
                'uuid': obj.ma_vi_tri.uuid,
                'ma_vi_tri': obj.ma_vi_tri.ma_vi_tri,
                'ten_vi_tri': obj.ma_vi_tri.ten_vi_tri,
            }
        return None

    def get_ma_thue_data(self, obj):  # noqa: C901
        """Method field for ma_thue_data"""
        if obj.ma_thue:
            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_tk_thue_co_data(self, obj):  # noqa: C901
        if obj.tk_thue_co:
            return {
                'uuid': obj.tk_thue_co.uuid,
                'code': obj.tk_thue_co.code,
                'name': obj.tk_thue_co.name,
            }
        return None

    def get_tk_dt_data(self, obj):  # noqa: C901
        if obj.tk_dt:
            return {
                'uuid': obj.tk_dt.uuid,
                'code': obj.tk_dt.code,
                'name': obj.tk_dt.name,
            }
        return None

    def get_tk_gv_data(self, obj):  # noqa: C901
        if obj.tk_gv:
            return {
                'uuid': obj.tk_gv.uuid,
                'code': obj.tk_gv.code,
                'name': obj.tk_gv.name,
            }
        return None

    def get_tk_vt_data(self, obj):  # noqa: C901
        if obj.tk_vt:
            return {
                'uuid': obj.tk_vt.uuid,
                'code': obj.tk_vt.code,
                'name': obj.tk_vt.name,
            }
        return None

    def get_tk_ck_data(self, obj):  # noqa: C901
        if obj.tk_ck:
            return {
                'uuid': obj.tk_ck.uuid,
                'code': obj.tk_ck.code,
                'name': obj.tk_ck.name,
            }
        return None

    def get_tk_km_data(self, obj):  # noqa: C901
        if obj.tk_km:
            return {
                'uuid': obj.tk_km.uuid,
                'code': obj.tk_km.code,
                'name': obj.tk_km.name,
            }
        return None

    def get_ma_bp_data(self, obj):  # noqa: C901
        if obj.ma_bp:
            return {
                'uuid': obj.ma_bp.uuid,
                'ma_bp': obj.ma_bp.ma_bp,
                'ten_bp': obj.ma_bp.ten_bp,
            }
        return None

    def get_ma_vv_data(self, obj):  # noqa: C901
        if obj.ma_vv:
            return {
                'uuid': obj.ma_vv.uuid,
                'ma_vv': obj.ma_vv.ma_vu_viec,
                'ten_vv': obj.ma_vv.ten_vu_viec,
            }
        return None

    def get_ma_hd_data(self, obj):  # noqa: C901
        if obj.ma_hd:
            return {
                'uuid': obj.ma_hd.uuid,
                'ma_hd': obj.ma_hd.ma_hd,
                'ten_hd': obj.ma_hd.ten_hd,
            }
        return None

    def get_ma_dtt_data(self, obj):  # noqa: C901
        if obj.ma_dtt:
            return {
                'uuid': obj.ma_dtt.uuid,
                'ma_dtt': obj.ma_dtt.ma_dtt,
                'ten_dtt': obj.ma_dtt.ten_dtt,
            }
        return None

    def get_ma_ku_data(self, obj):  # noqa: C901
        if obj.ma_ku:
            return {
                'uuid': obj.ma_ku.uuid,
                'ma_ku': obj.ma_ku.ma_ku,
                'ten_ku': obj.ma_ku.ten_ku,
            }
        return None

    def get_ma_phi_data(self, obj):  # noqa: C901
        if obj.ma_phi:
            return {
                'uuid': obj.ma_phi.uuid,
                'ma_phi': obj.ma_phi.ma_phi,
                'ten_phi': obj.ma_phi.ten_phi,
            }
        return None

    def get_ma_sp_data(self, obj):  # noqa: C901
        if obj.ma_sp:
            return {
                'uuid': obj.ma_sp.uuid,
                'ma_vt': obj.ma_sp.ma_vt,
                'ten_vt': obj.ma_sp.ten_vt,
            }
        return None

    def get_ma_cp0_data(self, obj):  # noqa: C901
        if obj.ma_cp0:
            return {
                'uuid': obj.ma_cp0.uuid,
                'ma_cpkhl': obj.ma_cp0.ma_cpkhl,
                'ten_cpkhl': obj.ma_cp0.ten_cpkhl,
            }
        return None

    def get_hoa_don_ban_hang_data(self, obj):  # noqa: C901
        if obj.hoa_don_ban_hang:
            so_ct = None
            if hasattr(obj.hoa_don_ban_hang, 'so_ct'):
                so_ct = obj.hoa_don_ban_hang.so_ct
            elif (
                hasattr(obj.hoa_don_ban_hang, 'chung_tu_item')
                and obj.hoa_don_ban_hang.chung_tu_item
            ):
                so_ct = getattr(obj.hoa_don_ban_hang.chung_tu_item, 'so_ct', None)

            return {'uuid': obj.hoa_don_ban_hang.uuid, 'so_ct': so_ct}
        return None

    def get_id_dh(self, obj):  # noqa: C901
        """
        Return object for order id: { uuid, so_ct }
        """
        if not getattr(obj, 'id_dh', None):
            return None
        try:
            dh = DonHangModel.objects.select_related('chung_tu_item').get(
                uuid=obj.id_dh
            )
            so_ct = None
            if hasattr(dh, 'so_ct'):
                so_ct = dh.so_ct
            elif hasattr(dh, 'chung_tu_item') and dh.chung_tu_item:
                so_ct = getattr(dh.chung_tu_item, 'so_ct', None)
            return {"uuid": str(dh.uuid), "so_ct": so_ct}
        except DonHangModel.DoesNotExist:
            return None

    def get_line_dh(self, obj):  # noqa: C901
        """
        Return object for order line: { uuid, line }
        """
        if not getattr(obj, 'line_dh', None):
            return None
        try:
            line = ChiTietDonHangModel.objects.only('uuid', 'line').get(
                uuid=obj.line_dh
            )
            return {"uuid": str(line.uuid), "line": line.line, "sl_cl": line.sl_cl}
        except ChiTietDonHangModel.DoesNotExist:
            return None


class ChiTietHoaDonBanHangCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Write serializer to allow setting id_dh and line_dh as UUID strings (CharFields).
    """

    class Meta:
        model = ChiTietHoaDonBanHangModel
        fields = [
            'uuid',
            'hoa_don_ban_hang',
            'line',
            'id_dh',
            'line_dh',
            'ma_vt',
            'ten_vt0',
            'dvt',
            'ten_dvt',
            'ma_kho',
            'ten_kho',
            'ma_lo',
            'ten_lo',
            'lo_yn',
            'ma_vi_tri',
            'ten_vi_tri',
            'vi_tri_yn',
            'ma_lvt',
            'he_so',
            'qc_yn',
            'ct_km',
            'ma_loai_gb',
            'v_dvt_goc',
            'v_sl_goc',
            'so_luong',
            'gia_nt1',
            'gia_nt2',
            'tien_nt2',
            'tl_ck',
            'ck_nt',
            'px_dd',
            'gia_nt',
            'tien_nt',
            'ma_thue',
            'thue_suat',
            'tk_thue_co',
            'ten_tk_thue_co',
            'thue_nt',
            'tk_dt',
            'ten_tk_dt',
            'tk_gv',
            'ten_tk_gv',
            'tk_vt',
            'ten_tk_vt',
            'tk_ck',
            'ten_tk_ck',
            'tk_km',
            'ten_tk_km',
            'ghi_chu',
            'ma_bp',
            'ten_bp',
            'ma_vv',
            'ten_vv',
            'ma_hd',
            'ten_hd',
            'ma_dtt',
            'ten_dtt',
            'ma_ku',
            'ma_phi',
            'ten_phi',
            'ma_sp',
            'ten_sp',
            'ma_lsx',
            'ma_cp0',
            'ten_cp0',
            'gia',
            'tien',
            'gia1',
            'gia2',
            'tien2',
            'ck',
            'thue',
            'sl_px',
            'id_px',
            'line_px',
            'id_hd',
            'line_hd',
            'id_nhap',
            'line_nhap',
            'sl_cl',
        ]
        read_only_fields = ['uuid']

    def validate(self, data):  # noqa: C901
        # Normalize empty strings -> None for optional char fields
        for f in ['id_dh', 'line_dh']:
            if f in data and data[f] == "":
                data[f] = None
        return data
