"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiTietHoaDonMuaHangTrongNuocRepository, which handles data access for the  # noqa: E501
ChiTietHoaDonMuaHangTrongNuocModel.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ChiTietHoaDonMuaHangTrongNuocModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class ChiTietHoaDonMuaHangTrongNuocRepository(BaseRepository):
    """
    Repository class for ChiTietHoaDonMuaHangTrongNuocModel.
    Handles data access operations for domestic purchase invoice details.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the repository with the ChiTietHoaDonMuaHangTrongNuocModel.
        """
        super().__init__(model_class=ChiTietHoaDonMuaHangTrongNuocModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the base queryset for ChiTietHoaDonMuaHangTrongNuocModel.

        Returns
        -------
        QuerySet
            The base queryset for ChiTietHoaDonMuaHangTrongNuocModel.
        """
        return self.model_class.objects.all()

    def get_by_uuid(
        self, uuid: Union[str, UUID]
    ) -> Optional[ChiTietHoaDonMuaHangTrongNuocModel]:  # noqa: C901
        """
        Get a ChiTietHoaDonMuaHangTrongNuocModel by UUID.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietHoaDonMuaHangTrongNuocModel to retrieve.

        Returns
        -------
        Optional[ChiTietHoaDonMuaHangTrongNuocModel]
            The ChiTietHoaDonMuaHangTrongNuocModel with the specified UUID, or None if not found.  # noqa: E501
        """
        try:
            return self.get_queryset().get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def get_by_hoa_don(self, hoa_don_uuid: Union[str, UUID]) -> QuerySet:  # noqa: C901
        """
        Get all ChiTietHoaDonMuaHangTrongNuocModel instances for a specific invoice.

        Parameters
        ----------
        hoa_don_uuid : Union[str, UUID]
            The UUID of the invoice to filter by.

        Returns
        -------
        QuerySet
            A queryset of ChiTietHoaDonMuaHangTrongNuocModel instances for the specified invoice.  # noqa: E501
        """
        return self.get_queryset().filter(hoa_don_id=hoa_don_uuid).order_by('line')

    def create(
        self, data: Dict[str, Any]
    ) -> ChiTietHoaDonMuaHangTrongNuocModel:  # noqa: C901
        """
        Create a new ChiTietHoaDonMuaHangTrongNuocModel instance.

        Parameters
        ----------
        data : Dict[str, Any]
            The data to create the ChiTietHoaDonMuaHangTrongNuocModel with.

        Returns
        -------
        ChiTietHoaDonMuaHangTrongNuocModel
            The created ChiTietHoaDonMuaHangTrongNuocModel instance.
        """
        # Convert UUID strings to model instances
        processed_data = self.convert_uuids_to_model_instances(data)
        instance = self.model_class(**processed_data)
        instance.save()
        return instance

    def convert_uuids_to_model_instances(
        self, data: Dict[str, Any]
    ) -> Dict[str, Any]:  # noqa: C901
        """
        Convert UUID strings to model instances for foreign key fields.
        This method extends the base implementation to specifically handle
        ChiTietHoaDonMuaHangTrongNuoc related fields and other foreign keys.

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing UUID strings

        Returns
        -------
        Dict[str, Any]
            The data with UUID strings converted to model instances
        """
        # Create a copy and exclude CharField fields that should not be converted
        data_copy = data.copy()

        # Fields that are CharField/IntegerField and should NOT be converted to model instances
        non_uuid_fields = [
            'line',  # IntegerField
            'ten_vt0',  # CharField
            'ten_dvt0',  # CharField
            'ten_lo',  # CharField
            'lo_yn',  # BooleanField
            'vi_tri_yn',  # BooleanField
            'he_so',  # DecimalField
            'qc_yn',  # BooleanField
            'px_dd',  # BooleanField
            'ma_lsx',  # CharField
            'so_ct_dh',  # CharField
            'line_dh',  # CharField (UUID string but handled separately)
            'so_ct_hd4',  # CharField
            'line_hd4',  # IntegerField
            'so_ct_pn',  # CharField
            'id_dh',  # CharField (UUID string but handled separately)
            'id_hd4',  # CharField
            'so_luong',  # DecimalField
            'gia_nt0',  # DecimalField
            'tien_nt0',  # DecimalField
            'tien0',  # DecimalField
            'cp_nt',  # DecimalField
            'gia_ton',  # DecimalField
            'tl_ck',  # DecimalField
            'ck_nt',  # DecimalField
            'thue_nt',  # DecimalField
            'sl_cl',  # IntegerField
        ]

        # Temporarily remove non-UUID fields to prevent conversion
        non_uuid_field_values = {}
        for field in non_uuid_fields:
            if field in data_copy:
                non_uuid_field_values[field] = data_copy.pop(field)

        # Special handling for line_dh - should be UUID string or None
        if 'line_dh' in data_copy:
            line_dh_value = data_copy.pop('line_dh')
            if isinstance(line_dh_value, int):
                # If integer is passed, set to None since line_dh should be UUID string
                non_uuid_field_values['line_dh'] = None
            elif line_dh_value == "" or line_dh_value is None:
                non_uuid_field_values['line_dh'] = None
            elif isinstance(line_dh_value, str):
                # Validate if it's a proper UUID string
                try:
                    from uuid import UUID

                    UUID(line_dh_value)  # Validate UUID format
                    non_uuid_field_values['line_dh'] = line_dh_value
                except ValueError:
                    # If not a valid UUID, set to None
                    non_uuid_field_values['line_dh'] = None
            else:
                non_uuid_field_values['line_dh'] = line_dh_value

        # Use the base implementation to handle common patterns (excluding non-UUID fields)
        data_copy = super().convert_uuids_to_model_instances(data_copy)

        # Restore non-UUID fields with their original values
        data_copy.update(non_uuid_field_values)

        return data_copy

    def create_many(  # noqa: C901
        self, hoa_don_uuid: Union[str, UUID], items: List[Dict[str, Any]]
    ) -> List[ChiTietHoaDonMuaHangTrongNuocModel]:
        """
        Create multiple ChiTietHoaDonMuaHangTrongNuocModel instances for a specific invoice.  # noqa: E501

        Parameters
        ----------
        hoa_don_uuid : Union[str, UUID]
            The UUID of the invoice to create the details for.
        items : List[Dict[str, Any]]
            The list of data to create the ChiTietHoaDonMuaHangTrongNuocModel instances with.  # noqa: E501

        Returns
        -------
        List[ChiTietHoaDonMuaHangTrongNuocModel]
            The list of created ChiTietHoaDonMuaHangTrongNuocModel instances.
        """
        instances = []
        for i, item in enumerate(items, start=1):
            item['hoa_don_id'] = hoa_don_uuid
            if 'line' not in item:
                item['line'] = i
            instance = self.create(item)
            instances.append(instance)
        return instances

    def update(  # noqa: C901
        self, instance: ChiTietHoaDonMuaHangTrongNuocModel, data: Dict[str, Any]
    ) -> ChiTietHoaDonMuaHangTrongNuocModel:
        """
        Update an existing ChiTietHoaDonMuaHangTrongNuocModel instance.

        Parameters
        ----------
        instance : ChiTietHoaDonMuaHangTrongNuocModel
            The ChiTietHoaDonMuaHangTrongNuocModel instance to update.
        data : Dict[str, Any]
            The data to update the ChiTietHoaDonMuaHangTrongNuocModel with.

        Returns
        -------
        ChiTietHoaDonMuaHangTrongNuocModel
            The updated ChiTietHoaDonMuaHangTrongNuocModel instance.
        """
        for key, value in data.items():
            setattr(instance, key, value)
        instance.save()
        return instance

    def delete(
        self, instance: ChiTietHoaDonMuaHangTrongNuocModel
    ) -> bool:  # noqa: C901
        """
        Delete a ChiTietHoaDonMuaHangTrongNuocModel instance.

        Parameters
        ----------
        instance : ChiTietHoaDonMuaHangTrongNuocModel
            The ChiTietHoaDonMuaHangTrongNuocModel instance to delete.

        Returns
        -------
        bool
            True if the instance was deleted, False otherwise.
        """
        try:
            instance.delete()
            return True
        except Exception:
            return False

    def delete_by_hoa_don(self, hoa_don_uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Delete all ChiTietHoaDonMuaHangTrongNuocModel instances for a specific invoice.

        Parameters
        ----------
        hoa_don_uuid : Union[str, UUID]
            The UUID of the invoice to delete the details for.

        Returns
        -------
        bool
            True if the instances were deleted, False otherwise.
        """
        try:
            self.get_by_hoa_don(hoa_don_uuid).delete()
            return True
        except Exception:
            return False
