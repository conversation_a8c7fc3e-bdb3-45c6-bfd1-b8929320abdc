"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiTietPhieuNhapChiPhiMuaHangSerializer, which handles serialization  # noqa: E501
for the ChiTietPhieuNhapChiPhiMuaHangModel.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_tiet_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401
    ChiTietHoaDonMuaHangTrongNuocModel,
)
from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401
    HoaDonMuaHangTrongNuocModel,
)
from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import (  # noqa: F401
    ChiTietPhieuNhapChiPhiMuaHangModel,
)


class ChiTietPhieuNhapChiPhiMuaHangSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietPhieuNhapChiPhiMuaHangModel.
    """

    # Reference data fields
    ma_vt_data = serializers.SerializerMethodField()
    dvt_data = serializers.SerializerMethodField()
    ma_kho_data = serializers.SerializerMethodField()
    tk_vt_data = serializers.SerializerMethodField()
    ma_bp_data = serializers.SerializerMethodField()

    class Meta:
        model = ChiTietPhieuNhapChiPhiMuaHangModel
        fields = [
            'uuid',
            'phieu_nhap',
            'line',
            'id_hd',
            'line_hd',
            'ma_vt',
            'dvt',
            'ten_dvt',
            'ma_kho',
            'ten_kho',
            'he_so',
            'so_luong',
            'tien0',
            'cp_nt',
            'tk_vt',
            'ten_tk_vt',
            'ma_bp',
            'ma_vt_data',
            'dvt_data',
            'ma_kho_data',
            'tk_vt_data',
            'ma_bp_data',
        ]
        read_only_fields = ['uuid', 'phieu_nhap']

    def get_ma_vt_data(self, obj):  # noqa: C901
        """
        Returns the material data for the ma_vt field.
        """
        if obj.ma_vt:
            return {
                'uuid': obj.ma_vt.uuid,
                'ma_vt': obj.ma_vt.ma_vt,
                'ten_vt': obj.ma_vt.ten_vt,
            }
        return None

    def get_dvt_data(self, obj):  # noqa: C901
        """
        Returns the unit data for the dvt field.
        """
        if obj.dvt:
            return {
                'uuid': obj.dvt.uuid,
                'ma_dvt': obj.dvt.dvt,
                'ten_dvt': obj.dvt.ten_dvt,
            }
        return None

    def get_ma_kho_data(self, obj):  # noqa: C901
        """
        Returns the warehouse data for the ma_kho field.
        """
        if obj.ma_kho:
            return {
                'uuid': obj.ma_kho.uuid,
                'ma_kho': obj.ma_kho.ma_kho,
                'ten_kho': obj.ma_kho.ten_kho,
            }
        return None

    def get_tk_vt_data(self, obj):  # noqa: C901
        """
        Returns the account data for the tk_vt field.
        """
        if obj.tk_vt:
            return {
                'uuid': obj.tk_vt.uuid,
                'code': obj.tk_vt.code,
                'name': obj.tk_vt.name,
            }
        return None

    def get_ma_bp_data(self, obj):  # noqa: C901
        """
        Returns the department data for the ma_bp field.
        """
        if obj.ma_bp:
            return {
                'uuid': obj.ma_bp.uuid,
                'ma_bp': obj.ma_bp.ma_bp,
                'ten_bp': obj.ma_bp.ten_bp,
            }
        return None

    def get_id_hd(self, obj):  # noqa: C901
        """
        Return object for invoice id: { uuid, so_ct }
        """
        import re
        from uuid import UUID

        id_hd_val = getattr(obj, "id_hd", None)
        if not id_hd_val:
            return None

        try:
            uuid_str = str(id_hd_val)
            # Use regex to extract the UUID from strings like "{'uuid': UUID('...')}"
            match = re.search(r"([a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12})", uuid_str)
            if match:
                uuid_str = match.group(1)

            hd = HoaDonMuaHangTrongNuocModel.objects.select_related("chung_tu_item").get(
                uuid=UUID(uuid_str)
            )
            so_ct = getattr(hd, "so_ct", None) or getattr(
                hd.chung_tu_item, "so_ct", None
            )
            return {"uuid": str(hd.uuid), "so_ct": so_ct}
        except (HoaDonMuaHangTrongNuocModel.DoesNotExist, ValueError, TypeError):
            return None

    def get_line_hd(self, obj):  # noqa: C901
        """
        Return object for invoice line: { uuid, line }
        """
        import re
        from uuid import UUID

        line_hd_val = getattr(obj, "line_hd", None)
        if not line_hd_val:
            return None

        try:
            uuid_str = str(line_hd_val)
            match = re.search(r"([a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12})", uuid_str)
            if match:
                uuid_str = match.group(1)

            line = ChiTietHoaDonMuaHangTrongNuocModel.objects.only(
                "uuid", "line", "sl_cl"
            ).get(uuid=UUID(uuid_str))
            return {"uuid": str(line.uuid), "line": line.line, "sl_cl": line.sl_cl}
        except (ChiTietHoaDonMuaHangTrongNuocModel.DoesNotExist, ValueError, TypeError):
            return None

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data['id_hd'] = self.get_id_hd(instance)
        data['line_hd'] = self.get_line_hd(instance)
        return data

