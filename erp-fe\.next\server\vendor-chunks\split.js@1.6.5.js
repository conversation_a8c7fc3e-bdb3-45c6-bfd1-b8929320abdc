/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/split.js@1.6.5";
exports.ids = ["vendor-chunks/split.js@1.6.5"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/split.js@1.6.5/node_modules/split.js/dist/split.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/split.js@1.6.5/node_modules/split.js/dist/split.js ***!
  \*******************************************************************************/
/***/ (function(module) {

eval("/*! Split.js - v1.6.5 */\n\n(function (global, factory) {\n     true ? module.exports = factory() :\n    0;\n}(this, (function () { 'use strict';\n\n    // The programming goals of Split.js are to deliver readable, understandable and\n    // maintainable code, while at the same time manually optimizing for tiny minified file size,\n    // browser compatibility without additional requirements\n    // and very few assumptions about the user's page layout.\n    var global = typeof window !== 'undefined' ? window : null;\n    var ssr = global === null;\n    var document = !ssr ? global.document : undefined;\n\n    // Save a couple long function names that are used frequently.\n    // This optimization saves around 400 bytes.\n    var addEventListener = 'addEventListener';\n    var removeEventListener = 'removeEventListener';\n    var getBoundingClientRect = 'getBoundingClientRect';\n    var gutterStartDragging = '_a';\n    var aGutterSize = '_b';\n    var bGutterSize = '_c';\n    var HORIZONTAL = 'horizontal';\n    var NOOP = function () { return false; };\n\n    // Helper function determines which prefixes of CSS calc we need.\n    // We only need to do this once on startup, when this anonymous function is called.\n    //\n    // Tests -webkit, -moz and -o prefixes. Modified from StackOverflow:\n    // http://stackoverflow.com/questions/16625140/js-feature-detection-to-detect-the-usage-of-webkit-calc-over-calc/16625167#16625167\n    var calc = ssr\n        ? 'calc'\n        : ((['', '-webkit-', '-moz-', '-o-']\n              .filter(function (prefix) {\n                  var el = document.createElement('div');\n                  el.style.cssText = \"width:\" + prefix + \"calc(9px)\";\n\n                  return !!el.style.length\n              })\n              .shift()) + \"calc\");\n\n    // Helper function checks if its argument is a string-like type\n    var isString = function (v) { return typeof v === 'string' || v instanceof String; };\n\n    // Helper function allows elements and string selectors to be used\n    // interchangeably. In either case an element is returned. This allows us to\n    // do `Split([elem1, elem2])` as well as `Split(['#id1', '#id2'])`.\n    var elementOrSelector = function (el) {\n        if (isString(el)) {\n            var ele = document.querySelector(el);\n            if (!ele) {\n                throw new Error((\"Selector \" + el + \" did not match a DOM element\"))\n            }\n            return ele\n        }\n\n        return el\n    };\n\n    // Helper function gets a property from the properties object, with a default fallback\n    var getOption = function (options, propName, def) {\n        var value = options[propName];\n        if (value !== undefined) {\n            return value\n        }\n        return def\n    };\n\n    var getGutterSize = function (gutterSize, isFirst, isLast, gutterAlign) {\n        if (isFirst) {\n            if (gutterAlign === 'end') {\n                return 0\n            }\n            if (gutterAlign === 'center') {\n                return gutterSize / 2\n            }\n        } else if (isLast) {\n            if (gutterAlign === 'start') {\n                return 0\n            }\n            if (gutterAlign === 'center') {\n                return gutterSize / 2\n            }\n        }\n\n        return gutterSize\n    };\n\n    // Default options\n    var defaultGutterFn = function (i, gutterDirection) {\n        var gut = document.createElement('div');\n        gut.className = \"gutter gutter-\" + gutterDirection;\n        return gut\n    };\n\n    var defaultElementStyleFn = function (dim, size, gutSize) {\n        var style = {};\n\n        if (!isString(size)) {\n            style[dim] = calc + \"(\" + size + \"% - \" + gutSize + \"px)\";\n        } else {\n            style[dim] = size;\n        }\n\n        return style\n    };\n\n    var defaultGutterStyleFn = function (dim, gutSize) {\n        var obj;\n\n        return (( obj = {}, obj[dim] = (gutSize + \"px\"), obj ));\n    };\n\n    // The main function to initialize a split. Split.js thinks about each pair\n    // of elements as an independant pair. Dragging the gutter between two elements\n    // only changes the dimensions of elements in that pair. This is key to understanding\n    // how the following functions operate, since each function is bound to a pair.\n    //\n    // A pair object is shaped like this:\n    //\n    // {\n    //     a: DOM element,\n    //     b: DOM element,\n    //     aMin: Number,\n    //     bMin: Number,\n    //     dragging: Boolean,\n    //     parent: DOM element,\n    //     direction: 'horizontal' | 'vertical'\n    // }\n    //\n    // The basic sequence:\n    //\n    // 1. Set defaults to something sane. `options` doesn't have to be passed at all.\n    // 2. Initialize a bunch of strings based on the direction we're splitting.\n    //    A lot of the behavior in the rest of the library is paramatized down to\n    //    rely on CSS strings and classes.\n    // 3. Define the dragging helper functions, and a few helpers to go with them.\n    // 4. Loop through the elements while pairing them off. Every pair gets an\n    //    `pair` object and a gutter.\n    // 5. Actually size the pair elements, insert gutters and attach event listeners.\n    var Split = function (idsOption, options) {\n        if ( options === void 0 ) options = {};\n\n        if (ssr) { return {} }\n\n        var ids = idsOption;\n        var dimension;\n        var clientAxis;\n        var position;\n        var positionEnd;\n        var clientSize;\n        var elements;\n\n        // Allow HTMLCollection to be used as an argument when supported\n        if (Array.from) {\n            ids = Array.from(ids);\n        }\n\n        // All DOM elements in the split should have a common parent. We can grab\n        // the first elements parent and hope users read the docs because the\n        // behavior will be whacky otherwise.\n        var firstElement = elementOrSelector(ids[0]);\n        var parent = firstElement.parentNode;\n        var parentStyle = getComputedStyle ? getComputedStyle(parent) : null;\n        var parentFlexDirection = parentStyle ? parentStyle.flexDirection : null;\n\n        // Set default options.sizes to equal percentages of the parent element.\n        var sizes = getOption(options, 'sizes') || ids.map(function () { return 100 / ids.length; });\n\n        // Standardize minSize and maxSize to an array if it isn't already.\n        // This allows minSize and maxSize to be passed as a number.\n        var minSize = getOption(options, 'minSize', 100);\n        var minSizes = Array.isArray(minSize) ? minSize : ids.map(function () { return minSize; });\n        var maxSize = getOption(options, 'maxSize', Infinity);\n        var maxSizes = Array.isArray(maxSize) ? maxSize : ids.map(function () { return maxSize; });\n\n        // Get other options\n        var expandToMin = getOption(options, 'expandToMin', false);\n        var gutterSize = getOption(options, 'gutterSize', 10);\n        var gutterAlign = getOption(options, 'gutterAlign', 'center');\n        var snapOffset = getOption(options, 'snapOffset', 30);\n        var snapOffsets = Array.isArray(snapOffset) ? snapOffset : ids.map(function () { return snapOffset; });\n        var dragInterval = getOption(options, 'dragInterval', 1);\n        var direction = getOption(options, 'direction', HORIZONTAL);\n        var cursor = getOption(\n            options,\n            'cursor',\n            direction === HORIZONTAL ? 'col-resize' : 'row-resize'\n        );\n        var gutter = getOption(options, 'gutter', defaultGutterFn);\n        var elementStyle = getOption(\n            options,\n            'elementStyle',\n            defaultElementStyleFn\n        );\n        var gutterStyle = getOption(options, 'gutterStyle', defaultGutterStyleFn);\n\n        // 2. Initialize a bunch of strings based on the direction we're splitting.\n        // A lot of the behavior in the rest of the library is paramatized down to\n        // rely on CSS strings and classes.\n        if (direction === HORIZONTAL) {\n            dimension = 'width';\n            clientAxis = 'clientX';\n            position = 'left';\n            positionEnd = 'right';\n            clientSize = 'clientWidth';\n        } else if (direction === 'vertical') {\n            dimension = 'height';\n            clientAxis = 'clientY';\n            position = 'top';\n            positionEnd = 'bottom';\n            clientSize = 'clientHeight';\n        }\n\n        // 3. Define the dragging helper functions, and a few helpers to go with them.\n        // Each helper is bound to a pair object that contains its metadata. This\n        // also makes it easy to store references to listeners that that will be\n        // added and removed.\n        //\n        // Even though there are no other functions contained in them, aliasing\n        // this to self saves 50 bytes or so since it's used so frequently.\n        //\n        // The pair object saves metadata like dragging state, position and\n        // event listener references.\n\n        function setElementSize(el, size, gutSize, i) {\n            // Split.js allows setting sizes via numbers (ideally), or if you must,\n            // by string, like '300px'. This is less than ideal, because it breaks\n            // the fluid layout that `calc(% - px)` provides. You're on your own if you do that,\n            // make sure you calculate the gutter size by hand.\n            var style = elementStyle(dimension, size, gutSize, i);\n\n            Object.keys(style).forEach(function (prop) {\n                // eslint-disable-next-line no-param-reassign\n                el.style[prop] = style[prop];\n            });\n        }\n\n        function setGutterSize(gutterElement, gutSize, i) {\n            var style = gutterStyle(dimension, gutSize, i);\n\n            Object.keys(style).forEach(function (prop) {\n                // eslint-disable-next-line no-param-reassign\n                gutterElement.style[prop] = style[prop];\n            });\n        }\n\n        function getSizes() {\n            return elements.map(function (element) { return element.size; })\n        }\n\n        // Supports touch events, but not multitouch, so only the first\n        // finger `touches[0]` is counted.\n        function getMousePosition(e) {\n            if ('touches' in e) { return e.touches[0][clientAxis] }\n            return e[clientAxis]\n        }\n\n        // Actually adjust the size of elements `a` and `b` to `offset` while dragging.\n        // calc is used to allow calc(percentage + gutterpx) on the whole split instance,\n        // which allows the viewport to be resized without additional logic.\n        // Element a's size is the same as offset. b's size is total size - a size.\n        // Both sizes are calculated from the initial parent percentage,\n        // then the gutter size is subtracted.\n        function adjust(offset) {\n            var a = elements[this.a];\n            var b = elements[this.b];\n            var percentage = a.size + b.size;\n\n            a.size = (offset / this.size) * percentage;\n            b.size = percentage - (offset / this.size) * percentage;\n\n            setElementSize(a.element, a.size, this[aGutterSize], a.i);\n            setElementSize(b.element, b.size, this[bGutterSize], b.i);\n        }\n\n        // drag, where all the magic happens. The logic is really quite simple:\n        //\n        // 1. Ignore if the pair is not dragging.\n        // 2. Get the offset of the event.\n        // 3. Snap offset to min if within snappable range (within min + snapOffset).\n        // 4. Actually adjust each element in the pair to offset.\n        //\n        // ---------------------------------------------------------------------\n        // |    | <- a.minSize               ||              b.minSize -> |    |\n        // |    |  | <- this.snapOffset      ||     this.snapOffset -> |  |    |\n        // |    |  |                         ||                        |  |    |\n        // |    |  |                         ||                        |  |    |\n        // ---------------------------------------------------------------------\n        // | <- this.start                                        this.size -> |\n        function drag(e) {\n            var offset;\n            var a = elements[this.a];\n            var b = elements[this.b];\n\n            if (!this.dragging) { return }\n\n            // Get the offset of the event from the first side of the\n            // pair `this.start`. Then offset by the initial position of the\n            // mouse compared to the gutter size.\n            offset =\n                getMousePosition(e) -\n                this.start +\n                (this[aGutterSize] - this.dragOffset);\n\n            if (dragInterval > 1) {\n                offset = Math.round(offset / dragInterval) * dragInterval;\n            }\n\n            // If within snapOffset of min or max, set offset to min or max.\n            // snapOffset buffers a.minSize and b.minSize, so logic is opposite for both.\n            // Include the appropriate gutter sizes to prevent overflows.\n            if (offset <= a.minSize + a.snapOffset + this[aGutterSize]) {\n                offset = a.minSize + this[aGutterSize];\n            } else if (\n                offset >=\n                this.size - (b.minSize + b.snapOffset + this[bGutterSize])\n            ) {\n                offset = this.size - (b.minSize + this[bGutterSize]);\n            }\n\n            if (offset >= a.maxSize - a.snapOffset + this[aGutterSize]) {\n                offset = a.maxSize + this[aGutterSize];\n            } else if (\n                offset <=\n                this.size - (b.maxSize - b.snapOffset + this[bGutterSize])\n            ) {\n                offset = this.size - (b.maxSize + this[bGutterSize]);\n            }\n\n            // Actually adjust the size.\n            adjust.call(this, offset);\n\n            // Call the drag callback continously. Don't do anything too intensive\n            // in this callback.\n            getOption(options, 'onDrag', NOOP)(getSizes());\n        }\n\n        // Cache some important sizes when drag starts, so we don't have to do that\n        // continously:\n        //\n        // `size`: The total size of the pair. First + second + first gutter + second gutter.\n        // `start`: The leading side of the first element.\n        //\n        // ------------------------------------------------\n        // |      aGutterSize -> |||                      |\n        // |                     |||                      |\n        // |                     |||                      |\n        // |                     ||| <- bGutterSize       |\n        // ------------------------------------------------\n        // | <- start                             size -> |\n        function calculateSizes() {\n            // Figure out the parent size minus padding.\n            var a = elements[this.a].element;\n            var b = elements[this.b].element;\n\n            var aBounds = a[getBoundingClientRect]();\n            var bBounds = b[getBoundingClientRect]();\n\n            this.size =\n                aBounds[dimension] +\n                bBounds[dimension] +\n                this[aGutterSize] +\n                this[bGutterSize];\n            this.start = aBounds[position];\n            this.end = aBounds[positionEnd];\n        }\n\n        function innerSize(element) {\n            // Return nothing if getComputedStyle is not supported (< IE9)\n            // Or if parent element has no layout yet\n            if (!getComputedStyle) { return null }\n\n            var computedStyle = getComputedStyle(element);\n\n            if (!computedStyle) { return null }\n\n            var size = element[clientSize];\n\n            if (size === 0) { return null }\n\n            if (direction === HORIZONTAL) {\n                size -=\n                    parseFloat(computedStyle.paddingLeft) +\n                    parseFloat(computedStyle.paddingRight);\n            } else {\n                size -=\n                    parseFloat(computedStyle.paddingTop) +\n                    parseFloat(computedStyle.paddingBottom);\n            }\n\n            return size\n        }\n\n        // When specifying percentage sizes that are less than the computed\n        // size of the element minus the gutter, the lesser percentages must be increased\n        // (and decreased from the other elements) to make space for the pixels\n        // subtracted by the gutters.\n        function trimToMin(sizesToTrim) {\n            // Try to get inner size of parent element.\n            // If it's no supported, return original sizes.\n            var parentSize = innerSize(parent);\n            if (parentSize === null) {\n                return sizesToTrim\n            }\n\n            if (minSizes.reduce(function (a, b) { return a + b; }, 0) > parentSize) {\n                return sizesToTrim\n            }\n\n            // Keep track of the excess pixels, the amount of pixels over the desired percentage\n            // Also keep track of the elements with pixels to spare, to decrease after if needed\n            var excessPixels = 0;\n            var toSpare = [];\n\n            var pixelSizes = sizesToTrim.map(function (size, i) {\n                // Convert requested percentages to pixel sizes\n                var pixelSize = (parentSize * size) / 100;\n                var elementGutterSize = getGutterSize(\n                    gutterSize,\n                    i === 0,\n                    i === sizesToTrim.length - 1,\n                    gutterAlign\n                );\n                var elementMinSize = minSizes[i] + elementGutterSize;\n\n                // If element is too smal, increase excess pixels by the difference\n                // and mark that it has no pixels to spare\n                if (pixelSize < elementMinSize) {\n                    excessPixels += elementMinSize - pixelSize;\n                    toSpare.push(0);\n                    return elementMinSize\n                }\n\n                // Otherwise, mark the pixels it has to spare and return it's original size\n                toSpare.push(pixelSize - elementMinSize);\n                return pixelSize\n            });\n\n            // If nothing was adjusted, return the original sizes\n            if (excessPixels === 0) {\n                return sizesToTrim\n            }\n\n            return pixelSizes.map(function (pixelSize, i) {\n                var newPixelSize = pixelSize;\n\n                // While there's still pixels to take, and there's enough pixels to spare,\n                // take as many as possible up to the total excess pixels\n                if (excessPixels > 0 && toSpare[i] - excessPixels > 0) {\n                    var takenPixels = Math.min(\n                        excessPixels,\n                        toSpare[i] - excessPixels\n                    );\n\n                    // Subtract the amount taken for the next iteration\n                    excessPixels -= takenPixels;\n                    newPixelSize = pixelSize - takenPixels;\n                }\n\n                // Return the pixel size adjusted as a percentage\n                return (newPixelSize / parentSize) * 100\n            })\n        }\n\n        // stopDragging is very similar to startDragging in reverse.\n        function stopDragging() {\n            var self = this;\n            var a = elements[self.a].element;\n            var b = elements[self.b].element;\n\n            if (self.dragging) {\n                getOption(options, 'onDragEnd', NOOP)(getSizes());\n            }\n\n            self.dragging = false;\n\n            // Remove the stored event listeners. This is why we store them.\n            global[removeEventListener]('mouseup', self.stop);\n            global[removeEventListener]('touchend', self.stop);\n            global[removeEventListener]('touchcancel', self.stop);\n            global[removeEventListener]('mousemove', self.move);\n            global[removeEventListener]('touchmove', self.move);\n\n            // Clear bound function references\n            self.stop = null;\n            self.move = null;\n\n            a[removeEventListener]('selectstart', NOOP);\n            a[removeEventListener]('dragstart', NOOP);\n            b[removeEventListener]('selectstart', NOOP);\n            b[removeEventListener]('dragstart', NOOP);\n\n            a.style.userSelect = '';\n            a.style.webkitUserSelect = '';\n            a.style.MozUserSelect = '';\n            a.style.pointerEvents = '';\n\n            b.style.userSelect = '';\n            b.style.webkitUserSelect = '';\n            b.style.MozUserSelect = '';\n            b.style.pointerEvents = '';\n\n            self.gutter.style.cursor = '';\n            self.parent.style.cursor = '';\n            document.body.style.cursor = '';\n        }\n\n        // startDragging calls `calculateSizes` to store the inital size in the pair object.\n        // It also adds event listeners for mouse/touch events,\n        // and prevents selection while dragging so avoid the selecting text.\n        function startDragging(e) {\n            // Right-clicking can't start dragging.\n            if ('button' in e && e.button !== 0) {\n                return\n            }\n\n            // Alias frequently used variables to save space. 200 bytes.\n            var self = this;\n            var a = elements[self.a].element;\n            var b = elements[self.b].element;\n\n            // Call the onDragStart callback.\n            if (!self.dragging) {\n                getOption(options, 'onDragStart', NOOP)(getSizes());\n            }\n\n            // Don't actually drag the element. We emulate that in the drag function.\n            e.preventDefault();\n\n            // Set the dragging property of the pair object.\n            self.dragging = true;\n\n            // Create two event listeners bound to the same pair object and store\n            // them in the pair object.\n            self.move = drag.bind(self);\n            self.stop = stopDragging.bind(self);\n\n            // All the binding. `window` gets the stop events in case we drag out of the elements.\n            global[addEventListener]('mouseup', self.stop);\n            global[addEventListener]('touchend', self.stop);\n            global[addEventListener]('touchcancel', self.stop);\n            global[addEventListener]('mousemove', self.move);\n            global[addEventListener]('touchmove', self.move);\n\n            // Disable selection. Disable!\n            a[addEventListener]('selectstart', NOOP);\n            a[addEventListener]('dragstart', NOOP);\n            b[addEventListener]('selectstart', NOOP);\n            b[addEventListener]('dragstart', NOOP);\n\n            a.style.userSelect = 'none';\n            a.style.webkitUserSelect = 'none';\n            a.style.MozUserSelect = 'none';\n            a.style.pointerEvents = 'none';\n\n            b.style.userSelect = 'none';\n            b.style.webkitUserSelect = 'none';\n            b.style.MozUserSelect = 'none';\n            b.style.pointerEvents = 'none';\n\n            // Set the cursor at multiple levels\n            self.gutter.style.cursor = cursor;\n            self.parent.style.cursor = cursor;\n            document.body.style.cursor = cursor;\n\n            // Cache the initial sizes of the pair.\n            calculateSizes.call(self);\n\n            // Determine the position of the mouse compared to the gutter\n            self.dragOffset = getMousePosition(e) - self.end;\n        }\n\n        // adjust sizes to ensure percentage is within min size and gutter.\n        sizes = trimToMin(sizes);\n\n        // 5. Create pair and element objects. Each pair has an index reference to\n        // elements `a` and `b` of the pair (first and second elements).\n        // Loop through the elements while pairing them off. Every pair gets a\n        // `pair` object and a gutter.\n        //\n        // Basic logic:\n        //\n        // - Starting with the second element `i > 0`, create `pair` objects with\n        //   `a = i - 1` and `b = i`\n        // - Set gutter sizes based on the _pair_ being first/last. The first and last\n        //   pair have gutterSize / 2, since they only have one half gutter, and not two.\n        // - Create gutter elements and add event listeners.\n        // - Set the size of the elements, minus the gutter sizes.\n        //\n        // -----------------------------------------------------------------------\n        // |     i=0     |         i=1         |        i=2       |      i=3     |\n        // |             |                     |                  |              |\n        // |           pair 0                pair 1             pair 2           |\n        // |             |                     |                  |              |\n        // -----------------------------------------------------------------------\n        var pairs = [];\n        elements = ids.map(function (id, i) {\n            // Create the element object.\n            var element = {\n                element: elementOrSelector(id),\n                size: sizes[i],\n                minSize: minSizes[i],\n                maxSize: maxSizes[i],\n                snapOffset: snapOffsets[i],\n                i: i,\n            };\n\n            var pair;\n\n            if (i > 0) {\n                // Create the pair object with its metadata.\n                pair = {\n                    a: i - 1,\n                    b: i,\n                    dragging: false,\n                    direction: direction,\n                    parent: parent,\n                };\n\n                pair[aGutterSize] = getGutterSize(\n                    gutterSize,\n                    i - 1 === 0,\n                    false,\n                    gutterAlign\n                );\n                pair[bGutterSize] = getGutterSize(\n                    gutterSize,\n                    false,\n                    i === ids.length - 1,\n                    gutterAlign\n                );\n\n                // if the parent has a reverse flex-direction, switch the pair elements.\n                if (\n                    parentFlexDirection === 'row-reverse' ||\n                    parentFlexDirection === 'column-reverse'\n                ) {\n                    var temp = pair.a;\n                    pair.a = pair.b;\n                    pair.b = temp;\n                }\n            }\n\n            // Determine the size of the current element. IE8 is supported by\n            // staticly assigning sizes without draggable gutters. Assigns a string\n            // to `size`.\n            //\n            // Create gutter elements for each pair.\n            if (i > 0) {\n                var gutterElement = gutter(i, direction, element.element);\n                setGutterSize(gutterElement, gutterSize, i);\n\n                // Save bound event listener for removal later\n                pair[gutterStartDragging] = startDragging.bind(pair);\n\n                // Attach bound event listener\n                gutterElement[addEventListener](\n                    'mousedown',\n                    pair[gutterStartDragging]\n                );\n                gutterElement[addEventListener](\n                    'touchstart',\n                    pair[gutterStartDragging]\n                );\n\n                parent.insertBefore(gutterElement, element.element);\n\n                pair.gutter = gutterElement;\n            }\n\n            setElementSize(\n                element.element,\n                element.size,\n                getGutterSize(\n                    gutterSize,\n                    i === 0,\n                    i === ids.length - 1,\n                    gutterAlign\n                ),\n                i\n            );\n\n            // After the first iteration, and we have a pair object, append it to the\n            // list of pairs.\n            if (i > 0) {\n                pairs.push(pair);\n            }\n\n            return element\n        });\n\n        function adjustToMin(element) {\n            var isLast = element.i === pairs.length;\n            var pair = isLast ? pairs[element.i - 1] : pairs[element.i];\n\n            calculateSizes.call(pair);\n\n            var size = isLast\n                ? pair.size - element.minSize - pair[bGutterSize]\n                : element.minSize + pair[aGutterSize];\n\n            adjust.call(pair, size);\n        }\n\n        elements.forEach(function (element) {\n            var computedSize = element.element[getBoundingClientRect]()[dimension];\n\n            if (computedSize < element.minSize) {\n                if (expandToMin) {\n                    adjustToMin(element);\n                } else {\n                    // eslint-disable-next-line no-param-reassign\n                    element.minSize = computedSize;\n                }\n            }\n        });\n\n        function setSizes(newSizes) {\n            var trimmed = trimToMin(newSizes);\n            trimmed.forEach(function (newSize, i) {\n                if (i > 0) {\n                    var pair = pairs[i - 1];\n\n                    var a = elements[pair.a];\n                    var b = elements[pair.b];\n\n                    a.size = trimmed[i - 1];\n                    b.size = newSize;\n\n                    setElementSize(a.element, a.size, pair[aGutterSize], a.i);\n                    setElementSize(b.element, b.size, pair[bGutterSize], b.i);\n                }\n            });\n        }\n\n        function destroy(preserveStyles, preserveGutter) {\n            pairs.forEach(function (pair) {\n                if (preserveGutter !== true) {\n                    pair.parent.removeChild(pair.gutter);\n                } else {\n                    pair.gutter[removeEventListener](\n                        'mousedown',\n                        pair[gutterStartDragging]\n                    );\n                    pair.gutter[removeEventListener](\n                        'touchstart',\n                        pair[gutterStartDragging]\n                    );\n                }\n\n                if (preserveStyles !== true) {\n                    var style = elementStyle(\n                        dimension,\n                        pair.a.size,\n                        pair[aGutterSize]\n                    );\n\n                    Object.keys(style).forEach(function (prop) {\n                        elements[pair.a].element.style[prop] = '';\n                        elements[pair.b].element.style[prop] = '';\n                    });\n                }\n            });\n        }\n\n        return {\n            setSizes: setSizes,\n            getSizes: getSizes,\n            collapse: function collapse(i) {\n                adjustToMin(elements[i]);\n            },\n            destroy: destroy,\n            parent: parent,\n            pairs: pairs,\n        }\n    };\n\n    return Split;\n\n})));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/split.js@1.6.5/node_modules/split.js/dist/split.js\n");

/***/ })

};
;