"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoThongTinCCDC (Tool Information Declaration) service implementation.
"""

from typing import Any, Dict, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc.khai_bao_thong_tin_ccdc import (  # noqa: F401,
    KhaiBaoThongTinCCDCModel,
)
from django_ledger.repositories.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc import (  # noqa: F401,
    KhaiBaoThongTinCCDCRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,
from django_ledger.services.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc.chi_tiet_doi_tuong_hach_toan_ccdc import (  # noqa: F401,
    ChiTietDoiTuongHachToanCCDCService,
)
from django_ledger.services.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc.chi_tiet_phu_tung_kem_theo_ccdc import (  # noqa: F401,
    ChiTietPhuTungKemTheoCCDCService,
)


class KhaiBaoThongTinCCDCService(BaseService):
    """
    Service class for KhaiBaoThongTinCCDCModel.
    Handles business logic for the model.
    """

    def __init__(self):  # noqa: C901
        self.repository = KhaiBaoThongTinCCDCRepository()
        self.chi_tiet_doi_tuong_service = ChiTietDoiTuongHachToanCCDCService()
        self.chi_tiet_phu_tung_service = ChiTietPhuTungKemTheoCCDCService()
        self._khai_bao_thoi_phan_bo_service = None
        super().__init__()

    @property
    def khai_bao_thoi_phan_bo_service(self):
        if self._khai_bao_thoi_phan_bo_service is None:
            from django_ledger.services.cong_cu.dung_phan_bo_ccdc.khai_bao_thoi_phan_bo_ccdc import (
                KhaiBaoThoiPhanBoCCDCService,
            )
            self._khai_bao_thoi_phan_bo_service = KhaiBaoThoiPhanBoCCDCService()
        return self._khai_bao_thoi_phan_bo_service

    def get_by_id(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> Optional[KhaiBaoThongTinCCDCModel]:  # noqa: C901
        """
        Retrieves a KhaiBaoThongTinCCDCModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the KhaiBaoThongTinCCDCModel to retrieve.

        Returns
        -------
        Optional[KhaiBaoThongTinCCDCModel]
            The KhaiBaoThongTinCCDCModel with the given UUID, or None if not found.
        """
        return self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

    def list_for_entity(self, entity_slug: str) -> QuerySet:  # noqa: C901
        """
        Returns KhaiBaoThongTinCCDCModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.

        Returns
        -------
        QuerySet
            QuerySet of KhaiBaoThongTinCCDCModel instances for the specified entity.
        """
        return self.repository.get_queryset(entity_slug=entity_slug)

    def list(self, entity_slug: str, **kwargs) -> QuerySet:  # noqa: C901
        """
        Returns KhaiBaoThongTinCCDCModel instances for a specific entity with filters.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters:
            - uuid: CCDC UUID (optional)
            - ma_lcc: CCDC type UUID (optional)
            - ma_bp: Department UUID (optional)
            - nh_cc1, nh_cc2, nh_cc3: Group UUIDs (optional)

        Returns
        -------
        QuerySet
            QuerySet of KhaiBaoThongTinCCDCModel instances for the specified entity with filters applied.
        """
        # Get the base queryset for the entity
        queryset = self.repository.get_queryset(entity_slug=entity_slug)

        # Add additional filters if provided
        if kwargs:
            queryset = queryset.filter(**kwargs)

        return queryset



    @transaction.atomic
    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> KhaiBaoThongTinCCDCModel:  # noqa: C901
        """
        Creates a new KhaiBaoThongTinCCDCModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new KhaiBaoThongTinCCDCModel.

        Returns
        -------
        KhaiBaoThongTinCCDCModel
            The created KhaiBaoThongTinCCDCModel instance.
        """
        return self.create_with_details(entity_slug=entity_slug, data=data)

    @transaction.atomic
    def update(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[KhaiBaoThongTinCCDCModel]:  # noqa: C901
        """
        Updates an existing KhaiBaoThongTinCCDCModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the KhaiBaoThongTinCCDCModel to update.
        data : Dict[str, Any]
            The data for updating the KhaiBaoThongTinCCDCModel.

        Returns
        -------
        Optional[KhaiBaoThongTinCCDCModel]
            The updated KhaiBaoThongTinCCDCModel instance, or None if not found.
        """
        return self.update_with_details(entity_slug=entity_slug, uuid=uuid, data=data)

    @transaction.atomic
    def create_with_details(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> KhaiBaoThongTinCCDCModel:
        """
        Creates a new KhaiBaoThongTinCCDCModel instance with its details.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new KhaiBaoThongTinCCDCModel.

        Returns
        -------
        KhaiBaoThongTinCCDCModel
            The created KhaiBaoThongTinCCDCModel instance.
        """
        chi_tiet_doi_tuong_data = data.pop(
            'chi_tiet_doi_tuong_hach_toan_ccdc', []
        )
        chi_tiet_phu_tung_data = data.pop(
            'chi_tiet_phu_tung_kem_theo_ccdc', []
        )
        instance = self.repository.create(entity_slug=entity_slug, data=data)

        if chi_tiet_doi_tuong_data:
            self.chi_tiet_doi_tuong_service.bulk_create(
                parent=instance,
                data=chi_tiet_doi_tuong_data,
            )

        if chi_tiet_phu_tung_data:
            self.chi_tiet_phu_tung_service.bulk_create(
                parent=instance,
                data=chi_tiet_phu_tung_data,
            )

        try:
            khai_bao_thoi_khau_hao_data = {
                'ma_cc': instance.uuid,
            }
            self.khai_bao_thoi_phan_bo_service.create(
                entity_slug=entity_slug,
                data=khai_bao_thoi_khau_hao_data
            )
        except Exception as e:
            raise e

        instance.refresh_from_db()
        return instance

    @transaction.atomic
    def update_with_details(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[KhaiBaoThongTinCCDCModel]:
        """
        Updates an existing KhaiBaoThongTinCCDCModel instance with its details.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the KhaiBaoThongTinCCDCModel to update.
        data : Dict[str, Any]
            The data for updating the KhaiBaoThongTinCCDCModel.

        Returns
        -------
        Optional[KhaiBaoThongTinCCDCModel]
            The updated KhaiBaoThongTinCCDCModel instance, or None if not found.
        """
        chi_tiet_doi_tuong_data = data.pop(
            'chi_tiet_doi_tuong_hach_toan_ccdc', None
        )
        chi_tiet_phu_tung_data = data.pop(
            'chi_tiet_phu_tung_kem_theo_ccdc', None
        )
        instance = self.repository.update(
            entity_slug=entity_slug, uuid=uuid, data=data
        )
        if instance:
            if chi_tiet_doi_tuong_data is not None:
                self.chi_tiet_doi_tuong_service.bulk_update(
                    parent=instance,
                    data=chi_tiet_doi_tuong_data,
                )

            if chi_tiet_phu_tung_data is not None:
                self.chi_tiet_phu_tung_service.bulk_update(
                    parent=instance,
                    data=chi_tiet_phu_tung_data,
                )

        instance.refresh_from_db()
        return instance

    @transaction.atomic
    def delete(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> bool:  # noqa: C901
        """
        Deletes an existing KhaiBaoThongTinCCDCModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the KhaiBaoThongTinCCDCModel to delete.

        Returns
        -------
        bool
            True if the instance was deleted, False otherwise.
        """
        # First, delete all related chi_tiet
        existing_chi_tiet_doi_tuong = (
            self.chi_tiet_doi_tuong_service.list_for_khai_bao_thong_tin_ccdc(
                khai_bao_thong_tin_ccdc_id=uuid
            )
        )
        for chi_tiet in existing_chi_tiet_doi_tuong:
            self.chi_tiet_doi_tuong_service.delete(uuid=chi_tiet.uuid)
        existing_chi_tiet_phu_tung = (
            self.chi_tiet_phu_tung_service.list_for_khai_bao_thong_tin_ccdc(
                khai_bao_thong_tin_ccdc_id=uuid
            )
        )
        for chi_tiet in existing_chi_tiet_phu_tung:
            self.chi_tiet_phu_tung_service.delete(uuid=chi_tiet.uuid)
        # Then delete the main instance
        return self.repository.delete(entity_slug=entity_slug, uuid=uuid)
