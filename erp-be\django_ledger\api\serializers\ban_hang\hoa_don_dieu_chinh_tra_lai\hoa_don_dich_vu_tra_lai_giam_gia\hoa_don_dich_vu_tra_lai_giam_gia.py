"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for HoaDonDichVuTraLaiGiamGia (Service Invoice Return/Discount) model.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers._utils.chung_tu_fields import (
    ChungTuSerializerMixin,
    get_chung_tu_field_names,
)
from django_ledger.api.serializers.customer import CustomerModelSerializer  # noqa: F401
from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dich_vu_tra_lai_giam_gia import (
    HoaDonDichVuTraLaiGiamGiaModel,
)


class HoaDonDichVuTraLaiGiamGiaModelSerializer(
    ChungTuSerializerMixin, serializers.ModelSerializer
):
    """
    Serializer for HoaDonDichVuTraLaiGiamGiaModel.
    """

    # Use proper serializer for chi_tiet to include full details in GET requests
    chi_tiet = serializers.SerializerMethodField()
    # Reference data fields
    ma_kh_data = serializers.SerializerMethodField()

    tk_data = serializers.SerializerMethodField()

    ma_mau_ct_data = serializers.SerializerMethodField()

    ma_nt_data = serializers.SerializerMethodField()

    unit_id_data = serializers.SerializerMethodField()

    class Meta:
        model = HoaDonDichVuTraLaiGiamGiaModel
        fields = [
            'uuid',
            # ChungTu fields are automatically added by ChungTuSerializerMixin
            # Main business fields
            'ma_ngv',
            'ma_kh',
            'ma_kh_data',
            'ma_so_thue',
            'ten_kh_thue',
            'ong_ba',
            'dia_chi',
            'e_mail',
            'tk',
            'tk_data',
            'ma_mau_ct',
            'ma_mau_ct_data',  # Add missing ma_mau_ct_data field
            'dien_giai',
            'unit_id',
            'unit_id_data',
            'so_ct0',
            'so_ct2',
            'so_ct3',
            'ngay_ct0',
            'ma_nt',
            'ma_nt_data',
            'ty_gia',
            'status',
            'transfer_yn',
            'ten_vt_thue',
            'ma_kh9',
            'ma_mau_bc',
            'ma_tc_thue',
            'invat_yn',
            'ma_tthddt',
            'ma_pttt',
            'so_ct_hddt',
            'ngay_ct_hddt',
            'so_ct2_hddt',
            'ma_mau_ct_hddt',
            'ma_tt',
            'ly_do_huy',
            'ly_do',
            't_tien_nt2',
            't_tien2',
            't_thue_nt',
            't_thue',
            't_tt_nt',
            't_tt',
            'created',
            'updated',
            'chi_tiet',
        ] + get_chung_tu_field_names()
        read_only_fields = [
            'uuid',
            'entity_model',
            'created',
            'updated',
            'chi_tiet',
            'ma_kh_data',
            'ma_kh_data',
            'ma_nt_data',
            'unit_id_data',
            'chi_tiet_data',
        ]

    def get_ma_kh_data(self, obj):  # noqa: C901
        """Get customer data."""
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_ma_nt_data(self, obj):  # noqa: C901
        """Get currency data."""
        if obj.ma_nt:
            from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer

            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_unit_id_data(self, obj):  # noqa: C901
        """Get unit data."""
        if obj.unit_id:
            from django_ledger.api.serializers.unit import (
                EntityUnitModelSimpleSerializer,
            )

            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None

    def get_chi_tiet(self, obj):  # noqa: C901
        """Get detail items (excluding soft deleted)."""
        from django_ledger.api.serializers.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dich_vu_tra_lai_giam_gia.chi_tiet_hoa_don_dich_vu_tra_lai_giam_gia import (
            ChiTietHoaDonDichVuTraLaiGiamGiaModelSerializer,
        )

        # SafeDelete manager automatically excludes soft deleted items
        chi_tiet_qs = obj.chi_tiet.filter(deleted__isnull=True)
        return ChiTietHoaDonDichVuTraLaiGiamGiaModelSerializer(
            chi_tiet_qs, many=True
        ).data

    def get_tk_data(self, obj):  # noqa: C901
        """Get account data."""
        if obj.tk:
            from django_ledger.api.serializers.accounts import AccountModelSerializer

            return AccountModelSerializer(obj.tk).data
        return None

    def get_ma_mau_ct_data(self, obj):  # noqa: C901
        """
        Get ma_mau_ct data.
        """
        if obj.ma_mau_ct:
            from django_ledger.api.serializers.mau_so_hd import MauSoHDModelSerializer

            return MauSoHDModelSerializer(obj.ma_mau_ct).data
        return None


class HoaDonDichVuTraLaiGiamGiaModelCreateUpdateSerializer(
    ChungTuSerializerMixin, serializers.ModelSerializer
):
    """
    Serializer for creating and updating HoaDonDichVuTraLaiGiamGia model.
    """

    chi_tiet = serializers.ListField(required=False, write_only=True)

    chi_tiet_items = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = HoaDonDichVuTraLaiGiamGiaModel
        fields = [
            'uuid',
            'entity_model',
            # ChungTu fields - explicitly included
            # 'i_so_ct',
            # 'ma_nk',
            # 'so_ct',
            # 'ngay_ct',
            # 'ngay_lct',
            # 'chung_tu',
            # Main business fields
            'ma_ngv',
            'ma_kh',
            'ma_so_thue',
            'ten_kh_thue',
            'ong_ba',
            'dia_chi',
            'e_mail',
            'tk',
            'ma_mau_ct',
            'dien_giai',
            'unit_id',
            'so_ct0',
            'so_ct2',
            'so_ct3',
            'ngay_ct0',
            'ma_nt',
            'ty_gia',
            'status',
            'transfer_yn',
            'ten_vt_thue',
            'ma_kh9',
            'ma_mau_bc',
            'ma_tc_thue',
            'invat_yn',
            'ma_tthddt',
            'ma_pttt',
            'so_ct_hddt',
            'ngay_ct_hddt',
            'so_ct2_hddt',
            'ma_mau_ct_hddt',
            'ma_tt',
            'ly_do_huy',
            'ly_do',
            't_tien_nt2',
            't_tien2',
            't_thue_nt',
            't_thue',
            't_tt_nt',
            't_tt',
            'created',
            'updated',
            'chi_tiet',
            'chi_tiet_items',
        ] + get_chung_tu_field_names()
        read_only_fields = ['uuid', 'created', 'updated', 'entity_model']

    def validate(self, data):  # noqa: C901
        """
        Validate the data before creating or updating the model instance.
        """
        # Use ChungTu mixin for validation
        data = self.validate_chung_tu_fields(data)

        # Auto-populate entity_model from context if not provided
        if not data.get('entity_model') and 'entity_slug' in self.context:
            from django_ledger.models.entity import EntityModel

            try:
                entity_model = EntityModel.objects.get(slug=self.context['entity_slug'])
                data['entity_model'] = entity_model
            except EntityModel.DoesNotExist:
                pass

        return data

    def to_internal_value(self, data):
        """
        Override to handle chi_tiet input for CREATE/UPDATE operations and allow empty string for ngay_ct_hddt.
        """
        # Handle empty string for ngay_ct_hddt
        if isinstance(data, dict) and data.get('ngay_ct_hddt', None) == '':
            data['ngay_ct_hddt'] = None

        # Handle both chi_tiet and chi_tiet_items field names
        chi_tiet_data = []
        if isinstance(data, dict):
            chi_tiet_data = data.pop('chi_tiet', []) or data.pop('chi_tiet_items', [])

        validated_data = super().to_internal_value(data)
        validated_data['chi_tiet'] = chi_tiet_data

        return validated_data
