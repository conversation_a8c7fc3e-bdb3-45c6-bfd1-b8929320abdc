"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiTietPhieuNhapDieuChinhGiaHangMuaModel, which represents the detail lines  # noqa: E501
of the purchase price adjustment receipt in the system.
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ChiTietPhieuNhapDieuChinhGiaHangMuaModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the ChiTietPhieuNhapDieuChinhGiaHangMuaModel.
    """

    def for_phieu_nhap(self, phieu_nhap_uuid):  # noqa: C901
        """
        Returns details for a specific purchase price adjustment receipt.

        Parameters
        ----------
        phieu_nhap_uuid: UUID
            The PhieuNhapDieuChinhGiaHangMuaModel UUID used for filtering the QuerySet.

        Returns
        -------
        ChiTietPhieuNhapDieuChinhGiaHangMuaModelQueryset
            A QuerySet of ChiTietPhieuNhapDieuChinhGiaHangMuaModel with applied filters.
        """
        return self.filter(phieu_nhap__uuid=phieu_nhap_uuid)


class ChiTietPhieuNhapDieuChinhGiaHangMuaModelManager(Manager):
    """
    A custom defined ChiTietPhieuNhapDieuChinhGiaHangMuaModel Manager that will act as an interface to handle the  # noqa: E501
    ChiTietPhieuNhapDieuChinhGiaHangMuaModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom ChiTietPhieuNhapDieuChinhGiaHangMuaModelQueryset.
        """
        return ChiTietPhieuNhapDieuChinhGiaHangMuaModelQueryset(
            self.model, using=self._db
        )

    def for_phieu_nhap(self, phieu_nhap_uuid):  # noqa: C901
        """
        Returns details for a specific purchase price adjustment receipt.

        Parameters
        ----------
        phieu_nhap_uuid: UUID
            The PhieuNhapDieuChinhGiaHangMuaModel UUID used for filtering the QuerySet.

        Returns
        -------
        ChiTietPhieuNhapDieuChinhGiaHangMuaModelQueryset
            A QuerySet of ChiTietPhieuNhapDieuChinhGiaHangMuaModel with applied filters.
        """
        return self.get_queryset().for_phieu_nhap(phieu_nhap_uuid=phieu_nhap_uuid)


class ChiTietPhieuNhapDieuChinhGiaHangMuaModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiTietPhieuNhapDieuChinhGiaHangMuaModel database will inherit from.  # noqa: E501
    The ChiTietPhieuNhapDieuChinhGiaHangMuaModel inherits functionality from the following MixIns:  # noqa: E501

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    phieu_nhap : ForeignKey
        Reference to the main purchase price adjustment receipt.
    ma_vt : ForeignKey
        Material code (required).
    dvt : ForeignKey
        Unit of measure (required).
    ma_kho : ForeignKey
        Warehouse code (required).
    ma_lo : ForeignKey
        Lot code.
    ma_vi_tri : ForeignKey
        Location code.
    ma_thue : ForeignKey
        Tax code.
    tk_vt : ForeignKey
        Material account (required).
    tk_thue : ForeignKey
        Tax account.
    ma_bp : ForeignKey
        Department code.
    ma_vv : ForeignKey
        Case code.
    ma_hd : ForeignKey
        Contract code.
    ma_dtt : ForeignKey
        Payment batch code.
    ma_ku : ForeignKey
        Loan agreement code.
    ma_phi : ForeignKey
        Fee code.
    ma_sp : ForeignKey
        Product code.
    ma_lsx : ForeignKey
        Production order code.
    ma_cp0 : ForeignKey
        Invalid expense code.
    entity_model : ForeignKey
        The entity this detail record belongs to.
    line : IntegerField
        Line number.
    lo_yn : IntegerField
        Has lot flag.
    vi_tri_yn : IntegerField
        Has location flag.
    qc_yn : IntegerField
        Quality control flag.
    id_hd4 : IntegerField
        Contract ID 4.
    id_hd5 : IntegerField
        Contract ID 5.
    id_hd7 : IntegerField
        Contract ID 7.
    line_hd : IntegerField
        Contract line.
    he_so : DecimalField
        Conversion factor.
    so_luong : DecimalField
        Quantity.
    gia_nt : DecimalField
        Price in foreign currency.
    tien_nt : DecimalField
        Amount in foreign currency.
    thue_suat : DecimalField
        Tax rate.
    thue_nt : DecimalField
        Tax in foreign currency.
    thue : DecimalField
        Tax amount.
    gia : DecimalField
        Price.
    tien : DecimalField
        Amount.
    ten_tk_vt : CharField
        Material account name.
    ten_tk_thue : CharField
        Tax account name.
    """

    uuid = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    # Link to main table
    phieu_nhap = models.ForeignKey(
        'django_ledger.PhieuNhapDieuChinhGiaHangMuaModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet_list',
        verbose_name=_('Phiếu nhập điều chỉnh giá'),
        help_text=_('Reference to the main purchase price adjustment receipt'),
    )

    # Required foreign keys
    ma_vt = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Mã vật tư'),
        help_text=_('Material code (required)'),
    )

    dvt = models.ForeignKey(
        'django_ledger.DonViTinhModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Đơn vị tính'),
        help_text=_('Unit of measure (required)'),
    )

    ma_kho = models.ForeignKey(
        'django_ledger.KhoHangModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Mã kho'),
        help_text=_('Warehouse code (required)'),
    )

    tk_vt = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet_phieu_nhap_dieu_chinh_gia_vt_list',
        verbose_name=_('Tài khoản vật tư'),
        help_text=_('Material account (required)'),
    )

    # Optional foreign keys
    ma_lo = models.ForeignKey(
        'django_ledger.LoModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã lô'),
        help_text=_('Lot code'),
    )

    ma_vi_tri = models.ForeignKey(
        'django_ledger.ViTriModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã vị trí'),
        help_text=_('Location code'),
    )

    ma_thue = models.ForeignKey(
        'django_ledger.TaxModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã thuế'),
        help_text=_('Tax code'),
    )

    tk_thue = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='chi_tiet_phieu_nhap_dieu_chinh_gia_thue_list',
        verbose_name=_('Tài khoản thuế'),
        help_text=_('Tax account'),
    )

    ma_bp = models.ForeignKey(
        'django_ledger.BoPhanModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã bộ phận'),
        help_text=_('Department code'),
    )

    ma_vv = models.ForeignKey(
        'django_ledger.VuViecModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã vụ việc'),
        help_text=_('Case code'),
    )

    ma_hd = models.ForeignKey(
        'django_ledger.ContractModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã hợp đồng'),
        help_text=_('Contract code'),
    )

    ma_dtt = models.ForeignKey(
        'django_ledger.DotThanhToanModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã đợt thanh toán'),
        help_text=_('Payment batch code'),
    )

    ma_ku = models.ForeignKey(
        'django_ledger.KheUocModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã khe ước'),
        help_text=_('Loan agreement code'),
    )

    ma_phi = models.ForeignKey(
        'django_ledger.PhiModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã phí'),
        help_text=_('Fee code'),
    )

    ma_sp = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='chi_tiet_phieu_nhap_dieu_chinh_gia_sp_list',
        verbose_name=_('Mã sản phẩm'),
        help_text=_('Product code'),
    )

    ma_lsx = models.CharField(
        max_length=50,
        verbose_name=_('Mã lệnh sản xuất'),
        help_text=_('Production order code'),
        null=True,
        blank=True,
    )

    ma_cp0 = models.ForeignKey(
        'django_ledger.ChiPhiKhongHopLeModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã chi phí không hợp lệ'),
        help_text=_('Invalid expense code'),
    )

    entity_model = models.ForeignKey(
        'django_ledger.EntityModel',
        on_delete=models.CASCADE,
        verbose_name=_('Entity'),
        help_text=_('The entity this detail record belongs to'),
    )

    # Integer fields
    line = models.IntegerField(verbose_name=_('Dòng'), help_text=_('Line number'))
    lo_yn = models.IntegerField(
        default=0,
        verbose_name=_('Có lô'),
        help_text=_('Has lot flag (0/1)'),
    )

    vi_tri_yn = models.IntegerField(
        default=0,
        verbose_name=_('Có vị trí'),
        help_text=_('Has location flag (0/1)'),
    )

    qc_yn = models.IntegerField(
        default=0,
        verbose_name=_('Quy cách'),
        help_text=_('Quality control flag (0/1)'),
    )

    id_hd4 = models.IntegerField(
        default=0,
        verbose_name=_('ID hợp đồng 4'),
        help_text=_('Contract ID 4'),
    )

    id_hd5 = models.IntegerField(
        default=0,
        verbose_name=_('ID hợp đồng 5'),
        help_text=_('Contract ID 5'),
    )

    id_hd7 = models.IntegerField(
        default=0,
        verbose_name=_('ID hợp đồng 7'),
        help_text=_('Contract ID 7'),
    )

    # Links to invoice and invoice detail by UUID stored as char
    id_hd = models.CharField(
        max_length=36,
        verbose_name=_("ID hóa đơn"),
        help_text=_("UUID of HoaDonMuaHangTrongNuocModel"),
        blank=True,
        null=True,
    )
    line_hd = models.CharField(
        max_length=36,
        verbose_name=_("Line hóa đơn"),
        help_text=_("UUID of ChiTietHoaDonMuaHangTrongNuocModel"),
        blank=True,
        null=True,
    )

    # Decimal fields
    he_so = models.DecimalField(
        max_digits=10,
        decimal_places=6,
        verbose_name=_('Hệ số'),
        help_text=_('Conversion factor'),
    )

    so_luong = models.DecimalField(
        max_digits=15,
        decimal_places=3,
        verbose_name=_('Số lượng'),
        help_text=_('Quantity'),
    )

    gia_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Giá ngoại tệ'),
        help_text=_('Price in foreign currency'),
    )

    tien_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tiền ngoại tệ'),
        help_text=_('Amount in foreign currency'),
    )

    thue_suat = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        verbose_name=_('Thuế suất'),
        help_text=_('Tax rate percentage'),
    )

    thue_nt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Thuế ngoại tệ'),
        help_text=_('Tax in foreign currency'),
    )

    thue = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Thuế'),
        help_text=_('Tax amount'),
    )

    gia = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Giá'),
        help_text=_('Price'),
    )

    tien = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name=_('Tiền'),
        help_text=_('Amount'),
    )

    # Text fields
    ten_tk_vt = models.CharField(
        max_length=255,
        verbose_name=_('Tên tài khoản vật tư'),
        help_text=_('Material account name'),
    )

    ten_tk_thue = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_('Tên tài khoản thuế'),
        help_text=_('Tax account name'),
    )

    objects = ChiTietPhieuNhapDieuChinhGiaHangMuaModelManager()

    class Meta:
        abstract = True
        verbose_name = _('Chi tiết phiếu nhập điều chỉnh giá hàng mua')
        verbose_name_plural = _('Chi tiết phiếu nhập điều chỉnh giá hàng mua')
        indexes = [
            models.Index(fields=['phieu_nhap']),
            models.Index(fields=['ma_vt']),
            models.Index(fields=['dvt']),
            models.Index(fields=['ma_kho']),
            models.Index(fields=['tk_vt']),
            models.Index(fields=['entity_model']),
            models.Index(fields=['line']),
        ]
        ordering = ['line']

    def __str__(self):  # noqa: C901
        return f'{self.phieu_nhap} - {self.line}: {self.ma_vt}'


class ChiTietPhieuNhapDieuChinhGiaHangMuaModel(
    ChiTietPhieuNhapDieuChinhGiaHangMuaModelAbstract
):
    """
    Base ChiTietPhieuNhapDieuChinhGiaHangMuaModel Implementation
    """

    class Meta(ChiTietPhieuNhapDieuChinhGiaHangMuaModelAbstract.Meta):
        abstract = False
        db_table = 'chi_tiet_phieu_nhap_dieu_chinh_gia_hang_mua'
