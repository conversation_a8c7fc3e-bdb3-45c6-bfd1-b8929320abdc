"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the serializers for the ChiTietHoaDonMuaHangTrongNuocModel.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.models.mua_hang.don_mua_hang.chi_tiet_don_mua_hang import (  # noqa: F401
    ChiTietDonMuaHangModel,
)
from django_ledger.models.mua_hang.don_mua_hang.don_mua_hang import (  # noqa: F401
    DonMuaHangModel,
)
from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401
    ChiTietHoaDonMuaHangTrongNuocModel,
)
from django_ledger.models.vat_tu import VatTuModel  # noqa: F401,


class ChiTietHoaDonMuaHangTrongNuocModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the ChiTietHoaDonMuaHangTrongNuocModel.
    Used for read operations.
    """

    # Read-only fields for related objects
    hoa_don_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_data = serializers.SerializerMethodField(read_only=True)
    ma_kho_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_lo_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    ma_nx_data = serializers.SerializerMethodField(read_only=True)
    tk_du_data = serializers.SerializerMethodField(read_only=True)
    ma_vi_tri_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)
    tk_vt_data = serializers.SerializerMethodField(read_only=True)
    tk_cpxt_data = serializers.SerializerMethodField(read_only=True)

    # Custom object responses
    id_dh = serializers.SerializerMethodField(read_only=True)
    line_dh = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietHoaDonMuaHangTrongNuocModel
        fields = [
            "uuid",
            "hoa_don",
            "hoa_don_data",
            "line",
            "id_dh",
            "line_dh",
            "ma_vt",
            "ma_vt_data",
            "dvt",
            "dvt_data",
            "ten_dvt0",
            "ma_kho",
            "ma_kho_data",
            "ma_lo",
            "ma_lo_data",
            "ten_lo",
            "lo_yn",
            "ma_vi_tri",
            "ma_vi_tri_data",
            "vi_tri_yn",
            "he_so",
            "qc_yn",
            "px_dd",
            "so_luong",
            "gia_nt0",
            "tien_nt0",
            "tien0",
            "tl_ck",
            "ck_nt",
            "ck",
            "tien_tck",
            "tien_tck_nt",
            "cp_nt",
            "gia_ton",
            "thue_xuat",
            "ma_thue",
            "ma_thue_data",
            "thue_nt",
            "tk_vt",
            "tk_vt_data",
            "tk_cpxt",
            "tk_cpxt_data",
            "ma_nx",
            "ma_nx_data",
            "tk_du",
            "tk_du_data",
            "ma_bp",
            "ma_bp_data",
            "ma_vv",
            "ma_vv_data",
            "ma_hd",
            "ma_hd_data",
            "ma_dtt",
            "ma_dtt_data",
            "ma_ku",
            "ma_ku_data",
            "ma_phi",
            "ma_phi_data",
            "ma_lsx",
            "ma_cp0",
            "ma_cp0_data",
            "ma_sp",
            "ma_sp_data",
            "so_ct_pn",
            "created",
            "updated",
            "sl_cl",
        ]
        read_only_fields = [
            "uuid",
            "hoa_don_data",
            "id_dh",
            "line_dh",
            "ma_vt_data",
            "dvt_data",
            "ma_kho_data",
            "ma_bp_data",
            "ma_vv_data",
            "ma_lo_data",
            "ma_nx_data",
            "tk_du_data",
            "ma_vi_tri_data",
            "ma_sp_data",
            "ma_hd_data",
            "ma_dtt_data",
            "ma_ku_data",
            "ma_phi_data",
            "ma_cp0_data",
            "ma_thue_data",
            "tk_vt_data",
            "tk_cpxt_data",
            "created",
            "updated",
            "sl_cl",
        ]

    def get_id_dh(self, obj):  # noqa: C901
        """
        Return object for purchase order id: { uuid, so_ct }
        """
        if not getattr(obj, 'id_dh', None):
            return None
        try:
            po = DonMuaHangModel.objects.select_related('chung_tu_item').get(
                uuid=obj.id_dh
            )

            so_ct = None
            if hasattr(po, 'so_ct'):
                so_ct = po.so_ct
            elif hasattr(po, 'chung_tu_item') and po.chung_tu_item:
                so_ct = getattr(po.chung_tu_item, 'so_ct', None)
            return {"uuid": str(po.uuid), "so_ct": so_ct}
        except DonMuaHangModel.DoesNotExist:
            return None

    def get_line_dh(self, obj):  # noqa: C901
        """
        Return object for purchase order line: { uuid, line }
        """
        line_dh_uuid = getattr(obj, 'line_dh', None)
        if not line_dh_uuid:
            return None

        import uuid

        try:
            uuid.UUID(str(line_dh_uuid))
        except (ValueError, TypeError):
            return None

        try:
            line = ChiTietDonMuaHangModel.objects.only('uuid', 'line').get(
                uuid=line_dh_uuid
            )
            return {"uuid": str(line.uuid), "line": line.line, "sl_cl": line.sl_cl}
        except ChiTietDonMuaHangModel.DoesNotExist:
            return None

    def get_hoa_don_data(self, obj):  # noqa: C901
        """
        Get basic information about the invoice
        """
        if not obj.hoa_don:
            return None

        # Check if hoa_don is a model instance or string
        if isinstance(obj.hoa_don, str):
            return {"uuid": obj.hoa_don, "so_ct": "", "ngay_ct": None, "ten_kh": ""}

        so_ct = None
        if hasattr(obj.hoa_don, 'so_ct'):
            so_ct = obj.hoa_don.so_ct
        elif hasattr(obj.hoa_don, 'chung_tu_item') and obj.hoa_don.chung_tu_item:
            so_ct = getattr(obj.hoa_don.chung_tu_item, 'so_ct', None)

        # Get basic invoice data
        hoa_don_data = {
            "uuid": str(obj.hoa_don.uuid),
            "so_ct": so_ct,
            "ngay_ct": getattr(obj.hoa_don, "ngay_ct", None),
            "ten_kh": getattr(obj.hoa_don, "ten_kh", ""),
        }

        # Add document number if available
        if hasattr(obj.hoa_don, "so_ct") and obj.hoa_don.so_ct:
            if hasattr(obj.hoa_don.so_ct, "uuid"):
                hoa_don_data["so_ct_data"] = {
                    "uuid": str(obj.hoa_don.so_ct.uuid),
                    "ma_ct": getattr(obj.hoa_don.so_ct, "ma_ct", ""),
                    "ten_ct": getattr(obj.hoa_don.so_ct, "ten_ct", ""),
                }

        return hoa_don_data

    def get_ma_vt_data(self, obj):  # noqa: C901
        """
        Get basic information about the material
        """
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_dvt_data(self, obj):  # noqa: C901
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_kho_data(self, obj):  # noqa: C901
        """
        Get basic information about the warehouse
        """
        if not obj.ma_kho:
            return None

        # Check if ma_kho is a model instance or string
        if isinstance(obj.ma_kho, str):
            return {"uuid": obj.ma_kho, "ma_kho": "", "ten_kho": ""}

        return {
            "uuid": (
                str(obj.ma_kho.uuid)
                if obj.ma_kho and hasattr(obj.ma_kho, "uuid")
                else None
            ),
            "ma_kho": getattr(obj.ma_kho, "ma_kho", ""),
            "ten_kho": getattr(obj.ma_kho, "ten_kho", "")
            or getattr(obj, "ten_kho", ""),
        }

    def get_ma_bp_data(self, obj):  # noqa: C901
        """
        Get basic information about the department
        """
        if not obj.ma_bp:
            return None

        # Check if ma_bp is a model instance or string
        if isinstance(obj.ma_bp, str):
            return {"uuid": obj.ma_bp, "ma_bp": "", "ten_bp": ""}

        return {
            "uuid": (
                str(obj.ma_bp.uuid)
                if obj.ma_bp and hasattr(obj.ma_bp, "uuid")
                else None
            ),
            "ma_bp": getattr(obj.ma_bp, "ma_bp", ""),
            "ten_bp": getattr(obj.ma_bp, "ten_bp", "") or getattr(obj, "ten_bp", ""),
        }

    def get_ma_vv_data(self, obj):  # noqa: C901
        """
        Get basic information about the job
        """
        if not obj.ma_vv:
            return None

        # Check if ma_vv is a model instance or string
        if isinstance(obj.ma_vv, str):
            return {"uuid": obj.ma_vv, "ma_vv": "", "ten_vv": ""}

        return {
            "uuid": (
                str(obj.ma_vv.uuid)
                if obj.ma_vv and hasattr(obj.ma_vv, "uuid")
                else None
            ),
            "ma_vv": getattr(obj.ma_vv, "ma_vu_viec", ""),
            "ten_vv": getattr(obj.ma_vv, "ten_vu_viec", "")
            or getattr(obj, "ten_vv", ""),
        }

    def get_ma_lo_data(self, obj):  # noqa: C901
        """
        Get basic information about the lot
        """
        if not obj.ma_lo:
            return None

        # Check if ma_lo is a model instance or string
        if isinstance(obj.ma_lo, str):
            return {
                "uuid": obj.ma_lo,
                "ma_lo": "",
                "ten_lo": getattr(obj, "ten_lo", ""),
            }

        return {
            "uuid": (
                str(obj.ma_lo.uuid)
                if obj.ma_lo and hasattr(obj.ma_lo, "uuid")
                else None
            ),
            "ma_lo": getattr(obj.ma_lo, "ma_lo", ""),
            "ten_lo": getattr(obj.ma_lo, "ten_lo", "") or getattr(obj, "ten_lo", ""),
        }

    def get_ma_nx_data(self, obj):  # noqa: C901
        """
        Get basic information about the import/export
        """
        if not obj.ma_nx:
            return None

        # Check if ma_nx is a model instance or string
        if isinstance(obj.ma_nx, str):
            return {"uuid": obj.ma_nx, "ma_nx": "", "ten_nx": ""}

        return {
            "uuid": (
                str(obj.ma_nx.uuid)
                if obj.ma_nx and hasattr(obj.ma_nx, "uuid")
                else None
            ),
            "ma_nx": getattr(obj.ma_nx, "ma_nx", ""),
            "ten_nx": getattr(obj.ma_nx, "ten_nx", ""),
        }

    def get_tk_du_data(self, obj):  # noqa: C901
        """
        Get basic information about the balance account
        """
        if not obj.tk_du:
            return None

        # Check if tk_du is a model instance or string
        if isinstance(obj.tk_du, str):
            return {"uuid": obj.tk_du, "account_code": "", "account_name": ""}

        return {
            "uuid": (
                str(obj.tk_du.uuid)
                if obj.tk_du and hasattr(obj.tk_du, "uuid")
                else None
            ),
            "account_code": getattr(obj.tk_du, "account_code", ""),
            "account_name": getattr(obj.tk_du, "account_name", ""),
        }

    def get_ma_vi_tri_data(self, obj):  # noqa: C901
        """
        Get basic information about the location
        """
        if not obj.ma_vi_tri:
            return None

        # Check if ma_vi_tri is a model instance or string
        if isinstance(obj.ma_vi_tri, str):
            return {"uuid": obj.ma_vi_tri, "ma_vi_tri": "", "ten_vi_tri": ""}

        return {
            "uuid": (
                str(obj.ma_vi_tri.uuid)
                if obj.ma_vi_tri and hasattr(obj.ma_vi_tri, "uuid")
                else None
            ),
            "ma_vi_tri": getattr(obj.ma_vi_tri, "ma_vi_tri", ""),
            "ten_vi_tri": getattr(obj.ma_vi_tri, "ten_vi_tri", ""),
        }

    def get_ma_sp_data(self, obj):  # noqa: C901
        """
        Get basic information about the product
        """
        if not obj.ma_sp:
            return None

        # Check if ma_sp is a model instance or string
        if isinstance(obj.ma_sp, str):
            return {"uuid": obj.ma_sp, "ma_vt": "", "ten_vt": ""}

        return {
            "uuid": (
                str(obj.ma_sp.uuid)
                if obj.ma_sp and hasattr(obj.ma_sp, "uuid")
                else None
            ),
            "ma_vt": getattr(obj.ma_sp, "ma_vt", ""),
            "ten_vt": getattr(obj.ma_sp, "ten_vt", ""),
        }

    def get_ma_hd_data(self, obj):  # noqa: C901
        """
        Get basic information about the contract
        """
        if not obj.ma_hd:
            return None

        try:
            return {
                "uuid": str(obj.ma_hd.uuid) if hasattr(obj.ma_hd, "uuid") else None,
                "ma_hd": getattr(obj.ma_hd, "ma_hd", ""),
                "ten_hd": getattr(obj.ma_hd, "ten_hd", ""),
                "ten_hd2": getattr(obj.ma_hd, "ten_hd2", ""),
                "loai_hd": getattr(obj.ma_hd, "loai_hd", ""),
                "status": getattr(obj.ma_hd, "status", ""),
            }
        except Exception:
            return None

    def get_ma_dtt_data(self, obj):  # noqa: C901
        """
        Get basic information about the payment term
        """
        if not obj.ma_dtt:
            return None

        try:
            return {
                "uuid": str(obj.ma_dtt.uuid) if hasattr(obj.ma_dtt, "uuid") else None,
                "ma_dtt": getattr(obj.ma_dtt, "ma_dtt", ""),
                "ten_dtt": getattr(obj.ma_dtt, "ten_dtt", ""),
                "han_tt": getattr(obj.ma_dtt, "han_tt", 0),
                "status": getattr(obj.ma_dtt, "status", ""),
            }
        except Exception:
            return None

    def get_ma_ku_data(self, obj):  # noqa: C901
        """
        Get basic information about the agreement
        """
        if not obj.ma_ku:
            return None

        return {
            "uuid": str(obj.ma_ku.uuid),
            "ma_ku": getattr(obj.ma_ku, "ma_ku", ""),
            "ten_ku": getattr(obj.ma_ku, "ten_ku", ""),
            "ten_ku2": getattr(obj.ma_ku, "ten_ku2", ""),
            "loai_ku": getattr(obj.ma_ku, "loai_ku", ""),
            "tinh_trang": getattr(obj.ma_ku, "tinh_trang", ""),
            "status": getattr(obj.ma_ku, "status", ""),
        }

    def get_ma_phi_data(self, obj):  # noqa: C901
        """
        Get basic information about the fee
        """
        if not obj.ma_phi:
            return None

        return {
            "uuid": str(obj.ma_phi.uuid),
            "ma_phi": getattr(obj.ma_phi, "ma_phi", ""),
            "ten_phi": getattr(obj.ma_phi, "ten_phi", ""),
            "loai_phi": getattr(obj.ma_phi, "loai_phi", ""),
            "status": getattr(obj.ma_phi, "status", ""),
        }

    def get_ma_cp0_data(self, obj):  # noqa: C901
        """
        Get basic information about the invalid cost
        """
        if not obj.ma_cp0:
            return None

        return {
            "uuid": str(obj.ma_cp0.uuid),
            "ma_cp": getattr(obj.ma_cp0, "ma_cpkhl", ""),
            "ten_cp": getattr(obj.ma_cp0, "ten_cpkhl", ""),
            "ten_cp2": getattr(obj.ma_cp0, "ten_cpkhl2", ""),
            "ghi_chu": getattr(obj.ma_cp0, "ghi_chu", ""),
            "status": getattr(obj.ma_cp0, "status", ""),
        }

    def get_ma_thue_data(self, obj):  # noqa: C901
        """
        Get basic information about the tax
        """
        if not obj.ma_thue:
            return None

        return {
            "uuid": str(obj.ma_thue.uuid),
            "ma_thue": getattr(obj.ma_thue, "ma_thue", ""),
            "ten_thue": getattr(obj.ma_thue, "ten_thue", ""),
            "thue_suat": getattr(obj.ma_thue, "thue_suat", 0),
            "loai_thue": getattr(obj.ma_thue, "loai_thue", ""),
            "status": getattr(obj.ma_thue, "status", ""),
        }

    def get_tk_vt_data(self, obj):  # noqa: C901
        """
        Get basic information about the material account
        """
        if not obj.tk_vt:
            return None

        return {
            "uuid": str(obj.tk_vt.uuid),
            "code": getattr(obj.tk_vt, "code", ""),
            "name": getattr(obj.tk_vt, "name", ""),
            "account_type": getattr(obj.tk_vt, "account_type", ""),
            "balance_type": getattr(obj.tk_vt, "balance_type", ""),
        }

    def get_tk_cpxt_data(self, obj):  # noqa: C901
        """
        Get basic information about the export cost account
        """
        if not obj.tk_cpxt:
            return None

        return {
            "uuid": str(obj.tk_cpxt.uuid),
            "code": getattr(obj.tk_cpxt, "code", ""),
            "name": getattr(obj.tk_cpxt, "name", ""),
            "account_type": getattr(obj.tk_cpxt, "account_type", ""),
            "balance_type": getattr(obj.tk_cpxt, "balance_type", ""),
        }


class ChiTietHoaDonMuaHangTrongNuocModelCreateUpdateSerializer(
    serializers.ModelSerializer
):
    """
    Serializer for the ChiTietHoaDonMuaHangTrongNuocModel.
    Used for create and update operations.
    """

    # Explicitly define UUID field to preserve it during serialization
    uuid = serializers.UUIDField(required=False, allow_null=True)

    class Meta:
        model = ChiTietHoaDonMuaHangTrongNuocModel
        fields = [
            "uuid",
            "line",
            "ma_vt",
            "ten_vt0",
            "dvt",
            "ten_dvt0",
            "ma_kho",
            "ma_lo",
            "ten_lo",
            "lo_yn",
            "ma_vi_tri",
            "vi_tri_yn",
            "he_so",
            "qc_yn",
            "px_dd",
            "so_luong",
            "gia_nt0",
            "tien_nt0",
            "tien0",
            "cp_nt",
            "gia_ton",
            "tl_ck",
            "ck_nt",
            "ck",
            "tien_tck",
            "tien_tck_nt",
            "thue_xuat",
            "ma_thue",
            "thue_nt",
            "tk_vt",
            "tk_cpxt",
            "ma_nx",
            "tk_du",
            "ma_bp",
            "ma_vv",
            "ma_hd",
            "ma_dtt",
            "ma_ku",
            "ma_phi",
            "ma_lsx",
            "ma_cp0",
            "ma_sp",
            "id_dh",
            "id_hd4",
            "line_dh",
            "line_hd4",
            "so_ct_dh",
            "so_ct_hd4",
            "so_ct_pn",
            "sl_cl",
        ]
        extra_kwargs = {
            'uuid': {'required': False, 'allow_null': True},
            'ten_vt0': {'required': False},
            'ten_dvt0': {'required': False},
            'ten_lo': {'required': False},
            'lo_yn': {'required': False},
            'vi_tri_yn': {'required': False},
            'he_so': {'required': False},
            'qc_yn': {'required': False},
            'px_dd': {'required': False},
            'tl_ck': {'required': False},
            'ck_nt': {'required': False},
            'ck': {'required': False},
            'ma_nx': {'required': False},
            'tk_vt': {'required': False},
            'tk_cpxt': {'required': False},
            'tk_du': {'required': False},
            'ma_vi_tri': {'required': False},
            'ma_sp': {'required': False},
            'id_dh': {'required': False},
            'id_hd4': {'required': False},
            'line_dh': {'required': False, 'allow_null': True},
            'line_hd4': {'required': False, 'allow_null': True},
            'so_ct_dh': {'required': False},
            'so_ct_hd4': {'required': False},
            'so_ct_pn': {'required': False},
            'tien_tck': {'required': False},
            'tien_tck_nt': {'required': False},
        }

    def validate(self, data):  # noqa: C901
        """
        Validate the data before creating or updating the model instance.

        Parameters
        ----------
        data : dict
            The data to validate.

        Returns
        -------
        dict
            The validated data.
        """
        # Normalize empty strings for optional fields
        optional_fields = [
            'line_dh',
            'line_hd4',
            'id_dh',
            'id_hd4',
            'so_ct_dh',
            'so_ct_hd4',
        ]
        for field in optional_fields:
            if field in data and data[field] == "":
                data[field] = None

        # Handle line_dh field - should be UUID string or None, not integer
        if 'line_dh' in data:
            if isinstance(data['line_dh'], int):
                # If integer is passed, it's likely a mistake - set to None
                # since line_dh should be a UUID string referencing ChiTietDonMuaHangModel.uuid
                data['line_dh'] = None
            elif data['line_dh'] == "" or data['line_dh'] is None:
                data['line_dh'] = None
            elif isinstance(data['line_dh'], str):
                # Validate if it's a proper UUID string
                try:
                    from uuid import UUID

                    UUID(
                        data['line_dh']
                    )  # This will raise ValueError if not valid UUID
                except ValueError:
                    # If not a valid UUID, set to None
                    data['line_dh'] = None

        # Handle line_hd4 field - ensure it's an integer or None
        if 'line_hd4' in data:
            if data['line_hd4'] == "" or data['line_hd4'] is None:
                data['line_hd4'] = None
            else:
                try:
                    data['line_hd4'] = int(data['line_hd4'])
                except (ValueError, TypeError):
                    data['line_hd4'] = None

        # Ensure line field is properly converted to integer
        if 'line' in data:
            try:
                data['line'] = int(data['line'])
            except (ValueError, TypeError):
                # If conversion fails, keep original value and let model validation handle it
                pass

        # Auto-calculate amount if not provided
        if "tien_nt0" not in data and "so_luong" in data and "gia_nt0" in data:
            data["tien_nt0"] = data["so_luong"] * data["gia_nt0"]

        return data
