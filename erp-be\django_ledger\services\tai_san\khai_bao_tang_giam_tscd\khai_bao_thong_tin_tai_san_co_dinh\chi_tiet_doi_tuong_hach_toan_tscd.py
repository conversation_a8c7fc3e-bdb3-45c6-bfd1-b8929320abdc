"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiTietDoiTuongHachToanTSCDService, which handles business logic  # noqa: E501
for the ChiTietDoiTuongHachToanTSCDModel.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import transaction
from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models import (
    ChiTietDoiTuongHachToanTSCDModel,  # noqa: F401
    KhaiBaoThongTinTaiSanCoDinhModel,
)
from django_ledger.repositories.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh.chi_tiet_doi_tuong_hach_toan_tscd import (
    ChiTietDoiTuongHachToanTSCDRepository,  # noqa: F401
)
from django_ledger.services.base import BaseService  # noqa: F401,


class ChiTietDoiTuongHachToanTSCDService(BaseService):
    """
    Service class for handling ChiTietDoiTuongHachToanTSCD (Fixed Asset Accounting Object Detail) model business logic  # noqa: E501
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the service with the repository.
        """
        self.repository = ChiTietDoiTuongHachToanTSCDRepository()
        super().__init__()

    def get_by_id(
        self, uuid: str
    ) -> Optional[ChiTietDoiTuongHachToanTSCDModel]:  # noqa: C901
        """
        Retrieves a ChiTietDoiTuongHachToanTSCDModel by its UUID.

        Parameters
        ----------
        uuid : str
            The UUID of the ChiTietDoiTuongHachToanTSCDModel to retrieve.

        Returns
        -------
        Optional[ChiTietDoiTuongHachToanTSCDModel]
            The ChiTietDoiTuongHachToanTSCDModel with the given UUID, or None if not found.  # noqa: E501
        """
        return self.repository.get_by_id(uuid=uuid)

    def get_by_parent(self, parent_uuid: str) -> QuerySet:  # noqa: C901
        """
        Retrieves all ChiTietDoiTuongHachToanTSCDModel instances for a parent KhaiBaoThongTinTaiSanCoDinhModel.  # noqa: E501

        Parameters
        ----------
        parent_uuid : str
            The UUID of the parent KhaiBaoThongTinTaiSanCoDinhModel.

        Returns
        -------
        QuerySet
            A QuerySet of ChiTietDoiTuongHachToanTSCDModel instances.
        """
        return self.repository.get_by_parent(parent_uuid=parent_uuid)

    def create(
        self, parent: KhaiBaoThongTinTaiSanCoDinhModel, data: Dict[str, Any]
    ) -> ChiTietDoiTuongHachToanTSCDModel:  # noqa: C901
        """
        Creates a new ChiTietDoiTuongHachToanTSCDModel instance.

        Parameters
        ----------
        parent : KhaiBaoThongTinTaiSanCoDinhModel
            The parent KhaiBaoThongTinTaiSanCoDinhModel instance.
        data : Dict[str, Any]
            The data for the new ChiTietDoiTuongHachToanTSCDModel.

        Returns
        -------
        ChiTietDoiTuongHachToanTSCDModel
            The created ChiTietDoiTuongHachToanTSCDModel instance.
        """
        return self.repository.create(parent_uuid=parent.uuid, data=data)

    @transaction.atomic
    def bulk_create(
        self, parent: KhaiBaoThongTinTaiSanCoDinhModel, data: List[Dict[str, Any]]
    ) -> None:  # noqa: C901
        """
        Creates multiple ChiTietDoiTuongHachToanTSCDModel instances.

        Parameters
        ----------
        parent : KhaiBaoThongTinTaiSanCoDinhModel
            The parent KhaiBaoThongTinTaiSanCoDinhModel instance.
        data : List[Dict[str, Any]]
            The list of data for creating the ChiTietDoiTuongHachToanTSCDModel instances.

        Returns
        -------
        None
        """
        for item in data:
            self.repository.create(parent_uuid=parent.uuid, data=item)
        return None

    @transaction.atomic
    def bulk_update(
        self, parent: KhaiBaoThongTinTaiSanCoDinhModel, data: List[Dict[str, Any]]
    ) -> None:  # noqa: C901
        """
        Update multiple ChiTietDoiTuongHachToanTSCDModel instances for a specific parent.

        Parameters
        ----------
        parent : KhaiBaoThongTinTaiSanCoDinhModel
            The parent KhaiBaoThongTinTaiSanCoDinhModel instance.
        data : List[Dict[str, Any]]
            The list of data for the instances to update.

        Returns
        -------
        None
        """
        existing_chi_tiet = parent.chi_tiet_doi_tuong_hach_toan.filter(deleted__isnull=True)
        existing_chi_tiet_dict = {str(ct.uuid): ct for ct in existing_chi_tiet}

        updated_uuids = set()

        for item in data:
            item_uuid = item.get("uuid")

            if item_uuid and item_uuid in existing_chi_tiet_dict:
                updated_uuids.add(item_uuid)
                item_data = item.copy()
                if "uuid" in item_data:
                    item_data.pop("uuid")
                self.update(uuid=item_uuid, data=item_data)
            else:
                item_data = item.copy()
                if "uuid" in item_data:
                    item_data.pop("uuid")
                self.create(parent=parent, data=item_data)

        for uuid_str in existing_chi_tiet_dict:
            if uuid_str not in updated_uuids:
                self.delete(uuid=uuid_str)

    def update(
        self, uuid: str, data: Dict[str, Any]
    ) -> Optional[ChiTietDoiTuongHachToanTSCDModel]:  # noqa: C901
        """
        Updates an existing ChiTietDoiTuongHachToanTSCDModel instance.

        Parameters
        ----------
        uuid : str
            The UUID of the ChiTietDoiTuongHachToanTSCDModel to update.
        data : Dict[str, Any]
            The data to update the ChiTietDoiTuongHachToanTSCDModel with.

        Returns
        -------
        Optional[ChiTietDoiTuongHachToanTSCDModel]
            The updated ChiTietDoiTuongHachToanTSCDModel instance, or None if not found.
        """
        # Check if the record exists
        instance = self.get_by_id(uuid=uuid)
        if not instance:
            raise ValueError(f"Record with UUID {uuid} not found")

        return self.repository.update(uuid=uuid, data=data)

    def delete(self, uuid: str) -> bool:  # noqa: C901
        """
        Deletes a ChiTietDoiTuongHachToanTSCDModel instance.

        Parameters
        ----------
        uuid : str
            The UUID of the ChiTietDoiTuongHachToanTSCDModel to delete.

        Returns
        -------
        bool
            True if the ChiTietDoiTuongHachToanTSCDModel was deleted, False otherwise.
        """
        # Check if the record exists
        instance = self.get_by_id(uuid=uuid)
        if not instance:
            raise ValueError(f"Record with UUID {uuid} not found")

        return self.repository.delete(uuid=uuid)
