"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietHoaDonDichVuTraLaiGiamGia (Service Invoice Return/Discount Detail) model.  # noqa: E501
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer  # noqa: F401
from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu.chi_tiet_hoa_don import (
    ChiTietHoaDonModel,
)
from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu.hoa_don_dich_vu import (
    HoaDonDichVuModel,
)
from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dich_vu_tra_lai_giam_gia import (  # noqa: F401
    ChiTietHoaDonDichVuTraLaiGiamGiaModel,
)
from django_ledger.services.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dich_vu_tra_lai_giam_gia import (  # noqa: F401,
    ChiTietHoaDonDichVuTraLaiGiamGiaService,
)


class ChiTietHoaDonDichVuTraLaiGiamGiaModelSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietHoaDonDichVuTraLaiGiamGia model.
    """

    hoa_don_data = serializers.SerializerMethodField()
    tk_no_data = serializers.SerializerMethodField()
    tk_thue_no_data = serializers.SerializerMethodField()
    dvt_data = serializers.SerializerMethodField()
    ma_thue_data = serializers.SerializerMethodField()
    ma_bp_data = serializers.SerializerMethodField()
    ma_vv_data = serializers.SerializerMethodField()
    ma_hd_data = serializers.SerializerMethodField()
    ma_dtt_data = serializers.SerializerMethodField()
    ma_ku_data = serializers.SerializerMethodField()
    ma_phi_data = serializers.SerializerMethodField()
    ma_sp_data = serializers.SerializerMethodField()
    ma_cp0_data = serializers.SerializerMethodField()

    # Custom object responses for id_hd and line_hd
    id_hd = serializers.SerializerMethodField(read_only=True)
    line_hd = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietHoaDonDichVuTraLaiGiamGiaModel
        fields = [
            'uuid',
            'hoa_don',
            'hoa_don_data',
            'line',
            'tk_no',
            'tk_no_data',
            'dvt',
            'dvt_data',
            'so_luong',
            'gia_nt2',
            'tien_nt2',
            'ma_thue',
            'ma_thue_data',
            'ten_thue',
            'tk_thue_no',
            'tk_thue_no_data',
            'ten_tk_thue',
            'thue_suat',
            'thue_nt',
            'dien_giai',
            'gia2',
            'tien2',
            'thue',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_cp0',
            'ma_cp0_data',
            'id_hd',
            'line_hd',
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'created',
            'updated',
            'hoa_don_data',
            'tk_no_data',
            'tk_thue_no_data',
            'dvt_data',
            'ma_thue_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_dtt_data',
            'ma_ku_data',
            'ma_phi_data',
            'ma_sp_data',
            'ma_cp0_data',
        ]

    def get_hoa_don_data(self, obj):  # noqa: C901
        """
        Get hoa_don data.
        """
        if obj.hoa_don:
            return {
                'uuid': str(obj.hoa_don.uuid),
                'so_ct': obj.hoa_don.so_ct,
                'dien_giai': obj.hoa_don.dien_giai,
            }
        return None

    def get_tk_no_data(self, obj):  # noqa: C901
        """
        Get tk_no data.
        """
        if obj.tk_no:
            return {
                'uuid': obj.tk_no.uuid,
                'code': obj.tk_no.code,
                'name': obj.tk_no.name,
            }
        return None

    def get_tk_thue_no_data(self, obj):  # noqa: C901
        """
        Get tk_thue_no data.
        """
        if obj.tk_thue_no:
            return {
                'uuid': obj.tk_thue_no.uuid,
                'code': obj.tk_thue_no.code,
                'name': obj.tk_thue_no.name,
            }
        return None

    def get_dvt_data(self, obj):  # noqa: C901
        """
        Get unit of measure data.
        """
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_thue_data(self, obj):  # noqa: C901
        """
        Get ma_thue data.
        """
        if obj.ma_thue:
            return {
                'uuid': obj.ma_thue.uuid,
                'ma_thue': obj.ma_thue.ma_thue,
                'ten_thue': obj.ma_thue.ten_thue,
                'thue_suat': obj.ma_thue.thue_suat,
            }
        return None

    def get_ma_bp_data(self, obj):  # noqa: C901
        """
        Get ma_bp data.
        """
        if obj.ma_bp:
            return {
                'uuid': obj.ma_bp.uuid,
                'ma_bp': obj.ma_bp.ma_bp,
                'ten_bp': obj.ma_bp.ten_bp,
            }
        return None

    def get_ma_vv_data(self, obj):  # noqa: C901
        """
        Get ma_vv data.
        """
        if obj.ma_vv:
            return {
                'uuid': obj.ma_vv.uuid,
                'ma_vv': obj.ma_vv.ma_vu_viec,
                'ten_vv': obj.ma_vv.ten_vu_viec,
            }
        return None

    def get_ma_hd_data(self, obj):  # noqa: C901
        """
        Get ma_hd data.
        """
        if obj.ma_hd:
            return {
                'uuid': obj.ma_hd.uuid,
                'ma_hd': obj.ma_hd.ma_hd,
                'ten_hd': obj.ma_hd.ten_hd,
            }
        return None

    def get_ma_dtt_data(self, obj):  # noqa: C901
        """
        Get ma_dtt data.
        """
        if obj.ma_dtt:
            return {
                'uuid': obj.ma_dtt.uuid,
                'ma_dtt': obj.ma_dtt.ma_dtt,
                'ten_dtt': obj.ma_dtt.ten_dtt,
            }
        return None

    def get_ma_ku_data(self, obj):  # noqa: C901
        """
        Get ma_ku data.
        """
        if obj.ma_ku:
            return {
                'uuid': obj.ma_ku.uuid,
                'ma_ku': obj.ma_ku.ma_ku,
                'ten_ku': obj.ma_ku.ten_ku,
            }
        return None

    def get_ma_phi_data(self, obj):  # noqa: C901
        """
        Get ma_phi data.
        """
        if obj.ma_phi:
            return {
                'uuid': obj.ma_phi.uuid,
                'ma_phi': obj.ma_phi.ma_phi,
                'ten_phi': obj.ma_phi.ten_phi,
            }
        return None

    def get_ma_sp_data(self, obj):  # noqa: C901
        """
        Get ma_sp data.
        """
        if obj.ma_sp:
            return {
                'uuid': obj.ma_sp.uuid,
                'ma_vt': obj.ma_sp.ma_vt,
                'ten_vt': obj.ma_sp.ten_vt,
            }
        return None

    def get_ma_cp0_data(self, obj):  # noqa: C901
        if obj.ma_cp0:
            return {
                'uuid': obj.ma_cp0.uuid,
                'ma_cpkhl': obj.ma_cp0.ma_cpkhl,
                'ten_cpkhl': obj.ma_cp0.ten_cpkhl,
            }
        return None

    def get_id_hd(self, obj):  # noqa: C901
        """
        Return object for service invoice id: { uuid, so_ct }
        """
        if not getattr(obj, 'id_hd', None):
            return None
        try:
            invoice = HoaDonDichVuModel.objects.select_related('chung_tu_item').get(
                uuid=obj.id_hd
            )

            so_ct = None
            if hasattr(invoice, 'so_ct'):
                so_ct = invoice.so_ct
            elif hasattr(invoice, 'chung_tu_item') and invoice.chung_tu_item:
                so_ct = getattr(invoice.chung_tu_item, 'so_ct', None)
            return {"uuid": str(invoice.uuid), "so_ct": so_ct}
        except HoaDonDichVuModel.DoesNotExist:
            return None

    def get_line_hd(self, obj):  # noqa: C901
        """
        Return object for service invoice line: { uuid, line }
        """
        if not getattr(obj, 'line_hd', None):
            return None
        try:
            line = ChiTietHoaDonModel.objects.only('uuid', 'line').get(uuid=obj.line_hd)
            return {"uuid": str(line.uuid), "line": line.line, "sl_cl": line.sl_cl}
        except ChiTietHoaDonModel.DoesNotExist:
            return None


class ChiTietHoaDonDichVuTraLaiGiamGiaModelCreateUpdateSerializer(
    serializers.ModelSerializer
):
    """
    Serializer for creating and updating ChiTietHoaDonDichVuTraLaiGiamGia model.
    """

    class Meta:
        model = ChiTietHoaDonDichVuTraLaiGiamGiaModel
        fields = [
            'uuid',
            'hoa_don',
            'line',
            'tk_no',
            'dvt',
            'so_luong',
            'gia_nt2',
            'tien_nt2',
            'ma_thue',
            'ten_thue',
            'tk_thue_no',
            'ten_tk_thue',
            'thue_suat',
            'thue_nt',
            'dien_giai',
            'gia2',
            'tien2',
            'thue',
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_cp0',
            'id_hd',
            'line_hd',
            'created',
            'updated',
        ]
        read_only_fields = ['uuid', 'created', 'updated']

    def validate(self, data):  # noqa: C901
        # Normalize empty strings -> None for optional char fields
        for f in ['id_hd', 'line_hd']:
            if f in data and data[f] == "":
                data[f] = None
        return data
