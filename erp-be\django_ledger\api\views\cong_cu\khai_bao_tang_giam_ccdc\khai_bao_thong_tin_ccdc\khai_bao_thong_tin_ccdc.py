"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoThongTinCCDC (Tool Information Declaration) view implementation.
"""

from rest_framework import permissions, status  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.decorators.error_handling import api_exception_handler
from django_ledger.api.serializers.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc import (  # noqa: F401
    KhaiBaoThongTinCCDCModelSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.services.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc import (  # noqa: F401,
    KhaiBaoThongTinCCDCService,
)


class KhaiBaoThongTinCCDCViewSet(EntityRelatedViewSet):
    """
    ViewSet for KhaiBaoThongTinCCDCModel.
    """

    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811
    lookup_field = 'pk'

    def __init__(self, *args, **kwargs):  # noqa: C901
        super().__init__(*args, **kwargs)
        self.service = KhaiBaoThongTinCCDCService()

    def get_serializer_context(self):  # noqa: C901
        """
        Returns the serializer context.
        """
        return {
            'request': self.request,
            'entity_slug': self.kwargs['entity_slug'],
        }

    def get_serializer(self, *args, **kwargs):  # noqa: C901
        """
        Returns the serializer instance.
        """
        kwargs['context'] = self.get_serializer_context()
        return KhaiBaoThongTinCCDCModelSerializer(*args, **kwargs)

    @api_exception_handler
    def list(self, request, entity_slug=None):  # noqa: F811,
        """
        Lists KhaiBaoThongTinCCDCModel instances for a specific entity.
        """
        queryset = self.service.list_for_entity(entity_slug=entity_slug)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @api_exception_handler
    def retrieve(self, request, entity_slug=None, pk=None):  # noqa: F811,
        """
        Retrieves a KhaiBaoThongTinCCDCModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        entity_slug : str
            The entity slug.
        pk : str
            The UUID of the KhaiBaoThongTinCCDCModel to retrieve.
        """
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)
        if not instance:
            return Response(
                {'detail': 'KhaiBaoThongTinCCDC not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @api_exception_handler
    def create(self, request, entity_slug=None):  # noqa: F811,
        """
        Creates a new KhaiBaoThongTinCCDCModel instance.
        """
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            # Call service to create data
            validated_data = serializer.validated_data
            instance = self.service.create(
                entity_slug=entity_slug, data=validated_data
            )
            # Get new data to return
            serializer = self.get_serializer(instance)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @api_exception_handler
    def update(self, request, entity_slug=None, pk=None):  # noqa: F811,
        """
        Updates an existing KhaiBaoThongTinCCDCModel instance.
        """
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)
        if not instance:
            return Response(
                {'detail': 'KhaiBaoThongTinCCDC not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            validated_data = serializer.validated_data
            instance = self.service.update(
                entity_slug=entity_slug, uuid=pk, data=validated_data
            )
            serializer = self.get_serializer(instance)
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

     
    @api_exception_handler
    def partial_update(self, request, entity_slug=None, pk=None):  # noqa: F811,
        """
        Partially updates an existing KhaiBaoThongTinCCDCModel instance.
        """
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)
        if not instance:
            return Response(
                {'detail': 'KhaiBaoThongTinCCDC not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.get_serializer(
            instance, data=request.data, partial=True
        )
        if serializer.is_valid():
            # Call service to update data
            validated_data = serializer.validated_data
            instance = self.service.update(
                entity_slug=entity_slug, uuid=pk, data=validated_data
            )

            # Get new data to return
            serializer = self.get_serializer(instance)
            return Response(serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @api_exception_handler
    def destroy(self, request, entity_slug=None, pk=None):  # noqa: F811,
        """
        Deletes an existing KhaiBaoThongTinCCDCModel instance.
        """
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)
        if not instance:
            return Response(
                {'detail': 'KhaiBaoThongTinCCDC not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        self.service.delete(entity_slug=entity_slug, uuid=pk)
        return Response(status=status.HTTP_204_NO_CONTENT)
