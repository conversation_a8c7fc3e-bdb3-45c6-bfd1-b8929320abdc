"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Hoa Don Ban Hang (Sales Invoice) service implementation.
"""

import logging
from datetime import date
from typing import Any, Dict, List, Optional

from django.core.exceptions import ObjectDoesNotExist  # noqa: F401
from django.db import transaction
from django.db.models import QuerySet
from django.shortcuts import get_object_or_404

from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import (  # noqa: F401,
    HoaDonBanHangModel,
)
from django_ledger.models.entity import EntityModel
from django_ledger.repositories.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import (  # noqa: F401,
    HoaDonBanHangRepository,
)
from django_ledger.services.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang.chi_tiet_hoa_don_ban_hang import (  # noqa: F401,
    ChiTietHoaDonBanHangService,
)
from django_ledger.services.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang.thong_tin_thanh_toan_hoa_don_ban_hang import (  # noqa: F401,
    ThongTinThanhToanHoaDonBanHangService,
)
from django_ledger.services.base import BaseService

# ✅ UNIFIED SERVICES: Replace legacy utils with unified services
from django_ledger.utils_new.debt_management import CongNoCreation
from django_ledger.utils_new.inventory_management import TonKhoCreation
from django_ledger.utils_new.lookup_uuid_utils import (
    lookup_phieu_xuat_kho_uuid,
    lookup_related_document_uuids_ban_hang,
)
from django_ledger.utils_new.xoa_chung_tu import XoaChungTu


class HoaDonBanHangService(BaseService):
    """
    Service class for HoaDonBanHangModel.
    Provides business logic for HoaDonBanHangModel operations.

    ✅ ENHANCED: Integrated with Unified Accounting Service
    ERP Expert - 20 năm kinh nghiệm
    """

    # ✅ PREDEFINED CONFIGURATION: Hóa đơn bán hàng accounting mappings
    SALES_INVOICE_ACCOUNTING_CONFIG = [
        {
            'journal_type': 'DT0CK',  # Doanh thu
            'debit_account_field': 'tk',  # Tài khoản công nợ - DEBIT
            'credit_account_field': 'tk_dt',  # Tài khoản doanh thu - CREDIT
            'debit_account_source': 'header',
            'credit_account_source': 'detail',
            'amount_field': 'doanh_thu_thuc_te_nt',  # Trường tạm thời
            'detail_source': '_temp_chi_tiet',  # Nguồn chi tiết tạm thời
            'detail_conditions': {
                'doanh_thu_thuc_te_nt': {'gt': 0},
                'tk_dt': {'is_not_null': True},
                'tk_ck': {'is_null': True},
            },
            'canCreate': True,
        },
         {
            'journal_type': 'DTCK',  # Doanh thu
            'debit_account_field': 'tk',  # Tài khoản công nợ - DEBIT
            'credit_account_field': 'tk_dt',  # Tài khoản doanh thu - CREDIT
            'debit_account_source': 'header',
            'credit_account_source': 'detail',
            'amount_field': 'doanh_thu_thuc_te_nt',  # Trường tạm thời
            'detail_source': '_temp_chi_tiet',  # Nguồn chi tiết tạm thời
            'detail_conditions': {
                'doanh_thu_thuc_te_nt': {'gt': 0},
                'tk_dt': {'is_not_null': True},
                'tk_ck': {'is_not_null': True},
            },
            'canCreate': True,
        },
         {
            'journal_type': 'CK',  # Doanh thu
            'debit_account_field': 'tk_ck',  # Tài khoản công nợ - DEBIT
            'credit_account_field': 'tk',  # Tài khoản doanh thu - CREDIT
            'debit_account_source': 'detail',
            'credit_account_source': 'header',
            'amount_field': 'ck_nt',  # Trường tạm thời
            'detail_source': 'chi_tiet',  # Nguồn chi tiết tạm thời
            'detail_conditions': {
                'ck_nt': {'gt': 0},
                'tk_ck': {'is_not_null': True},
            },
            'canCreate': True,
        },
        {
            'journal_type': 'GIAVON',
            'debit_account_field': 'tk_gv',
            'credit_account_field': 'tk_vt',
            'debit_account_source': 'detail',
            'credit_account_source': 'detail',
            'amount_field': 'tien_nt',
            'detail_source': 'chi_tiet',
            'detail_conditions': {
                'tien_nt': {'gt': 0},
                'tk_gv': {'is_not_null': True},
                'tk_vt': {'is_not_null': True},
            },
            'canCreate': True,
        },
        {
            'journal_type': 'THUE',
            'debit_account_field': 'tk',
            'credit_account_field': 'tk_thue_co',
            'debit_account_source': 'header',
            'credit_account_source': 'detail',
            'amount_field': 'thue_nt',
            'detail_source': 'chi_tiet',
            'detail_conditions': {
                'thue_nt': {'gt': 0},
                'tk_thue_co': {'is_not_null': True},
            },
            'canCreate': True,
        },
    ]

    # ✅ PREDEFINED CONFIGURATION: Hóa đơn bán hàng stock mappings
    SALES_INVOICE_STOCK_CONFIG = [
        {
            'transaction_type': 'OUT',  # Xuất kho (bán hàng)
            'detail_source': 'chi_tiet',  # Related name
            'quantity_field': 'so_luong',  # Số lượng (detail) - correct field name
            'price_field': 'gia',  # Đơn giá (detail)
            'canCreate': True,  # Default: will be determined by conditions
            'detail_conditions': {
                'so_luong': {'gt': 0},
                'ma_vt': {'is_not_null': True},
                'ma_kho': {'is_not_null': True},
            },
        }
    ]

    def __init__(self):  # noqa: C901
        super().__init__()
        self.hoa_don_repository = HoaDonBanHangRepository(
            model_class=HoaDonBanHangModel
        )
        self.chi_tiet_service = ChiTietHoaDonBanHangService()
        self.thanh_toan_service = ThongTinThanhToanHoaDonBanHangService()
        self._cong_no_service = CongNoCreation()
        self._ton_kho_service = TonKhoCreation()

        # ✅ FIX: Add logger for delete operations
        self.logger = logging.getLogger(__name__)

        # ✅ ADD: PhieuXuatKho service for automatic warehouse export creation
        from django_ledger.services.ton_kho.xuat_kho_noi_bo.phieu_xuat_kho import (
            PhieuXuatKhoService,
        )

        self.phieu_xuat_kho_service = PhieuXuatKhoService()
        self.xoa_chung_tu = XoaChungTu()

    def _determine_accounting_mappings(
        self, hoa_don: HoaDonBanHangModel
    ) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định accounting mappings dựa trên status và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Conditional accounting entry creation dựa trên document status
        - Flexible business rules cho different scenarios
        - Support complex approval workflows

        Parameters
        ----------
        hoa_don : HoaDonBanHangModel
            Hóa đơn bán hàng để analyze

        Returns
        -------
        List[Dict[str, Any]]
            Danh sách accounting mappings với canCreate được set theo business logic
        """
        # Get base configuration
        mappings = self.SALES_INVOICE_ACCOUNTING_CONFIG.copy()

        # ✅ BUSINESS LOGIC: Determine canCreate based on invoice status and business rules

        # Example business logic - customize theo requirements
        if hasattr(hoa_don, 'status'):
            status = getattr(
                hoa_don, 'status', '1'
            )  # Default APPROVED for backward compatibility

            if status in ['0', '4']:
                # Draft/Pending invoices - không tạo bút toán
                for mapping in mappings:
                    mapping['canCreate'] = False

            elif status in ['1', '2', '5']:
                # Approved invoices - tạo đầy đủ bút toán
                for mapping in mappings:
                    mapping['canCreate'] = True

            else:
                # Unknown status - default behavior
                for mapping in mappings:
                    mapping['canCreate'] = True
        else:
            # No status field - default behavior (backward compatibility)
            for mapping in mappings:
                mapping['canCreate'] = True

        # ✅ NEW LOGIC: Disable CK/DTCK entries if loai_ck is 4
        # Get loai_ck from hoa_don, default to '4'
        loai_ck = getattr(hoa_don, 'loai_ck', '4')


        # If loai_ck is '4', disable 'CK' and 'DTCK' journal entries
        if loai_ck == 4:
            for mapping in mappings:
                if mapping.get('journal_type') == 'CK':
                    mapping['canCreate'] = False
        return mappings

    def _determine_stock_mappings(
        self, hoa_don: HoaDonBanHangModel
    ) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định stock mappings dựa trên status và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Conditional stock entry creation dựa trên document status
        - Flexible business rules cho different scenarios
        - Support complex approval workflows

        Parameters
        ----------
        hoa_don : HoaDonBanHangModel
            Hóa đơn bán hàng để analyze

        Returns
        -------
        List[Dict[str, Any]]
            Danh sách stock mappings với canCreate được set theo business logic
        """
        # Get base configuration
        mappings = self.SALES_INVOICE_STOCK_CONFIG.copy()

        # ✅ BUSINESS LOGIC: Determine canCreate based on invoice status and business rules

        # Example business logic - customize theo requirements
        if hasattr(hoa_don, 'status'):
            status = getattr(
                hoa_don, 'status', '1'
            )  # Default APPROVED for backward compatibility

            if status in ['0', '4']:
                # Draft or Cancelled invoices - không tạo stock entries
                for mapping in mappings:
                    mapping['canCreate'] = False

            elif status in ['1', '2', '3']:
                # Approved invoices - tạo stock entries
                for mapping in mappings:
                    mapping['canCreate'] = True

            else:
                # Unknown status - default behavior
                for mapping in mappings:
                    mapping['canCreate'] = True
        else:
            # No status field - default behavior (backward compatibility)
            for mapping in mappings:
                mapping['canCreate'] = True

        # ✅ ADDITIONAL BUSINESS RULES: Add more complex logic here

        # Example: Skip stock entry nếu không có chi tiết có ma_kho và ma_vt
        if hasattr(hoa_don, 'chi_tiet'):
            try:
                chi_tiet_list = hoa_don.chi_tiet.all()
                has_stock_items = any(
                    hasattr(ct, 'ma_kho')
                    and hasattr(ct, 'ma_vt')
                    and getattr(ct, 'ma_kho', None)
                    and getattr(ct, 'ma_vt', None)
                    for ct in chi_tiet_list
                )

                if not has_stock_items:
                    for mapping in mappings:
                        mapping['canCreate'] = False
            except:
                pass  # Ignore errors in business logic evaluation

        return mappings

    def get_by_id(
        self, uuid, entity_slug=None, user_model=None
    ) -> HoaDonBanHangModel:  # noqa: F811,
        """
        Retrieves a HoaDonBanHangModel by its UUID with related document UUIDs.

        Parameters
        ----------
        uuid : UUID
            The UUID of the HoaDonBanHangModel to retrieve.
        entity_slug : str, optional
            The entity slug to filter by, by default None.
        user_model : UserModel, optional
            The user model to check permissions, by default None.

        Returns
        -------
        HoaDonBanHangModel
            The retrieved HoaDonBanHangModel with additional attributes:
            - phieu_thu_uuids: Array of related PhieuThu UUIDs if exists (when pt_tao_yn=True and ma_httt='TMB')
            - giay_bao_co_uuids: Array of related GiayBaoCo UUIDs if exists (when pt_tao_yn=True and ma_httt='CKB')
            - phieu_xuat_kho_uuid: UUID of related PhieuXuatKho if exists (when tu_dong_tao=True)

        Raises
        ------
        ObjectDoesNotExist
            If the HoaDonBanHangModel with the given UUID does not exist.
        """
        hoa_don = self.hoa_don_repository.get_by_id(uuid, entity_slug, user_model)

        # Lookup related document UUIDs based on pt_tao_yn and ma_httt
        try:
            related_uuids = lookup_related_document_uuids_ban_hang(
                hoa_don_uuid=str(uuid),
                pt_tao_yn=hoa_don.pt_tao_yn,
                ma_httt=hoa_don.ma_httt,
            )
            # Add the related UUIDs as attributes to the model instance
            hoa_don.phieu_thu_uuids = related_uuids.get('phieu_thu_uuids', [])
            hoa_don.giay_bao_co_uuids = related_uuids.get('giay_bao_co_uuids', [])
        except Exception:
            # If lookup fails, set empty arrays
            hoa_don.phieu_thu_uuids = []
            hoa_don.giay_bao_co_uuids = []

        # Lookup PhieuXuatKho UUID based on nguon_hoa_don_id and tu_dong_tao = True
        try:
            phieu_xuat_kho_uuid = lookup_phieu_xuat_kho_uuid(hoa_don_uuid=str(uuid))
            hoa_don.phieu_xuat_kho_uuid = phieu_xuat_kho_uuid
        except Exception:
            # If lookup fails, set empty string
            hoa_don.phieu_xuat_kho_uuid = ''

        return hoa_don

    def list(
        self, entity_slug=None, user_model=None, **kwargs
    ) -> QuerySet:  # noqa: F811,
        """
        Lists HoaDonBanHangModel instances with optional filtering.

        Parameters
        ----------
        entity_slug : str, optional
            The entity slug to filter by, by default None.
        user_model : UserModel, optional
            The user model to check permissions, by default None.
        **kwargs : dict
            Additional filters to apply to the queryset.

        Returns
        -------
        QuerySet
            A queryset of HoaDonBanHangModel instances.
        """
        return self.hoa_don_repository.list(entity_slug, user_model, **kwargs)

    def list_with_related_uuids(
        self, entity_slug=None, user_model=None, **kwargs
    ) -> List[HoaDonBanHangModel]:
        """
        Lists HoaDonBanHangModel instances with related UUIDs mapped.

        This method efficiently maps related document UUIDs for multiple invoices
        without causing N+1 query problems.

        Parameters
        ----------
        entity_slug : str, optional
            The entity slug to filter by, by default None.
        user_model : UserModel, optional
            The user model to check permissions, by default None.
        **kwargs : dict
            Additional filters to apply to the queryset.

        Returns
        -------
        List[HoaDonBanHangModel]
            A list of HoaDonBanHangModel instances with related UUIDs mapped.
        """

        # Get the base queryset
        queryset = self.hoa_don_repository.list(entity_slug, user_model, **kwargs)

        # Convert to list to add attributes
        hoa_don_list = list(queryset)

        # Map related UUIDs for each invoice
        for hoa_don in hoa_don_list:
            try:
                related_uuids = lookup_related_document_uuids_ban_hang(
                    hoa_don_uuid=str(hoa_don.uuid),
                    pt_tao_yn=hoa_don.pt_tao_yn,
                    ma_httt=hoa_don.ma_httt,
                )

                # Add the related UUIDs as attributes to the model instance
                hoa_don.phieu_thu_uuids = related_uuids.get('phieu_thu_uuids', [])
                hoa_don.giay_bao_co_uuids = related_uuids.get('giay_bao_co_uuids', [])

                # Lookup PhieuXuatKho UUID
                try:
                    phieu_xuat_kho_uuid = lookup_phieu_xuat_kho_uuid(
                        hoa_don_uuid=str(hoa_don.uuid)
                    )
                    hoa_don.phieu_xuat_kho_uuid = phieu_xuat_kho_uuid
                except Exception:
                    hoa_don.phieu_xuat_kho_uuid = ''

            except Exception:
                # Set empty arrays on error
                hoa_don.phieu_thu_uuids = []
                hoa_don.giay_bao_co_uuids = []
                hoa_don.phieu_xuat_kho_uuid = ''

        return hoa_don_list

    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> HoaDonBanHangModel:  # noqa: C901
        """
        Create a new HoaDonBanHangModel instance

        Parameters
        ----------
        entity_slug : str
            The entity slug
        data : Dict[str, Any]
            The data for the new instance

        Returns
        -------
        HoaDonBanHangModel
            The created HoaDonBanHangModel instance
        """
        # Create the instance using repository
        return self.create_with_details(entity_slug=entity_slug, data=data)

    def update(
        self, entity_slug: str, uuid: str, data: Dict[str, Any]
    ) -> HoaDonBanHangModel:  # noqa: C901
        """
        Update an existing HoaDonBanHangModel instance

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : str
            The UUID of the instance to update
        data : Dict[str, Any]
            The data to update

        Returns
        -------
        HoaDonBanHangModel
            The updated HoaDonBanHangModel instance

        Raises
        ------
        HoaDonBanHangModel.DoesNotExist
            If the instance does not exist
        """
        # Update the instance using repository
        return self.update_with_details(entity_slug=entity_slug, uuid=uuid, data=data)

    @transaction.atomic
    def create_with_details(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> HoaDonBanHangModel:  # noqa: C901
        """
        ✅ ENHANCED: Tạo HoaDonBanHangModel mới với đầy đủ accounting và stock entries.

        Tự động tạo cả bút toán kế toán (accounting entries) và giao dịch kho (stock entries)
        cho hóa đơn bán hàng sử dụng unified services.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Atomic transaction cho cả accounting và inventory
        - Status-based business logic cho entry creation
        - Complete audit trail cho cả financial và inventory impacts
        - Transaction safety với automatic rollback

        Parameters
        ----------
        entity_slug : str
            The entity slug
        data : Dict[str, Any]
            The data for the new HoaDonBanHangModel instance including chi_tiet and thong_tin_thanh_toan

        Returns
        -------
        HoaDonBanHangModel
            The created HoaDonBanHangModel instance

        Raises
        ------
        Exception
            If there's an error during the creation process.
        """
        # Extract chi_tiet and thanh_toan data
        chi_tiet_data = data.pop('chi_tiet', [])
        thanh_toan_data = data.pop('thong_tin_thanh_toan', [])

        # Get entity model
        entity_model = get_object_or_404(EntityModel, slug=entity_slug)

        # Create the invoice
        hoa_don = self.hoa_don_repository.create(entity_model=entity_model, **data)

        # Create invoice details if provided
        created_chi_tiet_list = []
        if chi_tiet_data:
            for item_data in chi_tiet_data:
                created_item = self.chi_tiet_service.create(
                    parent=hoa_don, data=item_data
                )
                created_chi_tiet_list.append(created_item)

        # Create payment information if provided
        if thanh_toan_data:
            self.thanh_toan_service.bulk_create(parent=hoa_don, data=thanh_toan_data)

        # ✅ TEMP DETAILS PATTERN: Create a temporary, enriched detail source for accounting
        if created_chi_tiet_list:
            for ct in created_chi_tiet_list:
                # Convert string values to float for calculation
                tien_nt2_raw = getattr(ct, 'tien_nt2', 0) or 0
                ck_nt_raw = getattr(ct, 'ck_nt', 0) or 0

                # Handle string to float conversion
                try:
                    tien_nt2 = (
                        float(tien_nt2_raw)
                        if isinstance(tien_nt2_raw, str)
                        else tien_nt2_raw
                    )
                    ck_nt = (
                        float(ck_nt_raw)
                        if isinstance(ck_nt_raw, str)
                        else ck_nt_raw
                    )
                except (ValueError, TypeError):
                    tien_nt2 = 0.0
                    ck_nt = 0.0

                ct.doanh_thu_thuc_te_nt = tien_nt2 - ck_nt

            # Create a QuerySet-like wrapper
            class TempChiTietWrapper:
                def __init__(self, details_list):
                    self.details_list = details_list

                def all(self):
                    return self.details_list

                def filter(self, **_kwargs):
                    return self.details_list

            # Attach the temporary source to the instance
            setattr(
                hoa_don, '_temp_chi_tiet', TempChiTietWrapper(created_chi_tiet_list)
            )

        # ✅ ENHANCED: Tạo đầy đủ cả accounting và stock entries cho hóa đơn
        # Sử dụng unified method để đảm bảo consistency và atomic transaction
        try:
            complete_entries_created = self.create_complete_entries(hoa_don)
            if not complete_entries_created:
                # ✅ FAIL FAST: Đảm bảo data integrity
                raise Exception(
                    f"Failed to create complete accounting entries for invoice {hoa_don.uuid}"
                )
        except Exception as e:
            # ✅ FAIL FAST: Kích hoạt atomic rollback để đảm bảo data integrity
            self.logger.error(
                f"❌ Error creating complete entries for invoice {hoa_don.uuid}: {str(e)}, rolling back transaction"
            )

            # ✅ ENHANCED: Raise specific exception types for better API error handling
            from django.core.exceptions import ValidationError
            from django.db import IntegrityError

            if isinstance(
                e.__cause__, IntegrityError
            ) or "UNIQUE constraint failed" in str(e):
                raise IntegrityError(
                    f"Lỗi ràng buộc dữ liệu khi tạo hóa đơn {hoa_don.so_ct}: {str(e)}"
                ) from e
            elif (
                isinstance(e.__cause__, ValidationError)
                or "validation" in str(e).lower()
            ):
                raise ValidationError(
                    f"Lỗi validation khi tạo hóa đơn {hoa_don.so_ct}: {str(e)}"
                ) from e
            else:
                raise Exception(
                    f"Lỗi tạo complete entries hóa đơn {hoa_don.so_ct}: {str(e)}"
                ) from e

        return hoa_don

    @transaction.atomic
    def update_with_details(
        self, uuid: str, data: Dict[str, Any], entity_slug: str = None
    ) -> HoaDonBanHangModel:  # noqa: C901
        """
        ✅ ENHANCED: Cập nhật HoaDonBanHangModel với đầy đủ accounting và stock entries.

        Tự động cập nhật cả bút toán kế toán (accounting entries) và giao dịch kho (stock entries)
        cho hóa đơn bán hàng sử dụng unified services.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Atomic transaction cho cả accounting và inventory
        - Delete-and-recreate pattern để đảm bảo tính nhất quán
        - Smart recalculation chỉ khi cần thiết
        - Transaction safety với automatic rollback

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : str
            The UUID of the instance to update
        data : Dict[str, Any]
            The data for updating the HoaDonBanHangModel instance including chi_tiet and thong_tin_thanh_toan

        Returns
        -------
        HoaDonBanHangModel
            The updated HoaDonBanHangModel instance

        Raises
        ------
        Exception
            If there's an error during the update process.
        """
        # Extract chi_tiet and thanh_toan data
        chi_tiet_data = data.pop('chi_tiet', [])
        thanh_toan_data = data.pop('thong_tin_thanh_toan', [])

        # Update the invoice
        hoa_don = self.hoa_don_repository.update(uuid, **data)

        # Update invoice details if provided
        if chi_tiet_data:
            self.chi_tiet_service.bulk_update(parent=hoa_don, data=chi_tiet_data)

        # Update payment information if provided
        if thanh_toan_data:
            self.thanh_toan_service.bulk_update(parent=hoa_don, data=thanh_toan_data)

        # ✅ TEMP DETAILS PATTERN: Create a temporary, enriched detail source for accounting
        if hasattr(hoa_don, 'chi_tiet'):
            # Get the actual detail objects and enrich them in memory
            enriched_chi_tiet_list = list(hoa_don.chi_tiet.all())
            for ct in enriched_chi_tiet_list:
                # Convert string values to float for calculation
                tien_nt2_raw = getattr(ct, 'tien_nt2', 0) or 0
                ck_nt_raw = getattr(ct, 'ck_nt', 0) or 0

                # Handle string to float conversion
                try:
                    tien_nt2 = (
                        float(tien_nt2_raw)
                        if isinstance(tien_nt2_raw, str)
                        else tien_nt2_raw
                    )
                    ck_nt = (
                        float(ck_nt_raw)
                        if isinstance(ck_nt_raw, str)
                        else ck_nt_raw
                    )
                except (ValueError, TypeError):
                    tien_nt2 = 0.0
                    ck_nt = 0.0

                ct.doanh_thu_thuc_te_nt = tien_nt2 - ck_nt

            # Create a QuerySet-like wrapper
            class TempChiTietWrapper:
                def __init__(self, details_list):
                    self.details_list = details_list

                def all(self):
                    return self.details_list

                def filter(self, **_kwargs):
                    return self.details_list

            # Attach the temporary source to the instance
            setattr(
                hoa_don, '_temp_chi_tiet', TempChiTietWrapper(enriched_chi_tiet_list)
            )

        # ✅ ENHANCED: Cập nhật đầy đủ cả accounting và stock entries cho hóa đơn
        # Sử dụng unified method để đảm bảo consistency và atomic transaction
        try:
            complete_entries_updated = self.update_complete_entries(hoa_don)
            if not complete_entries_updated:
                # ✅ FAIL FAST: Đảm bảo data integrity
                raise Exception(
                    f"Failed to update complete accounting entries for invoice {hoa_don.uuid}"
                )
        except Exception as e:
            # ✅ FAIL FAST: Kích hoạt atomic rollback để đảm bảo data integrity
            self.logger.error(
                f"❌ Error updating complete entries for invoice {hoa_don.uuid}: {str(e)}, rolling back transaction"
            )
            raise Exception(
                f"Lỗi update complete entries hóa đơn {hoa_don.so_ct}: {str(e)}"
            ) from e
        # ✅ UPDATE PHIEU_XUAT_KHO: Always sync PhieuXuatKho based on px_yn flag
        # Check px_yn flag to determine action

        # ✅ CLEANUP WHEN FLAGS ARE FALSE (delegated to utils)
        # 1) If px_yn is False: delete related PhieuXuatKho by nguon_hoa_don_id
        if not getattr(hoa_don, 'px_yn', False):
            self.xoa_chung_tu.delete_phieu_xuat_kho(hoa_don)

        # 2) If pt_tao_yn is False: delete all PhieuThu and GiayBaoCo linked via detail.id_hd == invoice uuid
        if not getattr(hoa_don, 'pt_tao_yn', False):
            self.xoa_chung_tu.delete_phieu_thu_by_hoa_don(hoa_don)
            self.xoa_chung_tu.delete_giay_bao_co_by_hoa_don(hoa_don)

        return hoa_don

    @transaction.atomic
    def delete_invoice(self, uuid) -> bool:  # noqa: C901
        """
        Deletes a HoaDonBanHangModel instance with accounting recalculation.

        ✅ SIMPLE PATTERN: Same pattern as other services

        Parameters
        ----------
        uuid : UUID
            The UUID of the HoaDonBanHangModel to delete.

        Returns
        -------
        bool
            True if the deletion was successful, False otherwise.

        Raises
        ------
        ObjectDoesNotExist
            If the HoaDonBanHangModel with the given UUID does not exist.
        """
        self.logger.info(f"Deleting HoaDonBanHang {uuid}")

        # Get instance BEFORE deletion
        instance = self.hoa_don_repository.get_by_id(uuid)
        if not instance:
            self.logger.warning(f"HoaDonBanHang {uuid} not found")
            from django.core.exceptions import ObjectDoesNotExist

            raise ObjectDoesNotExist(f"HoaDonBanHang with UUID {uuid} does not exist")

        # ✅ DELETE RELATED DOCUMENTS: Always attempt to delete PX, PT, and GBC linked to this invoice
        try:
            self.xoa_chung_tu.delete_phieu_xuat_kho(instance)
        except Exception as e:
            self.logger.warning(f"⚠️ Could not delete related PhieuXuatKho: {str(e)}")

        try:
            self.xoa_chung_tu.delete_phieu_thu_by_hoa_don(instance)
        except Exception as e:
            self.logger.warning(f"⚠️ Could not delete related PhieuThu: {str(e)}")

        try:
            self.xoa_chung_tu.delete_giay_bao_co_by_hoa_don(instance)
        except Exception as e:
            self.logger.warning(f"⚠️ Could not delete related GiayBaoCo: {str(e)}")

        # ✅ ENHANCED PATTERN: Delete both accounting and stock entries
        if instance.ledger:
            try:
                # ✅ STEP 1: Delete stock entries first (before document gets cascade deleted)
                if hasattr(instance, 'stock_ledger') and instance.stock_ledger:
                    try:
                        self._ton_kho_service.delete_stock_accounting_entries(
                            source_document=instance, document_type="hóa đơn bán hàng"
                        )
                        self.logger.info(
                            f"Deleted stock entries for HoaDonBanHang {instance.so_ct}"
                        )
                    except Exception as stock_error:
                        self.logger.error(
                            f"Failed to delete stock entries: {str(stock_error)}"
                        )
                        # Continue with accounting deletion even if stock deletion fails

                # ✅ STEP 2: Delete accounting entries (this will cascade delete the document)
                self._cong_no_service.delete_document_accounting_entries(
                    source_document=instance
                )
                self.logger.info(
                    f"Deleted accounting entries for HoaDonBanHang {instance.so_ct}"
                )

                # ✅ FIX: Check if HoaDonBanHang still exists after ledger deletion
                # (CASCADE might have deleted it already)
                try:
                    # Refresh instance to check if it still exists
                    instance.refresh_from_db()
                    # If we reach here, instance still exists, so delete it
                    result = self.hoa_don_repository.delete(uuid)
                except HoaDonBanHangModel.DoesNotExist:
                    # Instance was already deleted by CASCADE, that's OK
                    self.logger.info(
                        f"HoaDonBanHang {uuid} was already deleted by CASCADE"
                    )
                    result = True

            except Exception as e:
                self.logger.error(f"Failed to delete accounting entries: {str(e)}")
                raise Exception(f"Failed to delete accounting entries: {str(e)}") from e
        else:
            # No ledger, just delete the HoaDonBanHang directly
            result = self.hoa_don_repository.delete(uuid)

        if result:
            self.logger.info(f"Successfully deleted HoaDonBanHang {uuid}")
        else:
            self.logger.error(f"Failed to delete HoaDonBanHang {uuid}")

        return result

    def get_invoice_related_data(self, hoa_don_id, data_type='both'):
        """
        Retrieves related data for a specific HoaDonBanHangModel.

        Parameters
        ----------
        hoa_don_id : UUID
            The UUID of the HoaDonBanHangModel to filter by.
        data_type : str, optional
            Type of data to retrieve: 'details', 'payments', or 'both' (default).

        Returns
        -------
        QuerySet or dict
            - If data_type='details': QuerySet of ChiTietHoaDonBanHangModel
            - If data_type='payments': QuerySet of ThongTinThanhToanHoaDonBanHangModel
            - If data_type='both': dict with 'details' and 'payments' keys
        """
        if data_type == 'details':
            return self.chi_tiet_service.get_by_hoa_don(hoa_don_id)
        elif data_type == 'payments':
            return self.thanh_toan_service.get_by_hoa_don(hoa_don_id)
        elif data_type == 'both':
            return {
                'details': self.chi_tiet_service.get_by_hoa_don(hoa_don_id),
                'payments': self.thanh_toan_service.get_by_hoa_don(hoa_don_id),
            }
        else:
            raise ValueError(
                f"Invalid data_type: {data_type}. Must be 'details', 'payments', or 'both'."
            )

    def filter_invoices(
        self, customer_id=None, status=None, entity_slug=None, user_model=None, **kwargs
    ) -> QuerySet:  # noqa: C901
        """
        Retrieves HoaDonBanHangModel instances with flexible filtering.

        Parameters
        ----------
        customer_id : UUID, optional
            The UUID of the customer to filter by.
        status : str, optional
            The status to filter by.
        entity_slug : str, optional
            The entity slug to filter by, by default None.
        user_model : UserModel, optional
            The user model to check permissions, by default None.
        **kwargs : dict
            Additional filters to apply.

        Returns
        -------
        QuerySet
            A queryset of HoaDonBanHangModel instances matching the filters.
        """

        filters = {}

        if customer_id:
            filters['ma_kh'] = customer_id
        if status:
            filters['status'] = status

        # Add any additional filters
        filters.update(kwargs)

        return self.hoa_don_repository.list(entity_slug, user_model, **filters)

    def get_unprocessed_invoices(
        self,
        entity_slug: str,
        user_model,
        ngay_ct1: date,
        ngay_ct2: date,
        unit_id: str,
        ma_gd: str,
        auto_generated_receipt_invoice_ids: Optional[List[str]] = None,
        ma_kh: Optional[str] = None,
    ) -> QuerySet:
        """
        Get invoices that do NOT have corresponding auto-generated warehouse receipts.

        This method delegates to the repository layer for data access.

        Parameters
        ----------
        entity_slug : str
            The entity slug to filter by.
        user_model : UserModel
            The user model to check permissions.
        ngay_ct1 : date
            Start date for filtering invoices.
        ngay_ct2 : date
            End date for filtering invoices.
        unit_id : str
            Business unit ID to filter by.
        ma_gd : str
            Transaction type (BH, NB, XK, PO).
        auto_generated_receipt_invoice_ids : Optional[List[str]]
            List of invoice UUIDs that already have auto-generated receipts.
            If provided, these invoices will be excluded from the result.
        ma_kh : Optional[str]
            Customer code to filter by (optional).

        Returns
        -------
        QuerySet
            A queryset of unprocessed HoaDonBanHangModel instances.
        """
        return self.hoa_don_repository.get_unprocessed_invoices(
            entity_slug=entity_slug,
            user_model=user_model,
            ngay_ct1=ngay_ct1,
            ngay_ct2=ngay_ct2,
            unit_id=unit_id,
            ma_gd=ma_gd,
            auto_generated_receipt_invoice_ids=auto_generated_receipt_invoice_ids,
            ma_kh=ma_kh,
        )

    def create_debt_entry(self, hoa_don: HoaDonBanHangModel) -> bool:
        """
        ✅ ENHANCED: Tạo bút toán công nợ (journal entry) cho hóa đơn bán hàng.

        Sử dụng Unified Accounting Service thay vì ButToanHoaDonBanHangUtils riêng lẻ.
        ERP Expert Logic - 20 năm kinh nghiệm:
        - Flexible account mapping configuration
        - Support cả doanh thu và thuế trong một lần gọi
        - Consistent audit trail management
        - Transaction safety với rollback

        Parameters
        ----------
        hoa_don : HoaDonBanHangModel
            Hóa đơn bán hàng để tạo bút toán công nợ.

        Returns
        -------
        bool
            True nếu tạo bút toán thành công, False nếu thất bại.
        """
        try:
            # ✅ BUSINESS LOGIC: Determine accounting mappings based on document status
            account_mappings = self._determine_accounting_mappings(hoa_don)

            return self._cong_no_service.create_document_accounting_entries(
                source_document=hoa_don,
                document_type="hóa đơn bán hàng",
                account_mappings=account_mappings,
            )

        except Exception as e:
            # ✅ THROW EXCEPTION: Fail fast để dễ debug và đảm bảo consistency
            raise Exception(
                f"Lỗi tạo bút toán hóa đơn bán hàng {hoa_don.so_ct}: {str(e)}"
            ) from e

    def update_debt_entry(self, hoa_don: HoaDonBanHangModel) -> bool:
        """
        ✅ ENHANCED: Cập nhật bút toán công nợ (journal entry) cho hóa đơn bán hàng.

        Sử dụng Unified Accounting Service với delete-and-recreate pattern.
        ERP Expert Logic - 20 năm kinh nghiệm:
        - Smart recalculation chỉ khi cần thiết
        - Affected accounts tracking
        - Proper audit trail management
        - Transaction safety với rollback

        Parameters
        ----------
        hoa_don : HoaDonBanHangModel
            Hóa đơn bán hàng để cập nhật bút toán công nợ.

        Returns
        -------
        bool
            True nếu cập nhật bút toán thành công, False nếu thất bại.
        """
        try:

            # ✅ BUSINESS LOGIC: Determine accounting mappings based on document status
            account_mappings = self._determine_accounting_mappings(hoa_don)

            return self._cong_no_service.update_document_accounting_entries(
                source_document=hoa_don,
                document_type="hóa đơn bán hàng",
                account_mappings=account_mappings,
            )

        except Exception as e:
            # ✅ THROW EXCEPTION: Fail fast để dễ debug và đảm bảo consistency
            raise Exception(
                f"Lỗi cập nhật bút toán hóa đơn bán hàng {hoa_don.so_ct}: {str(e)}"
            ) from e

    def create_stock_entry(self, hoa_don: HoaDonBanHangModel) -> bool:
        """
        ✅ ENHANCED: Tạo stock entries cho hóa đơn bán hàng.

        Sử dụng Unified Inventory Service để tạo stock transactions.
        ERP Expert Logic - 20 năm kinh nghiệm:
        - Flexible stock mapping configuration
        - Support xuất kho cho bán hàng
        - Consistent stock audit trail management
        - Transaction safety với rollback

        Parameters
        ----------
        hoa_don : HoaDonBanHangModel
            Hóa đơn bán hàng để tạo stock entries.

        Returns
        -------
        bool
            True nếu tạo stock entries thành công, False nếu thất bại.
        """
        try:
            # ✅ BUSINESS LOGIC: Determine stock mappings based on document status
            stock_mappings = self._determine_stock_mappings(hoa_don)

            return self._ton_kho_service.create_document_stock_entries(
                source_document=hoa_don,
                document_type="hóa đơn bán hàng",
                stock_mappings=stock_mappings,
            )

        except Exception as e:
            # ✅ THROW EXCEPTION: Fail fast để dễ debug và đảm bảo consistency
            raise Exception(
                f"Lỗi tạo stock entries hóa đơn bán hàng {hoa_don.so_ct}: {str(e)}"
            ) from e

    def update_stock_entry(self, hoa_don: HoaDonBanHangModel) -> bool:
        """
        ✅ ENHANCED: Cập nhật stock entries cho hóa đơn bán hàng.

        Sử dụng Unified Inventory Service với delete-and-recreate pattern.
        ERP Expert Logic - 20 năm kinh nghiệm:
        - Smart stock recalculation chỉ khi cần thiết
        - Affected products tracking
        - Proper stock audit trail management
        - Transaction safety với rollback

        Parameters
        ----------
        hoa_don : HoaDonBanHangModel
            Hóa đơn bán hàng để cập nhật stock entries.

        Returns
        -------
        bool
            True nếu cập nhật stock entries thành công, False nếu thất bại.
        """
        try:
            # ✅ BUSINESS LOGIC: Determine stock mappings based on document status
            stock_mappings = self._determine_stock_mappings(hoa_don)

            return self._ton_kho_service.update_document_stock_entries(
                source_document=hoa_don,
                document_type="hóa đơn bán hàng",
                stock_mappings=stock_mappings,
            )

        except Exception as e:
            # ✅ THROW EXCEPTION: Fail fast để dễ debug và đảm bảo consistency
            raise Exception(
                f"Lỗi cập nhật stock entries hóa đơn bán hàng {hoa_don.so_ct}: {str(e)}"
            ) from e

    @transaction.atomic
    def create_complete_entries(self, hoa_don: HoaDonBanHangModel) -> bool:
        """
        ✅ UNIFIED METHOD: Tạo đầy đủ cả accounting và stock entries cho hóa đơn bán hàng.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Atomic transaction cho cả accounting và inventory
        - Consistent business logic application
        - Proper error handling và rollback
        - Complete audit trail

        Parameters
        ----------
        hoa_don : HoaDonBanHangModel
            Hóa đơn bán hàng để tạo đầy đủ entries.

        Returns
        -------
        bool
            True nếu tạo đầy đủ entries thành công.

        Raises
        ------
        Exception
            Nếu có lỗi trong quá trình tạo accounting hoặc stock entries
        """
        try:
            # ✅ STEP 1: Create accounting entries
            accounting_success = self.create_debt_entry(hoa_don)
            if not accounting_success:
                raise Exception("Failed to create accounting entries")

            # ✅ STEP 2: Create stock entries
            stock_success = self.create_stock_entry(hoa_don)
            if not stock_success:
                raise Exception("Failed to create stock entries")

            return True

        except Exception as e:
            # ✅ ATOMIC ROLLBACK: Transaction will rollback both accounting and stock entries
            raise Exception(
                f"Lỗi tạo complete entries hóa đơn bán hàng {hoa_don.so_ct}: {str(e)}"
            ) from e

    @transaction.atomic
    def update_complete_entries(self, hoa_don: HoaDonBanHangModel) -> bool:
        """
        ✅ UNIFIED METHOD: Cập nhật đầy đủ cả accounting và stock entries cho hóa đơn bán hàng.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Atomic transaction cho cả accounting và inventory updates
        - Consistent business logic application
        - Proper error handling và rollback
        - Complete audit trail maintenance

        Parameters
        ----------
        hoa_don : HoaDonBanHangModel
            Hóa đơn bán hàng để cập nhật đầy đủ entries.

        Returns
        -------
        bool
            True nếu cập nhật đầy đủ entries thành công.

        Raises
        ------
        Exception
            Nếu có lỗi trong quá trình cập nhật accounting hoặc stock entries
        """
        try:
            # ✅ STEP 1: Update accounting entries
            accounting_success = self.update_debt_entry(hoa_don)
            if not accounting_success:
                raise Exception("Failed to update accounting entries")

            # # ✅ STEP 2: Update stock entries
            stock_success = self.update_stock_entry(hoa_don)
            if not stock_success:
                raise Exception("Failed to update stock entries")

            return True

        except Exception as e:
            # ✅ ATOMIC ROLLBACK: Transaction will rollback both accounting and stock updates
            raise Exception(
                f"Lỗi cập nhật complete entries hóa đơn bán hàng {hoa_don.so_ct}: {str(e)}"
            ) from e
