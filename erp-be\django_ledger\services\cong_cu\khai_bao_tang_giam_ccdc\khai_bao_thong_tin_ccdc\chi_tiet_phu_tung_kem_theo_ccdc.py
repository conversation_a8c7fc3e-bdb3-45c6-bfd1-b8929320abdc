"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietPhuTungKemTheoCCDC (Tool Accessory Detail) service implementation.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc.chi_tiet_phu_tung_kem_theo_ccdc import (
    ChiTietPhuTungKemTheoCCDCModel,  # noqa: F401
)
from django_ledger.models.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc.khai_bao_thong_tin_ccdc import (
    KhaiBaoThongTinCCDCModel,  # noqa: F401
)
from django_ledger.repositories.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc import (
    ChiTietPhuTungKemTheoCCDCRepository,  # noqa: F401
)
from django_ledger.services.base import BaseService  # noqa: F401


class ChiTietPhuTungKemTheoCCDCService(BaseService):
    """
    Service class for ChiTietPhuTungKemTheoCCDCModel.
    Handles business logic for the model.
    """

    def __init__(self):  # noqa: C901
        self.repository = ChiTietPhuTungKemTheoCCDCRepository()
        super().__init__()

    def get_by_id(
        self, uuid: Union[str, UUID]
    ) -> Optional[ChiTietPhuTungKemTheoCCDCModel]:  # noqa: C901
        """
        Retrieves a ChiTietPhuTungKemTheoCCDCModel by its UUID.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhuTungKemTheoCCDCModel to retrieve.

        Returns
        -------
        Optional[ChiTietPhuTungKemTheoCCDCModel]
            The ChiTietPhuTungKemTheoCCDCModel with the given UUID, or None if not found.  # noqa: E501
        """
        return self.repository.get_by_id(uuid=uuid)

    def list_for_khai_bao_thong_tin_ccdc(
        self, khai_bao_thong_tin_ccdc_id: Union[str, UUID]
    ) -> QuerySet:  # noqa: C901
        """
        Returns ChiTietPhuTungKemTheoCCDCModel instances for a specific KhaiBaoThongTinCCDCModel.  # noqa: E501

        Parameters
        ----------
        khai_bao_thong_tin_ccdc_id : Union[str, UUID]
            The UUID of the KhaiBaoThongTinCCDCModel to filter by.

        Returns
        -------
        QuerySet
            QuerySet of ChiTietPhuTungKemTheoCCDCModel instances for the specified KhaiBaoThongTinCCDCModel.  # noqa: E501
        """
        return self.repository.list_for_khai_bao_thong_tin_ccdc(
            khai_bao_thong_tin_ccdc_id=khai_bao_thong_tin_ccdc_id
        )

    @transaction.atomic
    def create(
        self, parent: KhaiBaoThongTinCCDCModel, data: Dict[str, Any]
    ) -> ChiTietPhuTungKemTheoCCDCModel:  # noqa: C901
        """
        Creates a new ChiTietPhuTungKemTheoCCDCModel instance.

        Parameters
        ----------
        parent : KhaiBaoThongTinCCDCModel
            The parent KhaiBaoThongTinCCDCModel instance.
        data : Dict[str, Any]
            The data for creating the ChiTietPhuTungKemTheoCCDCModel.

        Returns
        -------
        ChiTietPhuTungKemTheoCCDCModel
            The created ChiTietPhuTungKemTheoCCDCModel instance.
        """
        return self.repository.create(
            khai_bao_thong_tin_ccdc_id=parent.uuid,
            data=data,
        )

    @transaction.atomic
    def bulk_create(
        self, parent: KhaiBaoThongTinCCDCModel, data: List[Dict[str, Any]]
    ) -> None:  # noqa: C901
        """
        Creates multiple ChiTietPhuTungKemTheoCCDCModel instances.

        Parameters
        ----------
        parent : KhaiBaoThongTinCCDCModel
            The parent KhaiBaoThongTinCCDCModel instance.
        data : List[Dict[str, Any]]
            The list of data for creating the ChiTietPhuTungKemTheoCCDCModel instances.

        Returns
        -------
        None
        """
        for item in data:
            self.repository.create(khai_bao_thong_tin_ccdc_id=parent.uuid, data=item)
        return None

    @transaction.atomic
    def bulk_update(
        self, parent: KhaiBaoThongTinCCDCModel, data: List[Dict[str, Any]]
    ) -> None:  # noqa: C901
        """
        Update multiple ChiTietPhuTungKemTheoCCDCModel instances for a specific parent

        Parameters
        ----------
        parent : KhaiBaoThongTinCCDCModel
            The parent KhaiBaoThongTinCCDCModel instance
        data : List[Dict[str, Any]]
            The list of data for the instances to update

        Returns
        -------
        None
        """
        existing_chi_tiet = parent.chi_tiet_phu_tung_kem_theo_ccdc.all()
        existing_chi_tiet_dict = {str(ct.uuid): ct for ct in existing_chi_tiet}

        updated_uuids = set()

        for item in data:
            item_uuid = item.get("uuid")

            if item_uuid and item_uuid in existing_chi_tiet_dict:
                updated_uuids.add(item_uuid)
                item_data = item.copy()
                if "uuid" in item_data:
                    item_data.pop("uuid")
                self.update(uuid=item_uuid, data=item_data)
            else:
                item_data = item.copy()
                if "uuid" in item_data:
                    item_data.pop("uuid")
                self.create(parent=parent, data=item_data)

        for uuid_str in existing_chi_tiet_dict:
            if uuid_str not in updated_uuids:
                self.delete(uuid=uuid_str)

    @transaction.atomic
    def update(
        self, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[ChiTietPhuTungKemTheoCCDCModel]:  # noqa: C901
        """
        Updates an existing ChiTietPhuTungKemTheoCCDCModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhuTungKemTheoCCDCModel to update.
        data : Dict[str, Any]
            The data for updating the ChiTietPhuTungKemTheoCCDCModel.

        Returns
        -------
        Optional[ChiTietPhuTungKemTheoCCDCModel]
            The updated ChiTietPhuTungKemTheoCCDCModel instance, or None if not found.
        """
        return self.repository.update(uuid=uuid, data=data)

    @transaction.atomic
    def delete(self, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes an existing ChiTietPhuTungKemTheoCCDCModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhuTungKemTheoCCDCModel to delete.

        Returns
        -------
        bool
            True if the instance was deleted, False otherwise.
        """
        return self.repository.delete(uuid=uuid)
