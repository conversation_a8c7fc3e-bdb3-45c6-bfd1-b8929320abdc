"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietPhieuNhapHangBanTraLai (Customer Return Receipt Detail) Model
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ChiTietPhieuNhapHangBanTraLaiModelQueryset(models.QuerySet):
    """
    A custom defined QuerySet for the ChiTietPhieuNhapHangBanTraLaiModel.
    """

    pass


class ChiTietPhieuNhapHangBanTraLaiModelManager(models.Manager):
    """
    A custom defined ChiTietPhieuNhapHangBanTraLaiModelManager that will act as an interface to handling the DB queries to the  # noqa: E501
    ChiTietPhieuNhapHangBanTraLaiModel.
    """

    def for_phieu_nhap(self, phieu_nhap_uuid):  # noqa: C901
        """
        Fetches a QuerySet of ChiTietPhieuNhapHangBanTraLaiModels for a specific PhieuNhapHangBanTraLaiModel.  # noqa: E501

        Parameters
        __________
        phieu_nhap_uuid: UUID
            The UUID of the PhieuNhapHangBanTraLaiModel to filter by.
        """
        qs = self.get_queryset()
        return qs.filter(phieu_nhap_hang_ban_tra_lai_id__exact=phieu_nhap_uuid)


class ChiTietPhieuNhapHangBanTraLaiModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiTietPhieuNhapHangBanTraLaiModel database will inherit from.  # noqa: E501
    The ChiTietPhieuNhapHangBanTraLaiModel inherits functionality from the following MixIns:  # noqa: E501

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501

    phieu_nhap_hang_ban_tra_lai: PhieuNhapHangBanTraLaiModel
        The PhieuNhapHangBanTraLaiModel this detail belongs to.

    line: int
        The line number of this detail.

    ma_vt: VatTuModel
        The product/item being returned.

    ten_vt: str
        The name of the product/item.

    dvt: DonViTinhModel
        The unit of measure.

    ten_dvt: str
        The name of the unit of measure.

    ma_kho: KhoHangModel
        The warehouse.

    ten_kho: str
        The name of the warehouse.

    ma_lo: LoModel
        The lot/batch.

    ten_lo: str
        The name of the lot/batch.

    lo_yn: bool
        Whether lot management is enabled.

    ma_vi_tri: ViTriModel
        The location.

    ten_vi_tri: str
        The name of the location.

    vi_tri_yn: bool
        Whether location management is enabled.

    he_so: Decimal
        The conversion factor.

    qc_yn: bool
        Whether quality control is enabled.

    so_luong: Decimal
        The quantity being returned.

    ct_km: Decimal
        Promotional discount.

    pn_tb: Decimal
        Decimal part.

    gia_nt: Decimal
        Foreign currency price.

    tien_nt: Decimal
        Foreign currency amount.

    gia_nt1: Decimal
        Foreign currency price 1.

    gia_nt2: Decimal
        Foreign currency price 2.

    tien_nt2: Decimal
        Foreign currency amount 2.

    don_gia: Decimal
        The unit price.

    tien: Decimal
        The total amount.

    gia: Decimal
        The price.

    gia1: Decimal
        Price 1.

    gia2: Decimal
        Price 2.

    tien2: Decimal
        Amount 2.

    tl_ck: Decimal
        The discount rate.

    ck_nt: Decimal
        Foreign currency discount.

    ck: Decimal
        The discount amount.

    ma_thue: ThueModel
        The tax.

    tk_thue_no: AccountModel
        The tax debit account.

    ten_tk_thue_no: str
        The name of the tax debit account.

    thue_suat: Decimal
        The tax rate.

    thue_nt: Decimal
        Foreign currency tax.

    thue: Decimal
        The tax amount.

    tk_dt: AccountModel
        The revenue account.

    ten_tk_tl: str
        The name of the rate account.

    tk_gv: AccountModel
        The cost of goods sold account.

    ten_tk_gv: str
        The name of the cost of goods sold account.

    sua_tk_vt: bool
        Whether to edit the material account.

    tk_vt: AccountModel
        The material account.

    ten_tk_vt: str
        The name of the material account.

    tk_ck: AccountModel
        The discount account.

    ten_tk_ck: str
        The name of the discount account.

    tk_km: AccountModel
        The promotion account.

    ten_tk_km: str
        The name of the promotion account.

    ma_bp: BoPhanModel
        The department.

    ma_vv: VanVanModel
        The transportation.

    ma_hd: HopDongModel
        The contract.

    ma_dtt: DoiTuongThueModel
        The tax subject.

    ma_ku: KheUocModel
        The loan/contract.

    ma_phi: PhiModel
        The fee.

    ma_sp: SanPhamModel
        The product.

    ma_lsx: LenhSanXuatModel
        The production order.

    ma_cp0: ChiPhiModel
        The cost.

    id_px: str
        The export receipt ID.

    so_ct_px: str
        The export receipt document number.

    line_px: int
        The export receipt line.

    id_hd: str
        The invoice ID.

    so_ct_hd: str
        The invoice document number.

    line_hd: int
        The invoice line.

    id_dh: str
        The order ID.

    line_dh: int
        The order line.
    """

    uuid = models.UUIDField(default=uuid4, editable=False, primary_key=True)
    phieu_nhap_hang_ban_tra_lai = models.ForeignKey(
        'django_ledger.PhieuNhapHangBanTraLaiModel',
        on_delete=models.CASCADE,
        verbose_name=_('Phiếu nhập hàng bán trả lại'),
        related_name='chi_tiet_phieu_nhap_hang_ban_tra_lai',
    )
    line = models.IntegerField(
        default=1, verbose_name=_('Dòng'), help_text=_('Số dòng')
    )

    # Thông tin sản phẩm
    ma_vt = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã VT'),
        help_text=_('Mã vật tư'),
    )
    ten_vt = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=_('Tên VT'),
        help_text=_('Tên vật tư'),
    )

    dvt = models.ForeignKey(
        'django_ledger.DonViTinhModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('ĐVT'),
        help_text=_('Đơn vị tính'),
    )
    ten_dvt = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=_('Tên ĐVT'),
        help_text=_('Tên đơn vị tính'),
    )

    # Thông tin số lượng và giá
    so_luong = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_('Số lượng'),
        help_text=_('Số lượng'),
    )
    ct_km = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Chiết khấu khuyến mãi'),
        help_text=_('Chiết khấu khuyến mãi'),
    )
    pn_tb = models.BooleanField(
        default=False,
        verbose_name=_('Phần nguyên thập phân'),
        help_text=_('Phần nguyên thập phân'),
    )
    gia_nt = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Giá ngoại tệ'),
        help_text=_('Giá ngoại tệ'),
    )
    tien_nt = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Tiền ngoại tệ'),
        help_text=_('Tiền ngoại tệ'),
    )
    gia_nt1 = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Giá ngoại tệ 1'),
        help_text=_('Giá ngoại tệ 1'),
    )
    gia_nt2 = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Giá ngoại tệ 2'),
        help_text=_('Giá ngoại tệ 2'),
    )
    tien_nt2 = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Tiền ngoại tệ 2'),
        help_text=_('Tiền ngoại tệ 2'),
    )
    don_gia = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Đơn giá'),
        help_text=_('Đơn giá'),
    )
    tien = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Tiền'),
        help_text=_('Tiền'),
    )
    gia = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Giá'),
        help_text=_('Giá'),
    )
    gia1 = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Giá 1'),
        help_text=_('Giá 1'),
    )
    gia2 = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Giá 2'),
        help_text=_('Giá 2'),
    )
    tien2 = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Tiền 2'),
        help_text=_('Tiền 2'),
    )

    # Thông tin chiết khấu
    tl_ck = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        default=0,
        verbose_name=_('Tỷ lệ CK'),
        help_text=_('Tỷ lệ chiết khấu'),
    )
    ck_nt = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Chiết khấu ngoại tệ'),
        help_text=_('Chiết khấu ngoại tệ'),
    )
    ck = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Chiết khấu'),
        help_text=_('Chiết khấu'),
    )

    # Thông tin thuế
    ma_thue = models.ForeignKey(
        'django_ledger.TaxModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã thuế'),
        help_text=_('Mã thuế'),
    )
    tk_thue_no = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='chi_tiet_phieu_nhap_hang_ban_tra_lai_tk_thue_no',
        verbose_name=_('TK thuế nợ'),
        help_text=_('Tài khoản thuế nợ'),
    )

    thue_suat = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Thuế suất'),
        help_text=_('Thuế suất'),
    )
    thue_nt = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Thuế ngoại tệ'),
        help_text=_('Thuế ngoại tệ'),
    )
    thue = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name=_('Thuế'),
        help_text=_('Thuế'),
    )

    # Thông tin kho
    ma_kho = models.ForeignKey(
        'django_ledger.KhoHangModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã kho'),
        help_text=_('Mã kho'),
    )
    ten_kho = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=_('Tên kho'),
        help_text=_('Tên kho'),
    )

    ma_lo = models.ForeignKey(
        'django_ledger.LoModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã lô'),
        help_text=_('Mã lô'),
    )
    ten_lo = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=_('Tên lô'),
        help_text=_('Tên lô'),
    )

    lo_yn = models.BooleanField(
        default=False,
        verbose_name=_('Quản lý lô'),
        help_text=_('Có quản lý theo lô hay không'),
    )
    ma_vi_tri = models.ForeignKey(
        'django_ledger.ViTriModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã vị trí'),
        help_text=_('Mã vị trí'),
    )
    ten_vi_tri = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=_('Tên vị trí'),
        help_text=_('Tên vị trí'),
    )

    vi_tri_yn = models.BooleanField(
        default=False,
        verbose_name=_('Quản lý vị trí'),
        help_text=_('Có quản lý theo vị trí hay không'),
    )
    he_so = models.DecimalField(
        max_digits=15,
        decimal_places=6,
        default=1,
        verbose_name=_('Hệ số'),
        help_text=_('Hệ số quy đổi'),
    )
    qc_yn = models.BooleanField(
        default=False,
        verbose_name=_('Quản lý chất lượng'),
        help_text=_('Có quản lý chất lượng hay không'),
    )

    # Thông tin kế toán
    tk_dt = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='chi_tiet_phieu_nhap_hang_ban_tra_lai_tk_dt',
        verbose_name=_('TK doanh thu'),
        help_text=_('Tài khoản doanh thu'),
    )

    tk_gv = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='chi_tiet_phieu_nhap_hang_ban_tra_lai_tk_gv',
        verbose_name=_('TK giá vốn'),
        help_text=_('Tài khoản giá vốn'),
    )

    sua_tk_vt = models.BooleanField(
        default=False,
        verbose_name=_('Sửa TK vật tư'),
        help_text=_('Có sửa tài khoản vật tư hay không'),
    )
    tk_vt = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='chi_tiet_phieu_nhap_hang_ban_tra_lai_tk_vt',
        verbose_name=_('TK vật tư'),
        help_text=_('Tài khoản vật tư'),
    )

    tk_ck = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='chi_tiet_phieu_nhap_hang_ban_tra_lai_tk_ck',
        verbose_name=_('TK chiết khấu'),
        help_text=_('Tài khoản chiết khấu'),
    )

    tk_km = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='chi_tiet_phieu_nhap_hang_ban_tra_lai_tk_km',
        verbose_name=_('TK khuyến mãi'),
        help_text=_('Tài khoản khuyến mãi'),
    )

    # Business dimensions
    ma_bp = models.ForeignKey(
        'django_ledger.BoPhanModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã bộ phận'),
        help_text=_('Mã bộ phận'),
    )
    ma_vv = models.ForeignKey(
        'django_ledger.VuViecModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã vận vụ'),
        help_text=_('Mã vận vụ'),
    )
    ma_hd = models.ForeignKey(
        'django_ledger.ContractModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã hợp đồng'),
        help_text=_('Mã hợp đồng'),
    )
    ma_dtt = models.ForeignKey(
        'django_ledger.DotThanhToanModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã đợt thanh toán'),
        help_text=_('Mã đợt thanh toán'),
    )
    ma_ku = models.ForeignKey(
        'django_ledger.KheUocModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã khế ước'),
        help_text=_('Mã khế ước'),
    )
    ma_phi = models.ForeignKey(
        'django_ledger.PhiModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã phí'),
        help_text=_('Mã phí'),
    )
    ma_sp = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='chi_tiet_phieu_nhap_hang_ban_tra_lai_sp',
        verbose_name=_('Mã sản phẩm'),
        help_text=_('Mã sản phẩm'),
    )
    ma_lsx = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Mã lệnh sản xuất'),
        help_text=_('Mã lệnh sản xuất'),
    )
    ma_cp0 = models.ForeignKey(
        'django_ledger.ChiPhiModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Mã chi phí'),
        help_text=_('Mã chi phí'),
    )

    # Document references (CharField instead of ForeignKey as per memory)
    id_px = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('ID phiếu xuất'),
        help_text=_('ID phiếu xuất'),
    )
    so_ct_px = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Số CT phiếu xuất'),
        help_text=_('Số chứng từ phiếu xuất'),
    )
    line_px = models.IntegerField(
        null=True,
        blank=True,
        verbose_name=_('Dòng phiếu xuất'),
        help_text=_('Dòng phiếu xuất'),
    )
    id_hd = models.CharField(
        max_length=36,
        verbose_name=_("ID hóa đơn"),
        help_text=_("UUID of HoaDonBanHangModel"),
        blank=True,
        null=True,
    )
    so_ct_hd = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('Số CT hóa đơn'),
        help_text=_('Số chứng từ hóa đơn'),
    )
    line_hd = models.CharField(
        max_length=36,
        verbose_name=_("Line hóa đơn"),
        help_text=_("UUID of ChiTietHoaDonBanHangModel"),
        blank=True,
        null=True,
    )
    id_dh = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_('ID đơn hàng'),
        help_text=_('ID đơn hàng'),
    )
    line_dh = models.IntegerField(
        null=True,
        blank=True,
        verbose_name=_('Dòng đơn hàng'),
        help_text=_('Dòng đơn hàng'),
    )

    objects = ChiTietPhieuNhapHangBanTraLaiModelManager.from_queryset(
        ChiTietPhieuNhapHangBanTraLaiModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _('Chi Tiết Phiếu Nhập Hàng Bán Trả Lại')
        verbose_name_plural = _('Chi Tiết Phiếu Nhập Hàng Bán Trả Lại')
        indexes = [
            models.Index(fields=['phieu_nhap_hang_ban_tra_lai']),
            models.Index(fields=['ma_vt']),
            models.Index(fields=['ma_kho']),
            models.Index(fields=['ma_lo']),
            models.Index(fields=['ma_vi_tri']),
            models.Index(fields=['ma_thue']),
            models.Index(fields=['ma_bp']),
            models.Index(fields=['ma_vv']),
            models.Index(fields=['ma_hd']),
        ]

    def __str__(self):  # noqa: C901
        vt_name = self.ma_vt.ten_vt if self.ma_vt else 'N/A'
        return f'{self.phieu_nhap_hang_ban_tra_lai.so_ct} - {self.line}: {vt_name}'


class ChiTietPhieuNhapHangBanTraLaiModel(ChiTietPhieuNhapHangBanTraLaiModelAbstract):
    """
    Base Customer Return Receipt Detail Model Implementation
    """

    class Meta(ChiTietPhieuNhapHangBanTraLaiModelAbstract.Meta):
        abstract = False
        db_table = 'chi_tiet_phieu_nhap_hang_ban_tra_lai'
