"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the HoaDonMuaHangTrongNuocService, which handles business logic
for the HoaDonMuaHangTrongNuocModel.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import models, transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    HoaDonMuaHangTrongNuocModel,
)
from django_ledger.repositories.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    HoaDonMuaHangTrongNuocRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,
from django_ledger.services.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_phi_chi_tiet_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ChiPhiChiTietHoaDonMuaHangTrongNuocService,
)
from django_ledger.services.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_phi_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ChiPhiHoaDonMuaHangTrongNuocService,
)
from django_ledger.services.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_tiet_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ChiTietHoaDonMuaHangTrongNuocService,
)
from django_ledger.services.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.thue_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ThueHoaDonMuaHangTrongNuocService,
)
from django_ledger.utils_new.debt_management.cong_no_creation import CongNoCreation
from django_ledger.utils_new.lookup_uuid_utils import (
    lookup_phieu_nhap_kho_uuid,
    lookup_related_document_uuids_mua_hang,
)
from django_ledger.utils_new.xoa_chung_tu import XoaChungTu


class HoaDonMuaHangTrongNuocService(BaseService):
    """
    Service class for HoaDonMuaHangTrongNuocModel.
    Handles business logic for the model with unified accounting service.

    ✅ ENHANCED: Integrated with CongNoCreation for standardized accounting
    ERP Expert - 20 năm kinh nghiệm
    """

    # ✅ PREDEFINED CONFIGURATION: Purchase invoice accounting mappings
    # Cross-reference journal types with enhanced conditional logic
    PURCHASE_INVOICE_ACCOUNTING_CONFIG = [
        # 1. CONGNO: Basic debt entries (no chi_phi array)
        {
            'journal_type': 'CONGNO',
            'debit_account_field': 'tk_vt',  # Detail debit account
            'credit_account_field': 'tk',  # Header credit account
            'debit_account_source': 'detail',  # From chi_tiet
            'credit_account_source': 'header',  # From header
            'amount_field': 'tien_nt0',  # Amount field
            'detail_source': 'chi_tiet_hoa_don',  # Related name
            'detail_conditions': {
                'tien_nt0': {'gt': 0},  # tien_nt0 > 0
                'tk_vt': {'is_not_null': True},  # tk_vt IS NOT NULL
            },
            'header_conditions': {'has_chi_phi': {'eq': False}},  # NO chi_phi array
            'canCreate': True,
        },
        # 2. CONGNOP: Debt entries with expenses (has chi_phi array)
        {
            'journal_type': 'CONGNOP',
            'debit_account_field': 'tk_vt',  # Detail debit account
            'credit_account_field': 'tk',  # Header credit account
            'debit_account_source': 'detail',  # From chi_tiet
            'credit_account_source': 'header',  # From header
            'amount_field': 'tien_nt0',  # Amount field
            'detail_source': 'chi_tiet_hoa_don',  # Related name
            'detail_conditions': {
                'tien_nt0': {'gt': 0},  # tien_nt0 > 0
                'tk_vt': {'is_not_null': True},  # tk_vt IS NOT NULL
            },
            'header_conditions': {'has_chi_phi': {'eq': True}},  # HAS chi_phi array
            'canCreate': True,
        },
        # 3. THUE: Tax entries
        {
            'journal_type': 'THUE',
            'debit_account_field': 'tk_thue_no',  # Detail tax debit account
            'credit_account_field': 'tk',  # Header credit account
            'debit_account_source': 'detail',  # From thue
            'credit_account_source': 'header',  # From header
            'amount_field': 't_thue_nt',  # Tax amount
            'detail_source': 'thue_hoa_don',  # Related name
            'detail_conditions': {
                't_thue_nt': {'gt': 0},  # t_thue > 0
                'tk_thue_no': {'is_not_null': True},  # tk_thue_no IS NOT NULL
            },
            'canCreate': True,
        },
        # 4. PHI: Cross-reference expense allocation using temporary merged detail source
        {
            'journal_type': 'PHI',
            'debit_account_field': 'debit_account',  # Merged debit account
            'credit_account_field': 'credit_account',  # Merged credit account
            'debit_account_source': 'detail',  # From merged detail
            'credit_account_source': 'detail',  # From merged detail
            'amount_field': 'amount',  # Merged amount
            'detail_source': '_temp_phi_details',  # Temporary merged detail source
            'detail_conditions': {'amount': {'gt': 0}},  # amount > 0
            'canCreate': True,
        },
    ]

    def __init__(self):  # noqa: C901
        super().__init__()
        self.repository = HoaDonMuaHangTrongNuocRepository()
        self.chi_tiet_service = ChiTietHoaDonMuaHangTrongNuocService()
        self.chi_phi_service = ChiPhiHoaDonMuaHangTrongNuocService()
        self.chi_phi_chi_tiet_service = ChiPhiChiTietHoaDonMuaHangTrongNuocService()
        self.thue_service = ThueHoaDonMuaHangTrongNuocService()
        self.xoa_chung_tu = XoaChungTu()

        # Logger
        import logging

        self.logger = logging.getLogger(__name__)

        # ✅ UNIFIED ACCOUNTING: Initialize CongNoCreation service
        self._cong_no_service = CongNoCreation()

    def _determine_accounting_mappings(
        self, hoa_don: HoaDonMuaHangTrongNuocModel
    ) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Determine accounting mappings based on business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - Cross-reference journal types for purchase invoice accounting
        - Conditional creation based on data availability
        - Support for 4 types: CONGNO, CONGNOP, THUE, PHI
        - PHI type uses special cross-reference logic

        Parameters
        ----------
        hoa_don : HoaDonMuaHangTrongNuocModel
            Purchase invoice to analyze

        Returns
        -------
        List[Dict[str, Any]]
            List of accounting mappings with conditional logic
        """
        mappings = []

        # ✅ BUSINESS LOGIC: Only create entries when header tk is available
        if not hoa_don.tk:
            return mappings

        # Check if has chi_phi array for CONGNO vs CONGNOP logic
        has_chi_phi = hoa_don.chi_phi_hoa_don.exists()

        # ✅ BUSINESS LOGIC: Filter mappings based on business rules
        for config in self.PURCHASE_INVOICE_ACCOUNTING_CONFIG:
            mapping = config.copy()

            # Handle header conditions
            if 'header_conditions' in config:
                header_conditions = config['header_conditions']

                # Check has_chi_phi condition
                if 'has_chi_phi' in header_conditions:
                    expected_has_chi_phi = header_conditions['has_chi_phi']['eq']
                    if has_chi_phi != expected_has_chi_phi:
                        continue  # Skip this mapping

            # ✅ CONDITIONAL CREATION: Will be validated at detail level
            # CongNoCreation will check conditions for each detail item
            mapping['canCreate'] = True
            mappings.append(mapping)
        return mappings

    def _create_temp_phi_details(self, hoa_don: HoaDonMuaHangTrongNuocModel):
        """
        ✅ MERGE LOGIC: Tạo temporary detail source gộp 3 sources lại thành 1.

        Gộp chi_tiet + chi_phi + chi_phi_chi_tiet thành 1 temporary detail source
        để CongNoCreation có thể xử lý như bình thường.

        Parameters
        ----------
        hoa_don : HoaDonMuaHangTrongNuocModel
            The invoice instance

        Returns
        -------
        List[Dict[str, Any]]
            Temporary detail source với debit_account, credit_account, amount
        """
        temp_details = []

        # Get related data
        chi_tiet_items = hoa_don.chi_tiet_hoa_don.all()
        chi_phi_items = hoa_don.chi_phi_hoa_don.all()
        chi_phi_chi_tiet_items = hoa_don.chi_phi_chi_tiet_hoa_don.all()

        # Build mapping dictionaries
        chi_tiet_map = {}  # ma_vt -> tk_vt
        for chi_tiet in chi_tiet_items:
            if chi_tiet.ma_vt and chi_tiet.tk_vt:
                ma_vt_key = (
                    chi_tiet.ma_vt.uuid
                    if hasattr(chi_tiet.ma_vt, 'uuid')
                    else str(chi_tiet.ma_vt)
                )
                chi_tiet_map[ma_vt_key] = chi_tiet.tk_vt

        chi_phi_map = {}  # ma_cp -> tk
        for chi_phi in chi_phi_items:
            if chi_phi.ma_cp and chi_phi.tk:
                ma_cp_key = (
                    chi_phi.ma_cp.uuid
                    if hasattr(chi_phi.ma_cp, 'uuid')
                    else str(chi_phi.ma_cp)
                )
                chi_phi_map[ma_cp_key] = chi_phi.tk

        # Create merged entries from chi_phi_chi_tiet allocations

        for allocation in chi_phi_chi_tiet_items:
            if (
                allocation.tien_cp
                and allocation.tien_cp > 0
                and allocation.ma_vt
                and allocation.ma_cp
            ):

                ma_vt_key = (
                    allocation.ma_vt.uuid
                    if hasattr(allocation.ma_vt, 'uuid')
                    else str(allocation.ma_vt)
                )
                ma_cp_key = (
                    allocation.ma_cp.uuid
                    if hasattr(allocation.ma_cp, 'uuid')
                    else str(allocation.ma_cp)
                )

                debit_account = chi_tiet_map.get(ma_vt_key)
                credit_account = chi_phi_map.get(ma_cp_key)

                if debit_account and credit_account:
                    # Tạo temporary detail entry
                    temp_detail = type(
                        'TempPhiDetail',
                        (),
                        {
                            'debit_account': debit_account,
                            'credit_account': credit_account,
                            'amount': allocation.tien_cp,
                            'description': f'PHI - {allocation.ma_vt} -> {allocation.ma_cp}',
                            'source_allocation': allocation,
                        },
                    )()

                    temp_details.append(temp_detail)
        return temp_details

    def get_accounting_configuration(self) -> List[Dict[str, Any]]:
        """
        Get accounting configuration for purchase invoice.

        Returns
        -------
        List[Dict[str, Any]]
            List of accounting mapping configurations
        """
        return self.PURCHASE_INVOICE_ACCOUNTING_CONFIG.copy()

    def get_by_id(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> Optional[HoaDonMuaHangTrongNuocModel]:  # noqa: C901
        """
        Retrieves a HoaDonMuaHangTrongNuocModel by its UUID with related document UUIDs.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the HoaDonMuaHangTrongNuocModel to retrieve.

        Returns
        -------
        Optional[HoaDonMuaHangTrongNuocModel]
            The HoaDonMuaHangTrongNuocModel with the given UUID, or None if not found.
            The model will have additional attributes:
            - phieu_chi_uuids: Array of related PhieuChi UUIDs if exists (when pc_tao_yn=True and ma_httt='TMM')
            - giay_bao_no_uuids: Array of related GiayBaoNo UUIDs if exists (when pc_tao_yn=True and ma_httt='CKM')
        """
        hoa_don = self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

        if not hoa_don:
            return None

        # Lookup related document UUIDs based on pc_tao_yn and ma_httt
        try:
            related_uuids = lookup_related_document_uuids_mua_hang(
                hoa_don_uuid=str(uuid),
                pc_tao_yn=hoa_don.pc_tao_yn,
                ma_httt=hoa_don.ma_httt,
            )
            # Add the related UUIDs as attributes to the model instance
            hoa_don.phieu_chi_uuids = related_uuids.get('phieu_chi_uuids', [])
            hoa_don.giay_bao_no_uuids = related_uuids.get('giay_bao_no_uuids', [])
        except Exception:
            # If lookup fails, set empty arrays
            hoa_don.phieu_chi_uuids = []
            hoa_don.giay_bao_no_uuids = []

        # Lookup PhieuNhapKho UUID based on nguon_hoa_don_id and tu_dong_tao = True
        try:
            phieu_nhap_kho_uuid = lookup_phieu_nhap_kho_uuid(hoa_don_uuid=str(uuid))
            hoa_don.phieu_nhap_kho_uuid = phieu_nhap_kho_uuid
        except Exception:
            # If lookup fails, set empty string
            hoa_don.phieu_nhap_kho_uuid = ''

        return hoa_don

    def get_with_details(
        self, entity_slug: str, uuid: UUID
    ) -> Optional[HoaDonMuaHangTrongNuocModel]:
        """
        Get a HoaDonMuaHangTrongNuoc with its related details.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : UUID
            The UUID of the HoaDonMuaHangTrongNuoc

        Returns
        -------
        Optional[HoaDonMuaHangTrongNuocModel]
            The HoaDonMuaHangTrongNuoc instance with related details or None if not found
        """
        try:
            # Get HoaDonMuaHangTrongNuoc with prefetched chi_tiet and thue
            hoa_don = (
                HoaDonMuaHangTrongNuocModel.objects.filter(
                    entity_model__slug=entity_slug
                )
                .prefetch_related('chi_tiet_hoa_don', 'thue_hoa_don')
                .get(uuid=uuid)
            )

            # Add chi_tiet and thue as list for compatibility
            hoa_don.chi_tiet_hoa_don_list = list(hoa_don.chi_tiet_hoa_don.all())
            hoa_don.thue_hoa_don_list = list(hoa_don.thue_hoa_don.all())

            return hoa_don

        except HoaDonMuaHangTrongNuocModel.DoesNotExist:
            return None

    def get_all(self, entity_slug: str) -> QuerySet:  # noqa: C901
        """
        Get all HoaDonMuaHangTrongNuocModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.

        Returns
        -------
        QuerySet
            A QuerySet of HoaDonMuaHangTrongNuocModel instances.
        """
        return self.repository.get_by_entity_slug(entity_slug)

    def list(self, entity_slug: str, **kwargs) -> QuerySet:  # noqa: C901
        """
        Lists HoaDonMuaHangTrongNuocModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of HoaDonMuaHangTrongNuocModel instances.
        """
        return self.repository.list(entity_slug=entity_slug, **kwargs)

    def list_with_related_uuids(
        self, entity_slug: str, user_model=None, **kwargs
    ) -> List[HoaDonMuaHangTrongNuocModel]:
        """
        Lists HoaDonMuaHangTrongNuocModel instances with related UUIDs mapped.

        This method efficiently maps related document UUIDs for multiple invoices
        without causing N+1 query problems.

        Parameters
        ----------
        entity_slug : str
            The entity slug to filter by.
        user_model : UserModel, optional
            The user model to check permissions, by default None.
        **kwargs : dict
            Additional filters to apply to the queryset.

        Returns
        -------
        List[HoaDonMuaHangTrongNuocModel]
            A list of HoaDonMuaHangTrongNuocModel instances with related UUIDs mapped.
        """

        # Get the base queryset
        queryset = self.repository.list(entity_slug=entity_slug, **kwargs)

        # Convert to list to add attributes
        hoa_don_list = list(queryset)
        # Map related UUIDs for each invoice
        for hoa_don in hoa_don_list:
            try:
                related_uuids = lookup_related_document_uuids_mua_hang(
                    hoa_don_uuid=str(hoa_don.uuid),
                    pc_tao_yn=hoa_don.pc_tao_yn,
                    ma_httt=hoa_don.ma_httt,
                )

                # Add the related UUIDs as attributes to the model instance
                hoa_don.phieu_chi_uuids = related_uuids.get('phieu_chi_uuids', [])
                hoa_don.giay_bao_no_uuids = related_uuids.get('giay_bao_no_uuids', [])

                # Lookup PhieuNhapKho UUID
                try:
                    from django_ledger.utils_new.lookup_uuid_utils import (
                        lookup_phieu_nhap_kho_uuid,
                    )

                    phieu_nhap_kho_uuid = lookup_phieu_nhap_kho_uuid(
                        hoa_don_uuid=str(hoa_don.uuid)
                    )
                    hoa_don.phieu_nhap_kho_uuid = phieu_nhap_kho_uuid
                except Exception:
                    hoa_don.phieu_nhap_kho_uuid = ''

            except Exception as e:
                # Set empty arrays on error
                hoa_don.phieu_chi_uuids = []
                hoa_don.giay_bao_no_uuids = []
                hoa_don.phieu_nhap_kho_uuid = ''

        return hoa_don_list

    @transaction.atomic
    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> HoaDonMuaHangTrongNuocModel:  # noqa: C901
        """
        Creates a new HoaDonMuaHangTrongNuocModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new HoaDonMuaHangTrongNuocModel.

        Returns
        -------
        HoaDonMuaHangTrongNuocModel
            The created HoaDonMuaHangTrongNuocModel instance.
        """
        # Set default values
        self._set_default_values(data)

        # Remove fields that don't exist in the model
        self._remove_non_model_fields(data)

        # Get entity_model from entity_slug
        from django_ledger.models import EntityModel

        try:
            entity_model = EntityModel.objects.get(slug=entity_slug)
        except EntityModel.DoesNotExist:
            raise ValueError(f"Entity with slug '{entity_slug}' not found")

        # Process related data
        chi_tiet_hoa_don = data.pop('chi_tiet_hoa_don', [])
        chi_phi_hoa_don = data.pop('chi_phi_hoa_don', [])
        chi_phi_chi_tiet_hoa_don = data.pop('chi_phi_chi_tiet_hoa_don', [])
        thue_hoa_don = data.pop('thue_hoa_don', [])

        # Add entity_model to data
        data['entity_model'] = entity_model

        # Convert UUIDs to model instances
        data = self.repository.convert_uuids_to_model_instances(data)

        # Create the HoaDonMuaHangTrongNuocModel instance
        instance = self.repository.create(entity_slug=entity_slug, data=data)

        # Process related data using correct method signatures
        if chi_tiet_hoa_don:
            # Add hoa_don reference to each chi_tiet_data
            for chi_tiet_data in chi_tiet_hoa_don:
                chi_tiet_data['hoa_don'] = instance
                self.chi_tiet_service.create(data=chi_tiet_data)

        if chi_phi_hoa_don:
            # Add hoa_don reference to each chi_phi_data
            for chi_phi_data in chi_phi_hoa_don:
                chi_phi_data['hoa_don'] = instance
                self.chi_phi_service.create(data=chi_phi_data)

        if chi_phi_chi_tiet_hoa_don:
            # Add hoa_don reference to each chi_phi_chi_tiet_data
            for chi_phi_chi_tiet_data in chi_phi_chi_tiet_hoa_don:
                chi_phi_chi_tiet_data['hoa_don'] = instance
                self.chi_phi_chi_tiet_service.create(data=chi_phi_chi_tiet_data)

        if thue_hoa_don:
            # Add hoa_don reference to each thue_data
            for thue_data in thue_hoa_don:
                thue_data['hoa_don'] = instance
                self.thue_service.create(data=thue_data)

        # ✅ STEP 9: Refresh instance and create accounting entries
        try:
            # Refresh the instance from DB to get all related details
            instance = self.repository.get_by_id(
                entity_slug=entity_slug, uuid=instance.uuid
            )

            # ✅ MERGE LOGIC: Create temporary detail source for PHI entries
            temp_phi_details = self._create_temp_phi_details(instance)

            # Create QuerySet-like wrapper
            class TempPhiDetailsWrapper:
                def __init__(self, details_list):
                    self.details_list = details_list

                def all(self):
                    return self.details_list

                def __iter__(self):
                    return iter(self.details_list)

                def count(self):
                    return len(self.details_list)

                def filter(self, **kwargs):  # noqa: ARG002
                    return self.details_list

            # Attach temporary detail source to instance
            setattr(
                instance, '_temp_phi_details', TempPhiDetailsWrapper(temp_phi_details)
            )

            # Get all mappings
            account_mappings = self._determine_accounting_mappings(instance)

            # ✅ SINGLE CALL: Create all entries via CongNoCreation
            self._cong_no_service.create_document_accounting_entries(
                source_document=instance,
                document_type="hóa đơn mua hàng trong nước",
                account_mappings=account_mappings,
            )

        except Exception as e:
            # ✅ FAIL FAST: Trigger atomic rollback for data integrity
            raise Exception(
                f"Lỗi tạo bút toán hóa đơn mua hàng trong nước {instance.so_ct}: {str(e)}"
            ) from e

        return instance

    def get_invoice_details(self, instance):
        """
        Get invoice details including nested data.
        """
        return {
            'chi_tiet_hoa_don': instance.chi_tiet_hoa_don.all(),
            'chi_phi_hoa_don': instance.chi_phi_hoa_don.all(),
            'chi_phi_chi_tiet_hoa_don': instance.chi_phi_chi_tiet_hoa_don.all(),
            'thue_hoa_don': instance.thue_hoa_don.all(),
        }

    def _set_default_values(self, data: Dict[str, Any]) -> None:
        """
        Set default values for HoaDonMuaHangTrongNuocModel fields.

        Parameters
        ----------
        data : Dict[str, Any]
            The data dictionary to set defaults for.
        """
        # Set default boolean values
        data.setdefault('hdmh_yn', True)
        data.setdefault('pn_yn', False)
        data.setdefault('pc_tao_yn', False)
        data.setdefault('xt_yn', False)
        data.setdefault('transfer_yn', False)

        # Set default numeric values
        data.setdefault('ck_tl_nt', 0)
        data.setdefault('ty_gia', 1.0)
        data.setdefault('i_so_ct', 1)

        # Set default string values
        data.setdefault('status', '1')
        data.setdefault('so_ct0', '')
        data.setdefault('so_ct2', '')
        data.setdefault('so_pn', '0')
        data.setdefault('i_so_pn', '')
        data.setdefault('loai_ck', '2')

        # Set default date values
        if 'ngay_ct' in data:
            data.setdefault('ngay_lct', data['ngay_ct'])
            data.setdefault('ngay_ct0', data['ngay_ct'])
            data.setdefault('ngay_pn', data['ngay_ct'])

    def _remove_non_model_fields(self, data: Dict[str, Any]) -> None:
        """
        Remove fields that don't exist in the HoaDonMuaHangTrongNuocModel.

        Parameters
        ----------
        data : Dict[str, Any]
            The data dictionary to clean.
        """
        # Fields that don't exist in the model and should be removed
        non_model_fields = [
            'i_so_px',
            'so_px',
            # Note: Total fields (t_so_luong, t_cp_nt, t_cp, t_thue_nt, t_thue, t_tien_nt, t_tien, t_tt_nt, t_tt, t_ck_nt, t_ck)
            # are actual model fields and should NOT be removed
            'ma_dc',
            'ma_ptvc',
            'ma_ptgh',
            'ma_kh9',
            'so_ct_hddt0',
            'ngay_ct_hddt0',
            'so_ct2_hddt0',
        ]

        for field in non_model_fields:
            data.pop(field, None)

    @transaction.atomic
    def update(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[HoaDonMuaHangTrongNuocModel]:  # noqa: C901
        """
        ✅ ENHANCED: Updates a HoaDonMuaHangTrongNuocModel instance with accounting integration.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the HoaDonMuaHangTrongNuocModel to update.
        data : Dict[str, Any]
            The data to update the HoaDonMuaHangTrongNuocModel with.

        Returns
        -------
        Optional[HoaDonMuaHangTrongNuocModel]
            The updated HoaDonMuaHangTrongNuocModel instance, or None if not found.
        """

        # Get current instance to check for changes
        current_instance = self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not current_instance:
            return None

        # Process related data
        chi_tiet_hoa_don = data.pop('chi_tiet_hoa_don', [])
        chi_phi_hoa_don = data.pop('chi_phi_hoa_don', [])
        chi_phi_chi_tiet_hoa_don = data.pop('chi_phi_chi_tiet_hoa_don', [])
        thue_hoa_don = data.pop('thue_hoa_don', [])

        # Update the instance
        instance = self.repository.update(entity_slug=entity_slug, uuid=uuid, data=data)

        if not instance:
            return None

        # Process related data only if they are provided in the payload
        if 'chi_tiet_hoa_don' in data:
            self.chi_tiet_service.bulk_update(
                hoa_don_id=uuid, data_list=chi_tiet_hoa_don
            )

        if 'chi_phi_hoa_don' in data:
            self.chi_phi_service.bulk_update(hoa_don_id=uuid, data_list=chi_phi_hoa_don)

        if 'chi_phi_chi_tiet_hoa_don' in data:
            self.chi_phi_chi_tiet_service.bulk_update(
                hoa_don_id=uuid, data_list=chi_phi_chi_tiet_hoa_don
            )

        if 'thue_hoa_don' in data:
            self.thue_service.bulk_update(hoa_don_id=uuid, data_list=thue_hoa_don)

        # ✅ REFRESH INSTANCE: Refresh the instance from DB to get all related details for accounting
        instance = self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

        # ✅ UNIFIED ACCOUNTING: Create or update accounting entries
        try:
            # ✅ MERGE LOGIC: Create temporary detail source for PHI entries
            temp_phi_details = self._create_temp_phi_details(instance)

            # Create QuerySet-like wrapper for temp_phi_details
            class TempPhiDetailsWrapper:
                def __init__(self, details_list):
                    self.details_list = details_list

                def all(self):
                    return self.details_list

                def __iter__(self):
                    return iter(self.details_list)

                def count(self):
                    return len(self.details_list)

                def filter(self, **kwargs):  # noqa: ARG002
                    return self.details_list

            # Attach temporary detail source to instance
            setattr(
                instance, '_temp_phi_details', TempPhiDetailsWrapper(temp_phi_details)
            )

            # Get all mappings (including PHI with temp detail source)
            account_mappings = self._determine_accounting_mappings(instance)

            if instance.ledger:
                # UPDATE existing entries
                self._cong_no_service.update_document_accounting_entries(
                    source_document=instance,
                    document_type="hóa đơn mua hàng trong nước",
                    account_mappings=account_mappings,
                )
            else:
                # CREATE new entries if no ledger exists
                self._cong_no_service.create_document_accounting_entries(
                    source_document=instance,
                    document_type="hóa đơn mua hàng trong nước",
                    account_mappings=account_mappings,
                )

        except Exception as e:
            # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
            raise Exception(
                f"Failed to create/update accounting entry for HoaDonMuaHang {instance.so_ct}: {str(e)}"
            ) from e

        # ✅ CLEANUP WHEN FLAGS ARE FALSE (delegated to utils)
        # 1) If pn_yn is False: delete related PhieuNhapKho by nguon_hoa_don_id
        if not getattr(instance, 'pn_yn', False):
            self.xoa_chung_tu.delete_phieu_nhap_kho(instance)

        # 2) If pc_tao_yn is False: delete all PhieuChi and GiayBaoNo linked via detail.id_hd == invoice uuid
        if not getattr(instance, 'pc_tao_yn', False):
            self.xoa_chung_tu.delete_phieu_chi_by_hoa_don(instance)
            self.xoa_chung_tu.delete_giay_bao_no_by_hoa_don(instance)

        return instance

    @transaction.atomic
    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        ✅ ENHANCED: Deletes a HoaDonMuaHangTrongNuocModel instance with accounting cleanup.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the HoaDonMuaHangTrongNuocModel to delete.

        Returns
        -------
        bool
            True if the HoaDonMuaHangTrongNuocModel was deleted, False otherwise.
        """
        # Get the instance first
        instance = self.repository.get_by_uuid(uuid)
        if not instance:
            return False

        # ✅ UNIFIED ACCOUNTING: Delete accounting entries if ledger exists
        if instance.ledger:
            try:
                # ✅ DELETE ACCOUNTING: Delete tất cả entries qua CongNoCreation
                self._cong_no_service.delete_document_accounting_entries(
                    source_document=instance
                )

            except Exception as e:
                # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                # to maintain data consistency between HoaDon and accounting entries
                raise Exception(
                    f"Failed to delete accounting entry for HoaDonMuaHang {instance.so_ct}: {str(e)}"
                ) from e

        # ✅ DELETE RELATED DOCUMENTS: Always attempt to delete PN, PC, and GBN linked to this invoice
        try:
            self.xoa_chung_tu.delete_phieu_nhap_kho(instance)
        except Exception as e:
            self.logger.warning(f"⚠️ Could not delete related PhieuNhapKho: {str(e)}")

        try:
            self.xoa_chung_tu.delete_phieu_chi_by_hoa_don(instance)
        except Exception as e:
            self.logger.warning(f"⚠️ Could not delete related PhieuChi: {str(e)}")

        try:
            self.xoa_chung_tu.delete_giay_bao_no_by_hoa_don(instance)
        except Exception as e:
            self.logger.warning(f"⚠️ Could not delete related GiayBaoNo: {str(e)}")

        # Delete the invoice instance
        return self.repository.delete(entity_slug=entity_slug, uuid=uuid)

    @transaction.atomic
    def update_with_details(
        self, instance, data: Dict[str, Any]
    ) -> Optional[HoaDonMuaHangTrongNuocModel]:  # noqa: C901
        """
        Updates a HoaDonMuaHangTrongNuocModel instance with related details.

        Parameters
        ----------
        instance : HoaDonMuaHangTrongNuocModel
            The instance to update.
        data : Dict[str, Any]
            The data to update the HoaDonMuaHangTrongNuocModel with, including related details.

        Returns
        -------
        Optional[HoaDonMuaHangTrongNuocModel]
            The updated HoaDonMuaHangTrongNuocModel instance, or None if not found.
        """

        # Extract related data
        chi_tiet_hoa_don = data.pop('chi_tiet_hoa_don', [])
        chi_phi_hoa_don = data.pop('chi_phi_hoa_don', [])
        chi_phi_chi_tiet_hoa_don = data.pop('chi_phi_chi_tiet_hoa_don', [])
        thue_hoa_don = data.pop('thue_hoa_don', [])

        # Update main instance using entity_slug and uuid
        entity_slug = instance.entity_model.slug

        instance = self.repository.update(
            entity_slug=entity_slug, uuid=instance.uuid, data=data
        )

        # Update related data theo logic phieu_xuat_kho
        if chi_tiet_hoa_don:
            # Convert OrderedDict to regular dict and preserve UUID if exists
            chi_tiet_data_list = []
            for item in chi_tiet_hoa_don:
                if hasattr(item, 'items'):
                    item_dict = dict(item)
                    chi_tiet_data_list.append(item_dict)
                else:
                    chi_tiet_data_list.append(item)

            self.chi_tiet_service.bulk_update(
                hoa_don_id=instance.uuid, data_list=chi_tiet_data_list
            )

        if chi_phi_hoa_don:
            chi_phi_data_list = []
            for item in chi_phi_hoa_don:
                if hasattr(item, 'items'):
                    item_dict = dict(item)
                    chi_phi_data_list.append(item_dict)
                else:
                    chi_phi_data_list.append(item)

            self.chi_phi_service.bulk_update(
                hoa_don_id=instance.uuid, data_list=chi_phi_data_list
            )

        if chi_phi_chi_tiet_hoa_don:
            chi_phi_chi_tiet_data_list = []
            for item in chi_phi_chi_tiet_hoa_don:
                if hasattr(item, 'items'):
                    item_dict = dict(item)
                    chi_phi_chi_tiet_data_list.append(item_dict)
                else:
                    chi_phi_chi_tiet_data_list.append(item)

            self.chi_phi_chi_tiet_service.bulk_update(
                hoa_don_id=instance.uuid, data_list=chi_phi_chi_tiet_data_list
            )

        if thue_hoa_don:
            thue_data_list = []
            for item in thue_hoa_don:
                if hasattr(item, 'items'):
                    item_dict = dict(item)
                    thue_data_list.append(item_dict)
                else:
                    thue_data_list.append(item)

            self.thue_service.bulk_update(
                hoa_don_id=instance.uuid, data_list=thue_data_list
            )

        # ✅ UNIFIED ACCOUNTING: Create or update accounting entries
        try:
            # ✅ MERGE LOGIC: Create temporary detail source for PHI entries
            temp_phi_details = self._create_temp_phi_details(instance)

            # Create QuerySet-like wrapper for temp_phi_details
            class TempPhiDetailsWrapper:
                def __init__(self, details_list):
                    self.details_list = details_list

                def all(self):
                    return self.details_list

                def __iter__(self):
                    return iter(self.details_list)

                def count(self):
                    return len(self.details_list)

                def filter(self, **kwargs):  # noqa: ARG002
                    return self.details_list

            # Attach temporary detail source to instance
            setattr(
                instance, '_temp_phi_details', TempPhiDetailsWrapper(temp_phi_details)
            )

            # Get all mappings (including PHI with temp detail source)
            account_mappings = self._determine_accounting_mappings(instance)

            if instance.ledger:
                # UPDATE existing entries
                self._cong_no_service.update_document_accounting_entries(
                    source_document=instance,
                    document_type="hóa đơn mua hàng trong nước",
                    account_mappings=account_mappings,
                )
            else:
                # CREATE new entries if no ledger exists
                self._cong_no_service.create_document_accounting_entries(
                    source_document=instance,
                    document_type="hóa đơn mua hàng trong nước",
                    account_mappings=account_mappings,
                )

        except Exception as e:
            # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
            raise Exception(
                f"Failed to create/update accounting entry for HoaDonMuaHang {instance.so_ct}: {str(e)}"
            ) from e

        # ✅ CLEANUP WHEN FLAGS ARE FALSE (delegated to utils)
        # 1) If pn_yn is False: delete related PhieuNhapKho by nguon_hoa_don_id
        if not getattr(instance, 'pn_yn', False):
            self.xoa_chung_tu.delete_phieu_nhap_kho(instance)

        # 2) If pc_tao_yn is False: delete all PhieuChi and GiayBaoNo linked via detail.id_hd == invoice uuid
        if not getattr(instance, 'pc_tao_yn', False):
            self.xoa_chung_tu.delete_phieu_chi_by_hoa_don(instance)
            self.xoa_chung_tu.delete_giay_bao_no_by_hoa_don(instance)

        return instance

    def get_by_entity_slug_and_status(
        self, entity_slug: str, status: str
    ) -> QuerySet:  # noqa: C901
        """
        Get HoaDonMuaHangTrongNuocModel instances by entity slug and status.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        status : str
            The status to filter by.

        Returns
        -------
        QuerySet
            A queryset of HoaDonMuaHangTrongNuocModel instances.
        """
        return self.repository.get_by_entity_slug_and_status(entity_slug, status)

    def get_by_so_ct(
        self, entity_slug: str, so_ct: str
    ) -> Optional[HoaDonMuaHangTrongNuocModel]:  # noqa: C901
        """
        Get a HoaDonMuaHangTrongNuocModel by so_ct (document number).

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        so_ct : str
            The document number.

        Returns
        -------
        Optional[HoaDonMuaHangTrongNuocModel]
            The HoaDonMuaHangTrongNuocModel with the specified so_ct, or None if not found.
        """
        return self.repository.get_by_so_ct(entity_slug, so_ct)

    def get_filtered_data(
        self, entity_slug: str, status_filter: str = None, search_query: str = None
    ) -> QuerySet:  # noqa: C901
        """
        Get filtered HoaDonMuaHangTrongNuocModel data based on filters.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        status_filter : str, optional
            Status filter to apply.
        search_query : str, optional
            Search query to apply.

        Returns
        -------
        QuerySet
            A filtered QuerySet of HoaDonMuaHangTrongNuocModel instances.
        """
        queryset = self.get_all(entity_slug)

        # Apply status filter
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Apply search query
        if search_query:
            queryset = queryset.filter(
                models.Q(chung_tu_item__so_ct__icontains=search_query)
                | models.Q(ten_kh__icontains=search_query)
                | models.Q(dien_giai__icontains=search_query)
            )

        return queryset

        # ✅ UPDATE PHIEU_NHAP_KHO: Always sync PhieuNhapKho when pn_yn is true


#          # Check if we should update PhieuNhapKho (always if pn_yn=true)
#          should_update = should_update_phieu_nhap_kho(instance)

#          if should_update:
#              try:
#                  # Get existing PhieuNhapKho UUID
#                  existing_pn_uuid = get_existing_phieu_nhap_kho_uuid(instance)

#                  if existing_pn_uuid:
#                     # Update existing PhieuNhapKho with chi_tiet sync
#                     phieu_nhap_kho_data = prepare_data_for_phieu_nhap_kho_update(
#                         instance
#                     )

#                     updated_pn = self.phieu_nhap_kho_service.update(
#                         entity_slug=entity_slug,
#                        uuid=existing_pn_uuid,
#                       data=phieu_nhap_kho_data,
#                   )
#                 else:
#                     # Check if can restore soft deleted PhieuNhapKho
#                     if can_restore_phieu_nhap_kho(instance):
#                         # Restore soft deleted PhieuNhapKho using SafeDelete
#                         restored_pn = restore_phieu_nhap_kho(instance)
#                         if restored_pn:
#                             # Update restored PhieuNhapKho with current data
#                             phieu_nhap_kho_data = (
#                                 prepare_data_for_phieu_nhap_kho_update(instance)
#                             )
#                             self.phieu_nhap_kho_service.update(
#                                 entity_slug=entity_slug,
#                                 uuid=restored_pn.uuid,
#                                 data=phieu_nhap_kho_data,
#                             )
#                        else:
#                            print(f"❌ DEBUG: Failed to restore PhieuNhapKho")
#                    else:
#                         # Create new PhieuNhapKho if can't restore
#                         phieu_nhap_kho_data = prepare_data_for_phieu_nhap_kho(instance)
#                         self.phieu_nhap_kho_service.create(
#                             entity_slug=entity_slug, data=phieu_nhap_kho_data
#                          )
#             except Exception as e:
#                  raise ValidationError(f"Không thể cập nhật phiếu nhập kho: {str(e)}")
#          else:
#              # If pn_yn is False, delete existing PhieuNhapKho if exists
#              try:
#                 existing_pn_uuid = get_existing_phieu_nhap_kho_uuid(instance)
#                  if existing_pn_uuid:
#                      self.phieu_nhap_kho_service.delete(                         entity_slug=entity_slug, uuid=existing_pn_uuid
#                      )
#                 except Exception as e:
#                  import logging

#                  logger = logging.getLogger(__name__)
#                  logger.warning(f"⚠️ Could not delete PhieuNhapKho: {str(e)}")

# -
