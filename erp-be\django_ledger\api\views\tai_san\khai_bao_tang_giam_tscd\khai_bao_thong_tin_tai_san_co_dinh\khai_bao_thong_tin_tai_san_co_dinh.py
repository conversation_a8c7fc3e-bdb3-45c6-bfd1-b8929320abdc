"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ViewSet for KhaiBaoThongTinTaiSanCoDinh (Fixed Asset Information Declaration) model
"""

from rest_framework import permissions, status, viewsets  # noqa: F401
from rest_framework.exceptions import ValidationError  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.decorators import api_exception_handler
from django_ledger.api.serializers.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh import (  # noqa: F401,
    KhaiBaoThongTinTaiSanCoDinhSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.models import (  # noqa: F401,
    Khai<PERSON>aoThongTinTaiSanCoDinhModel,
)
from django_ledger.services.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh import (  # noqa: F401,
    KhaiBaoThongTinTaiSanCoDinhService,
)


class KhaiBaoThongTinTaiSanCoDinhViewSet(EntityRelatedViewSet):
    """
    A viewset for handling fixed asset information declaration CRUD operations

    This viewset provides endpoints for managing fixed asset information declarations.
    Each fixed asset information declaration has a code (ma_ts), name (ten_ts), and other details.  # noqa: E501
    """

    queryset = KhaiBaoThongTinTaiSanCoDinhModel.objects.all()
    serializer_class = KhaiBaoThongTinTaiSanCoDinhSerializer  # noqa: F811
    lookup_field = 'pk'
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811

    def __init__(self, **kwargs):  # noqa: C901
        super().__init__(**kwargs)
        self.service = KhaiBaoThongTinTaiSanCoDinhService()

    @api_exception_handler
    def list(self, request, entity_slug=None):  # noqa: F811,
        """
        List fixed asset information declarations

        Parameters
        ----------
        request : Request
            The request object.
        entity_slug : str
            The entity slug.
        """
        queryset = self.service.list(entity_slug=entity_slug)
        # Apply pagination if available
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @api_exception_handler
    def retrieve(self, request, entity_slug=None, pk=None):  # noqa: F811,
        """
        Retrieves a KhaiBaoThongTinTaiSanCoDinhModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        entity_slug : str
            The entity slug.
        pk : str
            The UUID of the KhaiBaoThongTinTaiSanCoDinhModel to retrieve.
        """
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)
        if not instance:
            return Response(
                {
                    'detail': 'Fixed Asset Information Declaration not found.'
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @api_exception_handler
    def create(self, request, entity_slug=None):  # noqa: F811,
        """
        Creates a new KhaiBaoThongTinTaiSanCoDinhModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        entity_slug : str
            The entity slug.
        """
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            # Extract data from serializer
            validated_data = serializer.validated_data
            # Create the KhaiBaoThongTinTaiSanCoDinhModel instance using service
            instance = self.service.create(
                entity_slug=entity_slug, data=validated_data
            )

            # Refresh instance to get all related data
            instance = self.service.get_by_id(
                entity_slug=entity_slug, uuid=instance.uuid
            )
            serializer = self.get_serializer(instance)
            return Response(
                serializer.data, status=status.HTTP_201_CREATED
            )

        return Response(
            serializer.errors, status=status.HTTP_400_BAD_REQUEST
        )

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: F811,
        """
        Updates an existing KhaiBaoThongTinTaiSanCoDinhModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Positional arguments.
        **kwargs : dict
            Keyword arguments including entity_slug and pk.
        """
        entity_slug = kwargs.get('entity_slug')
        pk = kwargs.get('pk')  # Get uuid value but keep pk variable name

        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)
        print("pk", pk)
        if not instance:
            return Response(
                {
                    'detail': 'Fixed Asset Information Declaration not found.'
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            # Extract data from serializer
            validated_data = serializer.validated_data
            # Update the KhaiBaoThongTinTaiSanCoDinhModel instance using service
            instance = self.service.update(
                entity_slug=entity_slug,
                uuid=pk,
                data=validated_data,
            )

            # Refresh instance to get all related data
            instance = self.service.get_by_id(
                entity_slug=entity_slug, uuid=pk
            )
            serializer = self.get_serializer(instance)
            return Response(serializer.data)

        return Response(
            serializer.errors, status=status.HTTP_400_BAD_REQUEST
        )

    @api_exception_handler
    def partial_update(self, request, *args, **kwargs):  # noqa: F811,
        """
        Partially updates an existing KhaiBaoThongTinTaiSanCoDinhModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Positional arguments.
        **kwargs : dict
            Keyword arguments including entity_slug and pk.
        """
        entity_slug = kwargs.get('entity_slug')
        pk = kwargs.get('uuid')  # Get uuid value but keep pk variable name
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)
        if not instance:
            return Response(
                {
                    'detail': 'Fixed Asset Information Declaration not found.'
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.get_serializer(
            instance, data=request.data, partial=True
        )
        if serializer.is_valid():
            # Extract data from serializer
            validated_data = serializer.validated_data
            # Update the KhaiBaoThongTinTaiSanCoDinhModel instance using service
            instance = self.service.update(
                entity_slug=entity_slug,
                uuid=pk,
                data=validated_data,
            )

            # Refresh instance to get all related data
            instance = self.service.get_by_id(
                entity_slug=entity_slug, uuid=pk
            )
            serializer = self.get_serializer(instance)
            return Response(serializer.data)

        return Response(
            serializer.errors, status=status.HTTP_400_BAD_REQUEST
        )

    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: F811,
        """
        Deletes a KhaiBaoThongTinTaiSanCoDinhModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Variable length argument list.
        **kwargs : dict
            Arbitrary keyword arguments containing entity_slug and uuid.
        """
        # Get entity_slug and uuid from kwargs
        entity_slug = kwargs.get('entity_slug')
        uuid = kwargs.get('pk')

        # Check if the record exists before deleting
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not instance:
            return Response(
                {
                    'detail': 'Fixed Asset Information Declaration not found.'
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        # Delete the record
        success = self.service.delete(entity_slug=entity_slug, uuid=uuid)
        if success:
            return Response(status=status.HTTP_204_NO_CONTENT)
        else:
            return Response(
                {'detail': 'Failed to delete the record.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
