"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Income Statement Mapping for Multi-Period Income Statement Report.
Expert ERP Implementation - 20+ years experience.

Complete mapping for BCKQKD indicators according to Thông tư 200.
"""

from typing import Dict, Any, List


class IncomeStatementMapping:
    """
    Mapping class for BCKQKD (Income Statement) indicators according to Thông tư 200.

    Expert ERP Implementation - Based on Vietnamese Circular 200 and 20+ years experience.

    This class provides mapping for 27 BCKQKD indicators including:
    - Revenue and sales indicators (01, 01A, 01C)
    - Revenue deductions (02, 02A, 02B, 02C)
    - Net revenue and gross profit (10, 11, 20)
    - Financial activities (21, 22, 23)
    - Operating expenses (25, 26)
    - Operating profit and other income (30, 31, 32, 40)
    - Pre-tax and after-tax profit (50, 51, 52, 52A, 52B, 60)
    - Earnings per share (70, 71)
    - Account mappings and corresponding accounts for each indicator
    """

    def __init__(self):
        """Initialize the mapping with production and business indicators."""
        self._initialize_indicators()

    def _initialize_indicators(self):
        """Initialize all BCKQKD indicators according to Thông tư 200."""

        # BCKQKD Indicators according to Thông tư 200
        # Based on official Vietnamese accounting circular 200
        self.indicators = {
            # 1. Doanh thu bán hàng và cung cấp dịch vụ
            "01": {
                "ma_so": "01",
                "chi_tieu": "1. Doanh thu bán hàng và cung cấp dịch vụ",
                "thuyet_minh": "Tổng doanh thu bán hàng và cung cấp dịch vụ",
                "accounts": [],
                "tk_doi_ung": [],
                "formula": "[01A]+[01C]",
                "dau_cuoi": "C",
                "type": "formula",
                "is_total": True,
                "account_codes": []
            },
            "01A": {
                "ma_so": "01A",
                "chi_tieu": "- Doanh thu bán hàng và cung cấp dịch vụ",
                "thuyet_minh": "Doanh thu từ bán hàng và cung cấp dịch vụ",
                "accounts": ["511"],
                "tk_doi_ung": ["131", "111", "112", "113", "331"],
                "formula": None,
                "dau_cuoi": "C",
                "type": "account_query",
                "is_total": False,
                "account_codes": ["511"]
            },
            "01C": {
                "ma_so": "01C",
                "chi_tieu": "- Thuế TTĐB, thuế XK, thuế GTGT tr/t phải nộp",
                "thuyet_minh": "Thuế tiêu thụ đặc biệt, thuế xuất khẩu, thuế GTGT",
                "accounts": ["3333"],
                "tk_doi_ung": ["5111"],
                "formula": None,
                "dau_cuoi": "C",
                "type": "account_query",
                "is_total": False,
                "account_codes": ["3333"]
            },

            # 2. Các khoản giảm trừ doanh thu
            "02": {
                "ma_so": "02",
                "chi_tieu": "2. Các khoản giảm trừ doanh thu",
                "thuyet_minh": "Tổng các khoản giảm trừ doanh thu",
                "accounts": [],
                "tk_doi_ung": [],
                "formula": "[02A]+[02B]+[02C]",
                "dau_cuoi": "N",
                "type": "formula",
                "is_total": True,
                "account_codes": []
            },
            "02A": {
                "ma_so": "02A",
                "chi_tieu": "+ Chiết khấu thương mại",
                "thuyet_minh": "Chiết khấu thương mại",
                "accounts": ["52111"],
                "tk_doi_ung": ["511"],
                "formula": None,
                "dau_cuoi": "N",
                "type": "account_query",
                "is_total": False,
                "account_codes": ["52111"]
            },
            "02B": {
                "ma_so": "02B",
                "chi_tieu": "+ Giảm giá",
                "thuyet_minh": "Giảm giá hàng bán",
                "accounts": ["52112"],
                "tk_doi_ung": ["511"],
                "formula": None,
                "dau_cuoi": "N",
                "type": "account_query",
                "is_total": False,
                "account_codes": ["52112"]
            },
            "02C": {
                "ma_so": "02C",
                "chi_tieu": "+ Hàng bán bị trả lại",
                "thuyet_minh": "Hàng bán bị trả lại",
                "accounts": ["5212"],
                "tk_doi_ung": ["511"],
                "formula": None,
                "dau_cuoi": "N",
                "type": "account_query",
                "is_total": False,
                "account_codes": ["5212"]
            },

            # 3. Doanh thu thuần
            "10": {
                "ma_so": "10",
                "chi_tieu": "3. Doanh thu thuần về bán hàng và cung cấp dịch vụ (10=01- 02)",
                "thuyet_minh": "Doanh thu thuần sau khi trừ các khoản giảm trừ",
                "accounts": [],
                "tk_doi_ung": [],
                "formula": "[01]-[02]",
                "dau_cuoi": "C",
                "type": "formula",
                "is_total": True,
                "account_codes": []
            },

            # 4. Giá vốn hàng bán
            "11": {
                "ma_so": "11",
                "chi_tieu": "4. Giá vốn hàng bán",
                "thuyet_minh": "Giá vốn hàng bán",
                "accounts": ["632"],
                "tk_doi_ung": ["911"],
                "formula": None,
                "dau_cuoi": "N",
                "type": "account_query",
                "is_total": False,
                "account_codes": ["632"]
            },

            # 5. Lợi nhuận gộp
            "20": {
                "ma_so": "20",
                "chi_tieu": "5. Lợi nhuận gộp về bán hàng và cung cấp dịch vụ (20=10-11)",
                "thuyet_minh": "Lợi nhuận gộp từ hoạt động kinh doanh",
                "accounts": [],
                "tk_doi_ung": [],
                "formula": "[10]-[11]",
                "dau_cuoi": "C",
                "type": "formula",
                "is_total": True,
                "account_codes": []
            },

            # 6. Doanh thu hoạt động tài chính
            "21": {
                "ma_so": "21",
                "chi_tieu": "6. Doanh thu hoạt động tài chính",
                "thuyet_minh": "Doanh thu từ hoạt động tài chính",
                "accounts": ["515"],
                "tk_doi_ung": ["911"],
                "formula": None,
                "dau_cuoi": "C",
                "type": "account_query",
                "is_total": False,
                "account_codes": ["515"]
            },

            # 7. Chi phí tài chính
            "22": {
                "ma_so": "22",
                "chi_tieu": "7. Chi phí tài chính",
                "thuyet_minh": "Chi phí tài chính",
                "accounts": ["635"],
                "tk_doi_ung": ["911"],
                "formula": None,
                "dau_cuoi": "N",
                "type": "account_query",
                "is_total": False,
                "account_codes": ["635"]
            },
            "23": {
                "ma_so": "23",
                "chi_tieu": "- Trong đó: Chi phí lãi vay",
                "thuyet_minh": "Chi phí lãi vay",
                "accounts": ["6352"],
                "tk_doi_ung": ["911"],
                "formula": None,
                "dau_cuoi": "N",
                "type": "account_query",
                "is_total": False,
                "account_codes": ["6352"]
            },

            # 8. Chi phí bán hàng
            "25": {
                "ma_so": "25",
                "chi_tieu": "8. Chi phí bán hàng",
                "thuyet_minh": "Chi phí bán hàng",
                "accounts": ["641"],
                "tk_doi_ung": ["911"],
                "formula": None,
                "dau_cuoi": "N",
                "type": "account_query",
                "is_total": False,
                "account_codes": ["641"]
            },

            # 9. Chi phí quản lý doanh nghiệp
            "26": {
                "ma_so": "26",
                "chi_tieu": "9. Chi phí quản lý doanh nghiệp",
                "thuyet_minh": "Chi phí quản lý doanh nghiệp",
                "accounts": ["642"],
                "tk_doi_ung": ["911"],
                "formula": None,
                "dau_cuoi": "N",
                "type": "account_query",
                "is_total": False,
                "account_codes": ["642"]
            },

            # 10. Lợi nhuận thuần từ hoạt động kinh doanh
            "30": {
                "ma_so": "30",
                "chi_tieu": "10. Lợi nhuận thuần từ hoạt động kinh doanh {30=20+(21-22)-(25+26)}",
                "thuyet_minh": "Lợi nhuận thuần từ hoạt động kinh doanh",
                "accounts": [],
                "tk_doi_ung": [],
                "formula": "[20]+[21]-[22]-[25]-[26]",
                "dau_cuoi": "C",
                "type": "formula",
                "is_total": True,
                "account_codes": []
            },

            # 11. Thu nhập khác
            "31": {
                "ma_so": "31",
                "chi_tieu": "11. Thu nhập khác",
                "thuyet_minh": "Thu nhập khác",
                "accounts": ["711"],
                "tk_doi_ung": ["911"],
                "formula": None,
                "dau_cuoi": "C",
                "type": "account_query",
                "is_total": False,
                "account_codes": ["711"]
            },

            # 12. Chi phí khác
            "32": {
                "ma_so": "32",
                "chi_tieu": "12. Chi phí khác",
                "thuyet_minh": "Chi phí khác",
                "accounts": ["811"],
                "tk_doi_ung": ["911"],
                "formula": None,
                "dau_cuoi": "N",
                "type": "account_query",
                "is_total": False,
                "account_codes": ["811"]
            },

            # 13. Lợi nhuận khác
            "40": {
                "ma_so": "40",
                "chi_tieu": "13. Lợi nhuận khác (40=31-32)",
                "thuyet_minh": "Lợi nhuận khác",
                "accounts": [],
                "tk_doi_ung": [],
                "formula": "[31]-[32]",
                "dau_cuoi": "C",
                "type": "formula",
                "is_total": True,
                "account_codes": []
            },

            # 14. Tổng lợi nhuận kế toán trước thuế
            "50": {
                "ma_so": "50",
                "chi_tieu": "14. Tổng lợi nhuận kế toán trước thuế (50=30+40)",
                "thuyet_minh": "Tổng lợi nhuận kế toán trước thuế",
                "accounts": [],
                "tk_doi_ung": [],
                "formula": "[30]+[40]",
                "dau_cuoi": "C",
                "type": "formula",
                "is_total": True,
                "account_codes": []
            },

            # 15. Chi phí thuế TNDN hiện hành
            "51": {
                "ma_so": "51",
                "chi_tieu": "15. Chi phí thuế TNDN hiện hành",
                "thuyet_minh": "Chi phí thuế thu nhập doanh nghiệp hiện hành",
                "accounts": ["8211"],
                "tk_doi_ung": ["911"],
                "formula": None,
                "dau_cuoi": "N",
                "type": "account_query",
                "is_total": False,
                "account_codes": ["8211"]
            },

            # 16. Chi phí thuế TNDN hoãn lại
            "52": {
                "ma_so": "52",
                "chi_tieu": "16. Chi phí thuế TNDN hoãn lại",
                "thuyet_minh": "Chi phí thuế thu nhập doanh nghiệp hoãn lại",
                "accounts": [],
                "tk_doi_ung": [],
                "formula": "[52A]-[52B]",
                "dau_cuoi": "N",
                "type": "formula",
                "is_total": True,
                "account_codes": []
            },
            "52A": {
                "ma_so": "52A",
                "chi_tieu": "- Chi phí thuế TNDN hoãn lại phải trả",
                "thuyet_minh": "Chi phí thuế TNDN hoãn lại phải trả",
                "accounts": ["8212"],
                "tk_doi_ung": ["911"],
                "formula": None,
                "dau_cuoi": "N",
                "type": "account_query",
                "is_total": False,
                "account_codes": ["8212"]
            },
            "52B": {
                "ma_so": "52B",
                "chi_tieu": "- Chi phí thuế TNDN hoãn lại phải thu",
                "thuyet_minh": "Chi phí thuế TNDN hoãn lại phải thu",
                "accounts": ["8212"],
                "tk_doi_ung": ["911"],
                "formula": None,
                "dau_cuoi": "C",
                "type": "account_query",
                "is_total": False,
                "account_codes": ["8212"]
            },

            # 17. Lợi nhuận sau thuế thu nhập doanh nghiệp
            "60": {
                "ma_so": "60",
                "chi_tieu": "17. Lợi nhuận sau thuế thu nhập doanh nghiệp (60=50-51-52)",
                "thuyet_minh": "Lợi nhuận sau thuế thu nhập doanh nghiệp",
                "accounts": [],
                "tk_doi_ung": [],
                "formula": "[50]-[51]-[52]",
                "dau_cuoi": "C",
                "type": "formula",
                "is_total": True,
                "account_codes": []
            },

            # 18. Lãi cơ bản trên cổ phiếu
            "70": {
                "ma_so": "70",
                "chi_tieu": "18. Lãi cơ bản trên cổ phiếu",
                "thuyet_minh": "Lãi cơ bản trên cổ phiếu",
                "accounts": [],
                "tk_doi_ung": [],
                "formula": None,
                "dau_cuoi": "C",
                "type": "account_query",
                "is_total": False,
                "account_codes": []
            },

            # 19. Lãi suy giảm trên cổ phiếu
            "71": {
                "ma_so": "71",
                "chi_tieu": "19. Lãi suy giảm trên cổ phiếu",
                "thuyet_minh": "Lãi suy giảm trên cổ phiếu",
                "accounts": [],
                "tk_doi_ung": [],
                "formula": None,
                "dau_cuoi": "C",
                "type": "account_query",
                "is_total": False,
                "account_codes": []
            },
        }

    def get_all_indicators(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all production and business indicators.

        Returns
        -------
        Dict[str, Dict[str, Any]]
            Dictionary of all indicators with their properties
        """
        return self.indicators

    def get_indicator_by_code(self, code: str) -> Dict[str, Any]:
        """
        Get indicator by code.

        Parameters
        ----------
        code : str
            Indicator code

        Returns
        -------
        Dict[str, Any]
            Indicator properties or empty dict if not found
        """
        return self.indicators.get(code, {})

    def get_accounts_for_indicator(self, code: str) -> List[str]:
        """
        Get account codes for a specific indicator.

        Parameters
        ----------
        code : str
            Indicator code

        Returns
        -------
        List[str]
            List of account codes
        """
        indicator = self.get_indicator_by_code(code)
        return indicator.get('accounts', [])

    def get_formula_for_indicator(self, code: str) -> str:
        """
        Get formula for a specific indicator.

        Parameters
        ----------
        code : str
            Indicator code

        Returns
        -------
        str
            Formula string or None if no formula
        """
        indicator = self.get_indicator_by_code(code)
        return indicator.get('formula')

    def get_ordered_indicators(self) -> List[str]:
        """
        Get indicators in display order according to Thông tư 200.

        Returns
        -------
        List[str]
            List of indicator codes in order (27 total indicators)
        """
        return [
            # BCKQKD according to Thông tư 200 - 27 indicators total
            "01", "01A", "01C",           # 1. Doanh thu bán hàng và cung cấp dịch vụ
            "02", "02A", "02B", "02C",    # 2. Các khoản giảm trừ doanh thu
            "10",                         # 3. Doanh thu thuần
            "11",                         # 4. Giá vốn hàng bán
            "20",                         # 5. Lợi nhuận gộp
            "21",                         # 6. Doanh thu hoạt động tài chính
            "22", "23",                   # 7. Chi phí tài chính (và chi phí lãi vay)
            "25",                         # 8. Chi phí bán hàng
            "26",                         # 9. Chi phí quản lý doanh nghiệp
            "30",                         # 10. Lợi nhuận thuần từ hoạt động kinh doanh
            "31",                         # 11. Thu nhập khác
            "32",                         # 12. Chi phí khác
            "40",                         # 13. Lợi nhuận khác
            "50",                         # 14. Tổng lợi nhuận kế toán trước thuế
            "51",                         # 15. Chi phí thuế TNDN hiện hành
            "52", "52A", "52B",           # 16. Chi phí thuế TNDN hoãn lại
            "60",                         # 17. Lợi nhuận sau thuế TNDN
            "70",                         # 18. Lãi cơ bản trên cổ phiếu
            "71",                         # 19. Lãi suy giảm trên cổ phiếu
        ]

    def is_formula_indicator(self, code: str) -> bool:
        """
        Check if indicator uses formula calculation.

        Parameters
        ----------
        code : str
            Indicator code

        Returns
        -------
        bool
            True if indicator uses formula
        """
        formula = self.get_formula_for_indicator(code)
        return formula is not None and formula.strip() != ""

    # Legacy methods for backward compatibility
    def get_all_items(self) -> Dict[str, Dict[str, Any]]:
        """Get all items (legacy method)."""
        return self.indicators

    def get_item(self, item_code: str) -> Dict[str, Any]:
        """Get specific item (legacy method)."""
        return self.get_indicator_by_code(item_code)

    def get_account_codes(self, item_code: str) -> List[str]:
        """Get account codes (legacy method)."""
        return self.get_accounts_for_indicator(item_code)
