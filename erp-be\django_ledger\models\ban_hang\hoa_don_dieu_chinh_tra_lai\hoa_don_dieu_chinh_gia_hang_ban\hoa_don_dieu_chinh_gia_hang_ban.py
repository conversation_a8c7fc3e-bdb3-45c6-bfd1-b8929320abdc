"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the HoaDonDieuChinhGiaHangBan model, which represents a Price Adjustment Invoice  # noqa: E501
which the EntityModel issues to its customers for the price adjustment of goods sold.
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401,
from django.utils import timezone  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models._mixins.chung_tu_mixins import ChungTuMixIn  # noqa: F401,
from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class HoaDonDieuChinhGiaHangBanModelQueryset(models.QuerySet):
    """
    A custom defined HoaDonDieuChinhGiaHangBan QuerySet.
    """

    def active(self):  # noqa: C901
        """
        Returns active HoaDonDieuChinhGiaHangBan instances.
        """
        return self.filter(status="1")

    def inactive(self):  # noqa: C901
        """
        Returns inactive HoaDonDieuChinhGiaHangBan instances.
        """
        return self.exclude(status="1")


class HoaDonDieuChinhGiaHangBanModelManager(models.Manager):
    """
    A custom defined HoaDonDieuChinhGiaHangBan Manager that will act as an interface to handle the  # noqa: E501
    HoaDonDieuChinhGiaHangBan database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom HoaDonDieuChinhGiaHangBanModelQueryset.
        """
        return HoaDonDieuChinhGiaHangBanModelQueryset(self.model, using=self._db)

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Returns a QuerySet of HoaDonDieuChinhGiaHangBan associated with a specific EntityModel.  # noqa: E501

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug used for filtering the QuerySet.

        Returns
        -------
        HoaDonDieuChinhGiaHangBanModelQueryset
            A QuerySet of HoaDonDieuChinhGiaHangBan with applied filters.
        """
        qs = self.get_queryset()
        return qs.filter(entity_model__slug__exact=entity_slug)


class HoaDonDieuChinhGiaHangBanModelAbstract(ChungTuMixIn, CreateUpdateMixIn):
    """
    This is the main abstract class which the HoaDonDieuChinhGiaHangBan database will inherit from.  # noqa: E501
    The HoaDonDieuChinhGiaHangBan inherits functionality from the following MixIns:

        1. :func:`ChungTuMixIn <django_ledger.models._mixins.chung_tu_mixins.ChungTuMixIn>`
        2. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUIDField
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    entity_model : ForeignKey
        The EntityModel that this invoice belongs to.
    ma_ngv : CharField
        The employee code.
    ma_kh : ForeignKey
        The customer code.
    ma_so_thue : CharField
        The tax code.
    ten_kh_thue : CharField
        The customer tax name.
    ong_ba : CharField
        The contact person.
    dia_chi : CharField
        The address.
    e_mail : CharField
        The email.
    tk : ForeignKey
        The account code.
    ma_tt : ForeignKey
        The payment term code.
    dien_giai : TextField
        The description.
    id : IntegerField
        The ID.
    unit_id : ForeignKey
        The unit ID.
    id_progress : IntegerField
        The progress ID.
    xprogress : CharField
        The progress.
    i_so_ct : IntegerField (from ChungTuMixIn)
        The internal document number.
    ma_nk : ForeignKey (from ChungTuMixIn)
        The document series code.
    so_ct : CharField (from ChungTuMixIn)
        The document number.
    ngay_ct : DateField (from ChungTuMixIn)
        The document date.
    ngay_lct : DateField (from ChungTuMixIn)
        The document creation date.
    chung_tu : ForeignKey (from ChungTuMixIn)
        The document reference.
    xdatetime2 : CharField
        The datetime 2.
    so_ct2 : CharField
        The document number 2.
    ma_nt : ForeignKey
        The currency code.
    ty_gia : DecimalField
        The exchange rate.
    status : CharField
        The status.
    transfer_yn : BooleanField
        The transfer status.
    ma_tthddt : CharField
        The electronic invoice status code.
    ma_pttt : CharField
        The payment method code.
    so_ct_hddt : CharField
        The electronic invoice document number.
    ngay_ct_hddt : DateField
        The electronic invoice document date.
    so_ct2_hddt : CharField
        The electronic invoice document number 2.
    ma_mau_ct_hddt : CharField
        The electronic invoice document template code.
    ten_vt_thue : CharField
        The tax product name.
    ma_kh9 : ForeignKey
        The customer code 9.
    ma_dc : CharField
        The address code.
    ly_do_huy : CharField
        The cancellation reason.
    ly_do : CharField
        The reason.
    t_so_luong : DecimalField
        The total quantity.
    t_tien_nt2 : DecimalField
        The total amount in foreign currency 2.
    t_tien2 : DecimalField
        The total amount 2.
    t_thue_nt : DecimalField
        The total tax in foreign currency.
    t_thue : DecimalField
        The total tax.
    t_tt_nt : DecimalField
        The total payment in foreign currency.
    t_tt : DecimalField
        The total payment.
    xfile : CharField
        The file.
    created_by : CharField
        The creator.
    created_at : DateTimeField
        The creation time.
    """

    # Khóa chính
    uuid = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        verbose_name=_("UUID"),
        help_text=_("Unique identifier for the record"),
    )

    # Entity reference
    entity_model = models.ForeignKey(
        "django_ledger.EntityModel",
        on_delete=models.CASCADE,
        verbose_name=_("Entity Model"),
        help_text=_("Entity that owns this record"),
    )

    # Thông tin người/đơn vị
    ma_ngv = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Mã người giao việc"),
        help_text=_("Mã người giao việc"),
    )

    ma_kh = models.ForeignKey(
        "django_ledger.CustomerModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã khách hàng"),
        help_text=_("Mã khách hàng"),
    )

    ma_so_thue = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Mã số thuế"),
        help_text=_("Mã số thuế"),
    )

    ten_kh_thue = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Tên khách hàng thuế"),
        help_text=_("Tên khách hàng thuế"),
    )

    ong_ba = models.CharField(
        max_length=100,
        verbose_name=_("Ông/Bà"),
        help_text=_("Ông/Bà"),
        null=True,
        blank=True,
    )

    dia_chi = models.CharField(
        max_length=255,
        verbose_name=_("Địa chỉ"),
        help_text=_("Địa chỉ"),
        null=True,
        blank=True,
    )

    e_mail = models.CharField(
        max_length=100,
        verbose_name=_("Email"),
        help_text=_("Email"),
        null=True,
        blank=True,
    )

    tk = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.CASCADE,
        related_name="hoa_don_dieu_chinh_gia_hang_ban_tk",
        verbose_name=_("Tài khoản"),
        help_text=_("Tài khoản"),
    )

    # Thông tin thanh toán
    ma_tt = models.ForeignKey(
        "django_ledger.HanThanhToanModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã thanh toán"),
        help_text=_("Mã thanh toán"),
    )

    dien_giai = models.TextField(
        verbose_name=_("Diễn giải"), help_text=_("Diễn giải"), null=True, blank=True
    )
    id = models.IntegerField(verbose_name=_("ID"), null=True, help_text=_("ID"))
    unit_id = models.ForeignKey(
        "django_ledger.EntityUnitModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("ID đơn vị"),
        help_text=_("ID đơn vị"),
    )

    id_progress = models.IntegerField(
        verbose_name=_("ID tiến trình"),
        null=True,
        help_text=_("ID tiến trình"),
    )

    xprogress = models.CharField(
        max_length=100,
        verbose_name=_("Tiến trình"),
        blank=True,
        help_text=_("Tiến trình"),
    )

    # Thông tin chứng từ được cung cấp bởi ChungTuMixIn
    # ChungTuMixIn provides: i_so_ct, ma_nk, so_ct, ngay_ct, ngay_lct, chung_tu

    xdatetime2 = models.CharField(
        max_length=50,
        verbose_name=_("Thời gian"),
        blank=True,
        help_text=_("Thời gian"),
    )

    so_ct2 = models.CharField(
        max_length=50,
        verbose_name=_("Số chứng từ 2"),
        blank=True,
        null=True,
        help_text=_("Số chứng từ 2"),
    )

    # Thông tin tiền tệ
    ma_nt = models.ForeignKey(
        "django_ledger.NgoaiTeModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã ngoại tệ"),
        help_text=_("Mã ngoại tệ"),
    )

    ty_gia = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=1,
        verbose_name=_("Tỷ giá"),
        help_text=_("Tỷ giá"),
    )

    # Trạng thái
    status = models.CharField(
        max_length=10,
        default="1",
        verbose_name=_("Trạng thái"),
        help_text=_("Trạng thái"),
    )

    transfer_yn = models.BooleanField(
        default=False,
        verbose_name=_("Đã chuyển"),
        help_text=_("Đã chuyển"),
    )

    # Thông tin hóa đơn điện tử
    ma_tthddt = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Mã trạng thái HĐĐT"),
        help_text=_("Mã trạng thái HĐĐT"),
    )

    ma_pttt = models.ForeignKey(
        "django_ledger.PhuongThucThanhToanModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã phương thức thanh toán"),
        help_text=_("Mã phương thức thanh toán"),
    )

    so_ct_hddt = models.CharField(
        max_length=50,
        verbose_name=_("Số chứng từ HĐĐT"),
        null=True,
        blank=True,
        help_text=_("Số chứng từ HĐĐT"),
    )

    ngay_ct_hddt = models.DateField(
        verbose_name=_("Ngày chứng từ HĐĐT"),
        null=True,
        blank=True,
        help_text=_("Ngày chứng từ HĐĐT"),
    )

    so_ct2_hddt = models.CharField(
        max_length=50,
        verbose_name=_("Số chứng từ 2 HĐĐT"),
        null=True,
        blank=True,
        help_text=_("Số chứng từ 2 HĐĐT"),
    )

    ma_mau_ct_hddt = models.CharField(
        max_length=50,
        verbose_name=_("Mã mẫu chứng từ HĐĐT"),
        null=True,
        blank=True,
        help_text=_("Mã mẫu chứng từ HĐĐT"),
    )

    # Thông tin hàng hóa
    ten_vt_thue = models.CharField(
        max_length=255,
        verbose_name=_("Tên vật tư thuế"),
        help_text=_("Tên vật tư thuế"),
        null=True,
        blank=True,
    )

    ma_kh9 = models.ForeignKey(
        "django_ledger.CustomerModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="hoa_don_dieu_chinh_gia_hang_ban_kh9",
        verbose_name=_("Mã khách hàng 9"),
        help_text=_("Mã khách hàng 9"),
    )

    ma_dc = models.ForeignKey(
        "django_ledger.DiaChiModel",
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_("Mã địa chỉ"),
        blank=True,
        help_text=_("Mã địa chỉ"),
    )

    # Lý do
    ly_do_huy = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Lý do hủy"),
        help_text=_("Lý do hủy"),
    )

    ly_do = models.CharField(
        max_length=255,
        verbose_name=_("Lý do"),
        help_text=_("Lý do"),
        null=True,
        blank=True,
    )

    # Tổng tiền
    t_so_luong = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Tổng số lượng"),
        help_text=_("Tổng số lượng"),
    )

    t_tien_nt2 = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Tổng tiền ngoại tệ 2"),
        help_text=_("Tổng tiền ngoại tệ 2"),
    )

    t_tien2 = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Tổng tiền 2"),
        help_text=_("Tổng tiền 2"),
    )

    t_thue_nt = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Tổng thuế ngoại tệ"),
        help_text=_("Tổng thuế ngoại tệ"),
    )

    t_thue = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Tổng thuế"),
        help_text=_("Tổng thuế"),
    )

    t_tt_nt = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Tổng thanh toán ngoại tệ"),
        help_text=_("Tổng thanh toán ngoại tệ"),
    )

    t_tt = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Tổng thanh toán"),
        help_text=_("Tổng thanh toán"),
    )

    # Metadata
    xfile = models.CharField(
        max_length=255,
        verbose_name=_("File"),
        blank=True,
        help_text=_("File"),
    )

    # Thông tin về người tạo và thời gian
    created_by = models.CharField(
        max_length=100,
        default="qtrieukobietcode",
        verbose_name=_("Người tạo"),
        help_text=_("Người tạo"),
    )

    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name=_("Thời gian tạo"),
        help_text=_("Thời gian tạo"),
    )

    # ✅ ENHANCED: Ledger relationship for 1:1 mapping with invoice
    # Following pattern from HoaDonBanHangModel for unified accounting
    ledger = models.OneToOneField(
        'django_ledger.LedgerModel',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_("Sổ cái"),
        help_text=_("Sổ cái được tạo cho hóa đơn điều chỉnh giá này"),
        related_name="hoa_don_dieu_chinh_gia_hang_ban",
    )

    objects = HoaDonDieuChinhGiaHangBanModelManager.from_queryset(
        HoaDonDieuChinhGiaHangBanModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _("Hóa đơn điều chỉnh giá hàng bán")
        verbose_name_plural = _("Hóa đơn điều chỉnh giá hàng bán")
        indexes = [
            models.Index(fields=["entity_model"]),
            models.Index(fields=["status"]),
            models.Index(fields=["ledger"]),
        ]

    def __str__(self):  # noqa: C901
        return f"{self.so_ct}: {self.dien_giai}"


class HoaDonDieuChinhGiaHangBanModel(HoaDonDieuChinhGiaHangBanModelAbstract):
    """
    Base HoaDonDieuChinhGiaHangBan Model Implementation
    """

    class Meta(HoaDonDieuChinhGiaHangBanModelAbstract.Meta):
        abstract = False
        db_table = "hoa_don_dieu_chinh_gia_hang_ban"
