"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiTietPhieuNhapChiPhiMuaHangRepository, which handles database operations  # noqa: E501
for the ChiTietPhieuNhapChiPhiMuaHangModel.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models import (  # noqa: F401,
    AccountModel,
    BoPhanModel,
    DonViTinhModel,
    KhoHangModel,
    VatTuModel,
)
from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import (  # noqa: F401,
    ChiTietPhieuNhapChiPhiMuaHangModel,
    PhieuNhapChiPhiMuaHangModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class ChiTietPhieuNhapChiPhiMuaHangRepository(BaseRepository):
    """
    Repository class for ChiTietPhieuNhapChiPhiMuaHangModel.
    Handles database operations for the model.
    """

    def __init__(self):  # noqa: C901
        super().__init__(model_class=ChiTietPhieuNhapChiPhiMuaHangModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for ChiTietPhieuNhapChiPhiMuaHangModel.

        Returns
        -------
        QuerySet
            The base queryset for ChiTietPhieuNhapChiPhiMuaHangModel.
        """
        return self.model_class.objects.all()

    def get_by_id(
        self, uuid: Union[str, UUID]
    ) -> Optional[ChiTietPhieuNhapChiPhiMuaHangModel]:  # noqa: C901
        """
        Retrieves a ChiTietPhieuNhapChiPhiMuaHangModel by its UUID.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhieuNhapChiPhiMuaHangModel to retrieve.

        Returns
        -------
        Optional[ChiTietPhieuNhapChiPhiMuaHangModel]
            The ChiTietPhieuNhapChiPhiMuaHangModel with the given UUID, or None if not found.  # noqa: E501
        """
        try:
            return self.model_class.objects.get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def list_for_phieu_nhap(
        self, phieu_nhap_id: Union[str, UUID], **kwargs
    ) -> QuerySet:  # noqa: C901
        """
        Lists ChiTietPhieuNhapChiPhiMuaHangModel instances for a specific purchase expense receipt.  # noqa: E501

        Parameters
        ----------
        phieu_nhap_id : Union[str, UUID]
            The UUID of the PhieuNhapChiPhiMuaHangModel.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of ChiTietPhieuNhapChiPhiMuaHangModel instances.
        """
        return self.model_class.objects.for_phieu_nhap(
            phieu_nhap_id=phieu_nhap_id
        ).filter(**kwargs)

    def create(
        self, phieu_nhap_id: Union[str, UUID], data: Dict[str, Any]
    ) -> ChiTietPhieuNhapChiPhiMuaHangModel:  # noqa: C901
        """
        Creates a new ChiTietPhieuNhapChiPhiMuaHangModel instance.

        Parameters
        ----------
        phieu_nhap_id : Union[str, UUID]
            The UUID of the PhieuNhapChiPhiMuaHangModel.
        data : Dict[str, Any]
            The data for the new ChiTietPhieuNhapChiPhiMuaHangModel.

        Returns
        -------
        ChiTietPhieuNhapChiPhiMuaHangModel
            The created ChiTietPhieuNhapChiPhiMuaHangModel instance.
        """
        # Get the PhieuNhapChiPhiMuaHangModel instance
        phieu_nhap = PhieuNhapChiPhiMuaHangModel.objects.get(uuid=phieu_nhap_id)

        # Convert UUIDs to model instances
        data = self.convert_uuids_to_model_instances(data)

        # Create the ChiTietPhieuNhapChiPhiMuaHangModel instance
        instance = self.model_class(phieu_nhap=phieu_nhap, **data)
        instance.save()

        return instance

    def update(
        self, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[ChiTietPhieuNhapChiPhiMuaHangModel]:  # noqa: C901
        """
        Updates an existing ChiTietPhieuNhapChiPhiMuaHangModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhieuNhapChiPhiMuaHangModel to update.
        data : Dict[str, Any]
            The data to update the ChiTietPhieuNhapChiPhiMuaHangModel with.

        Returns
        -------
        Optional[ChiTietPhieuNhapChiPhiMuaHangModel]
            The updated ChiTietPhieuNhapChiPhiMuaHangModel instance, or None if not found.  # noqa: E501
        """
        instance = self.get_by_id(uuid=uuid)
        if instance:
            # Convert UUIDs to model instances
            data = self.convert_uuids_to_model_instances(data)
            for key, value in data.items():
                setattr(instance, key, value)
            instance.save()
            return instance
        return None

    def delete(self, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a ChiTietPhieuNhapChiPhiMuaHangModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhieuNhapChiPhiMuaHangModel to delete.

        Returns
        -------
        bool
            True if the ChiTietPhieuNhapChiPhiMuaHangModel was deleted, False otherwise.
        """
        instance = self.get_by_id(uuid=uuid)
        if instance:
            instance.delete()
            return True
        return False

    def convert_uuids_to_model_instances(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert UUID strings to model instances for foreign key fields.
        This method extends the base implementation to specifically handle
        CharField fields that should not be converted.
        """
        # Create a copy and exclude CharField fields that should not be converted
        data_copy = data.copy()

        # Fields that are CharField and should NOT be converted to model instances
        char_fields = [
            'id_hd',  # This should remain as UUID string, not converted to model instance
            'line_hd',
        ]

        # Temporarily remove CharField fields to prevent conversion
        char_field_values = {}
        for field in char_fields:
            if field in data_copy:
                char_field_values[field] = data_copy.pop(field)

        # Call parent method for other fields
        data_copy = super().convert_uuids_to_model_instances(data_copy)

        # Restore CharField fields
        for field, value in char_field_values.items():
            data_copy[field] = value

        return data_copy