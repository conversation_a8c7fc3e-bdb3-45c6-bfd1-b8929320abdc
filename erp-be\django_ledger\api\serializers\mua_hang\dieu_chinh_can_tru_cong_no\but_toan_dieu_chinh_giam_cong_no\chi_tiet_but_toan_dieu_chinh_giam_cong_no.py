"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietButToanDieuChinhGiamCongNoMuaHangModel (Purchase Debt Reduction Adjustment Detail) model.
"""

from rest_framework import serializers

from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.tax import TaxModelSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer
from django_ledger.models.mua_hang.dieu_chinh_can_tru_cong_no.but_toan_dieu_chinh_giam_cong_no import (
    ChiTietButToanDieuChinhGiamCongNoMuaHangModel,
)


class ChiTietButToanDieuChinhGiamCongNoSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietButToanDieuChinhGiamCongNoMuaHangModel.
    """

    # Data fields for foreign key relationships
    tk_no_data = serializers.SerializerMethodField()
    tk_co_data = serializers.SerializerMethodField()
    ma_kh_data = serializers.SerializerMethodField()
    id_hd_data = serializers.SerializerMethodField()
    ma_vt_data = serializers.SerializerMethodField()
    ma_thue_data = serializers.SerializerMethodField()
    ma_bp_data = serializers.SerializerMethodField()
    ma_vv_data = serializers.SerializerMethodField()
    ma_phi_data = serializers.SerializerMethodField()
    ma_kho_data = serializers.SerializerMethodField()
    ma_td_data = serializers.SerializerMethodField()
    ma_hd_data = serializers.SerializerMethodField()
    ma_dtt_data = serializers.SerializerMethodField()
    ma_ku_data = serializers.SerializerMethodField()
    ma_sp_data = serializers.SerializerMethodField()
    ma_cp0_data = serializers.SerializerMethodField()

    # # Custom field names for API response - map model fields to API field names
    # tien = serializers.SerializerMethodField()
    # tien_nt = serializers.SerializerMethodField()

    class Meta:
        model = ChiTietButToanDieuChinhGiamCongNoMuaHangModel
        fields = [
            "uuid",
            "but_toan",
            "stt",
            "tk_no",
            "tk_no_data",
            "tk_co",
            "tk_co_data",
            "dien_giai",
            "so_tien_nt",
            "so_tien",
            "tien_nt",  # API field name for so_tien_nt
            "tien",  # API field name for so_tien
            "ma_kh",
            "ma_kh_data",
            "id_hd",  # Contract ID field
            "id_hd_data",
            "ma_vt",
            "ma_vt_data",
            "so_luong",
            "don_gia_nt",
            "don_gia",
            "ma_thue",
            "ma_thue_data",
            "thue_suat",
            "tien_thue_nt",
            "tien_thue",
            "ma_bp",
            "ma_bp_data",
            "ma_vv",
            "ma_vv_data",
            "ma_phi",
            "ma_phi_data",
            "ma_kho",
            "ma_kho_data",
            "ma_td",
            "ma_td_data",
            "dien_giai_them",
            "du_cn",
            "ma_hd",
            "ma_hd_data",
            "ma_dtt",
            "ma_dtt_data",
            "ma_ku",
            "ma_ku_data",
            "ma_sp",
            "ma_sp_data",
            "ma_lsx",
            "ma_cp0",
            "ma_cp0_data",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "but_toan",
            "tk_no_data",
            "tk_co_data",
            "ma_kh_data",
            "id_hd_data",
            "ma_vt_data",
            "ma_thue_data",
            "ma_bp_data",
            "ma_vv_data",
            "ma_phi_data",
            "ma_kho_data",
            "ma_td_data",
            "ma_hd_data",
            "ma_dtt_data",
            "ma_ku_data",
            "ma_sp_data",
            "ma_cp0_data",
            # "tien_nt",  # Read-only API field
            # "tien",  # Read-only API field
            "created",
            "updated",
        ]

    def to_internal_value(self, data):
        """
        Override to handle field mapping.
        """
        # Create a copy to avoid modifying original data
        data_copy = data.copy() if hasattr(data, 'copy') else dict(data)

        # Field mapping: request field -> model field
        field_mapping = {
            'line': 'stt',  # line -> stt
            # 'tien_nt': 'so_tien_nt',  # tien_nt -> so_tien_nt
        }

        # Apply field mapping
        for request_field, model_field in field_mapping.items():
            if request_field in data_copy:
                data_copy[model_field] = data_copy.pop(request_field)

        return super().to_internal_value(data_copy)

    def get_tk_no_data(self, obj):
        """Get debit account data."""
        if obj.tk_no:
            return AccountModelSerializer(obj.tk_no).data
        return None

    def get_tk_co_data(self, obj):
        """Get credit account data."""
        if obj.tk_co:
            return AccountModelSerializer(obj.tk_co).data
        return None

    def get_ma_kh_data(self, obj):
        """Get customer data."""
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_ma_vt_data(self, obj):
        """Get item data."""
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_ma_thue_data(self, obj):
        """Get tax data."""
        if obj.ma_thue:
            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_ma_bp_data(self, obj):
        """Get department data."""
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):
        """Get job data."""
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_phi_data(self, obj):
        """Get expense data."""
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_kho_data(self, obj):
        """Get warehouse data."""
        if obj.ma_kho:
            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def get_ma_td_data(self, obj):
        """Get field data."""
        if obj.ma_td:
            # TruongDuLieuModel serializer would be needed here
            # For now, return basic data
            return {
                'uuid': str(obj.ma_td.uuid),
                'ma_td': getattr(obj.ma_td, 'ma_td', ''),
                'ten_td': getattr(obj.ma_td, 'ten_td', ''),
            }
        return None

    def get_id_hd_data(self, obj):  # noqa: C901
        """
        Get invoice data from id_hd UUID string.
        Checks both HoaDonMuaHangTrongNuocModel and HoaDonMuaDichVuModel.
        """
        if not obj.id_hd:
            return None

        try:
            # Import models here to avoid circular imports
            from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import (
                HoaDonMuaDichVuModel,
            )
            from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (
                HoaDonMuaHangTrongNuocModel,
            )

            # Normalize UUID format - add dashes if missing
            uuid_str = str(obj.id_hd).strip()
            if len(uuid_str) == 32 and '-' not in uuid_str:
                # Convert from 35fd95805ea14482a15209dc57472d66 to 35fd9580-5ea1-4482-a152-09dc57472d66
                uuid_str = f"{uuid_str[:8]}-{uuid_str[8:12]}-{uuid_str[12:16]}-{uuid_str[16:20]}-{uuid_str[20:]}"

            # Try to find in HoaDonMuaHangTrongNuocModel first
            try:
                hoa_don = HoaDonMuaHangTrongNuocModel.objects.get(uuid=uuid_str)
                # Get chung_tu_item data
                chung_tu_item = getattr(hoa_don, 'chung_tu_item', None)

                # Extract ma_ct from so_ct (document type code)
                ma_ct = self._extract_ma_ct_from_so_ct(
                    chung_tu_item.so_ct if chung_tu_item else hoa_don.so_ct
                )

                # Get tk_data (account information)
                tk_data = self._get_tk_data(hoa_don)

                return {
                    "ID": str(hoa_don.uuid),
                    "so_ct": chung_tu_item.so_ct if chung_tu_item else hoa_don.so_ct,
                    "ngay_ct": (
                        chung_tu_item.ngay_ct.isoformat()
                        if chung_tu_item and chung_tu_item.ngay_ct
                        else hoa_don.ngay_ct.isoformat() if hoa_don.ngay_ct else ''
                    ),
                    "so_hd": getattr(hoa_don, 'so_ct0', '') or '',
                    "ngay_hd": (
                        getattr(hoa_don, 'ngay_ct0', '').isoformat()
                        if getattr(hoa_don, 'ngay_ct0', None)
                        else ''
                    ),
                    "ma_ct": ma_ct,
                    "tien_hd_nt": float(getattr(hoa_don, 't_tt', 0) or 0),
                    "tien_con_phai_tt": float(getattr(hoa_don, 't_tt', 0) or 0),
                    "ngoai_te": "VND",  # Default currency
                    "tien_tren_hd": float(getattr(hoa_don, 't_tt', 0) or 0),
                    "dien_giai": getattr(hoa_don, 'dien_giai', '') or '',
                    "tk_data": tk_data,
                    "loai_hoa_don": "hoa_don_mua_hang_trong_nuoc",
                }
            except HoaDonMuaHangTrongNuocModel.DoesNotExist:
                pass

            # Try to find in HoaDonMuaDichVuModel
            try:
                hoa_don = HoaDonMuaDichVuModel.objects.get(uuid=uuid_str)
                # Get chung_tu_item data
                chung_tu_item = getattr(hoa_don, 'chung_tu_item', None)

                # Extract ma_ct from so_ct (document type code)
                ma_ct = self._extract_ma_ct_from_so_ct(
                    chung_tu_item.so_ct if chung_tu_item else hoa_don.so_ct
                )

                # Get tk_data (account information)
                tk_data = self._get_tk_data(hoa_don)

                return {
                    "ID": str(hoa_don.uuid),
                    "so_ct": chung_tu_item.so_ct if chung_tu_item else hoa_don.so_ct,
                    "ngay_ct": (
                        chung_tu_item.ngay_ct.isoformat()
                        if chung_tu_item and chung_tu_item.ngay_ct
                        else hoa_don.ngay_ct.isoformat() if hoa_don.ngay_ct else ''
                    ),
                    "so_hd": getattr(hoa_don, 'so_ct0', '') or '',
                    "ngay_hd": (
                        getattr(hoa_don, 'ngay_ct0', '').isoformat()
                        if getattr(hoa_don, 'ngay_ct0', None)
                        else ''
                    ),
                    "ma_ct": ma_ct,
                    "tien_hd_nt": float(getattr(hoa_don, 't_tt', 0) or 0),
                    "tien_con_phai_tt": float(getattr(hoa_don, 't_tt', 0) or 0),
                    "ngoai_te": "VND",  # Default currency
                    "tien_tren_hd": float(getattr(hoa_don, 't_tt', 0) or 0),
                    "dien_giai": getattr(hoa_don, 'dien_giai', '') or '',
                    "tk_data": tk_data,
                    "loai_hoa_don": "hoa_don_mua_dich_vu",
                }
            except HoaDonMuaDichVuModel.DoesNotExist:
                pass

            # If not found in either model, return basic info with debug
            return {
                "uuid": obj.id_hd,
                "so_ct": None,
                "ngay_ct": None,
                "dien_giai": f"Hóa đơn không tìm thấy với UUID: {uuid_str}",
                "t_tien": 0,
                "t_thue": 0,
                "t_tt": 0,
                "loai_hoa_don": "unknown",
            }

        except Exception as e:
            # Return error info for debugging
            return {
                "error": f"Lỗi khi tìm hóa đơn: {str(e)}",
                "uuid": obj.id_hd,
                "loai_hoa_don": "error",
            }

    def _extract_ma_ct_from_so_ct(self, so_ct):
        """
        Extract ma_ct (document type code) from so_ct.

        Parameters
        ----------
        so_ct : str
            Document number like 'HD1.08.25.000054'

        Returns
        -------
        str
            Document type code like 'HD'
        """
        if not so_ct:
            return ''

        # Extract prefix before first dot or number
        import re

        match = re.match(r'^([A-Z]+)', str(so_ct))
        return match.group(1) if match else ''

    def _get_tk_data(self, invoice) -> dict:
        """
        Get account (tk) data from invoice.

        Parameters
        ----------
        invoice : Model
            Invoice model instance

        Returns
        -------
        dict
            Account data with uuid and code, or None if no account
        """
        try:
            tk = getattr(invoice, 'tk', None)
            if not tk or not hasattr(tk, 'uuid'):
                return None

            return {
                'uuid': str(tk.uuid),
                'tk': getattr(tk, 'code', ''),
            }

        except Exception as e:
            # Log error but don't break the flow
            print(f"Error getting tk_data: {e}")
            return None

    def get_ma_hd_data(self, obj):
        """Get contract data."""
        if obj.ma_hd:
            return {
                'uuid': str(obj.ma_hd.uuid),
                'ma_hd': getattr(obj.ma_hd, 'ma_hd', ''),
                'ten_hd': getattr(obj.ma_hd, 'ten_hd', ''),
            }
        return None

    def get_ma_dtt_data(self, obj):
        """Get payment object data."""
        if obj.ma_dtt:
            return {
                'uuid': str(obj.ma_dtt.uuid),
                'ma_dtt': getattr(obj.ma_dtt, 'ma_dtt', ''),
                'ten_dtt': getattr(obj.ma_dtt, 'ten_dtt', ''),
            }
        return None

    def get_ma_ku_data(self, obj):
        """Get loan data."""
        if obj.ma_ku:
            return {
                'uuid': str(obj.ma_ku.uuid),
                'ma_ku': getattr(obj.ma_ku, 'ma_ku', ''),
                'ten_ku': getattr(obj.ma_ku, 'ten_ku', ''),
            }
        return None

    def get_ma_sp_data(self, obj):
        """Get product data."""
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):
        """Get invalid cost center data."""
        if obj.ma_cp0:
            return {
                'uuid': str(obj.ma_cp0.uuid),
                'ma_cp0': getattr(obj.ma_cp0, 'ma_cpkhl', ''),
                'ten_cp0': getattr(obj.ma_cp0, 'ten_cpkhl', ''),
            }
        return None

    def get_tien_nt(self, obj):
        """Get foreign currency amount - maps to so_tien_nt."""
        return obj.tien_nt

    def get_tien(self, obj):
        """Get VND amount - maps to so_tien."""
        return obj.tien
