"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Hoa Don Mua Hang Trong Nuoc (Domestic Purchase Invoice) view implementation.
"""

import logging

from rest_framework import permissions, status  # noqa: F401
from rest_framework.decorators import action  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.decorators import api_exception_handler  # noqa: F401,
from django_ledger.api.decorators.error_handling import (  # noqa: F401
    api_exception_handler,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_phi_chi_tiet_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ChiPhiChiTietHoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_phi_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ChiPhiHoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_tiet_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ChiTietHoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    HoaDonMuaHangTrongNuocModelCreateUpdateSerializer,
    HoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.thue_hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ThueHoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    HoaDonMuaHangTrongNuocModel,
)
from django_ledger.services.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    HoaDonMuaHangTrongNuocService,
)

logger = logging.getLogger(__name__)


class HoaDonMuaHangTrongNuocModelViewSet(EntityRelatedViewSet):
    """
    ViewSet for HoaDonMuaHangTrongNuocModel.
    Handles API requests for the model.
    """

    queryset = HoaDonMuaHangTrongNuocModel.objects.all()
    serializer_class = HoaDonMuaHangTrongNuocModelSerializer  # noqa: F811
    lookup_field = 'uuid'
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811

    def __init__(self, **kwargs):  # noqa: C901
        super().__init__(**kwargs)
        self.service = HoaDonMuaHangTrongNuocService()

    def get_queryset(self):  # noqa: C901
        """
        Get QuerySet of HoaDonMuaHangTrongNuocModel instances for the entity

        Returns
        -------
        QuerySet
            QuerySet of HoaDonMuaHangTrongNuocModel instances for the entity
        """
        context = self.get_serializer_context()
        entity_slug = self.kwargs.get('entity_slug')
        user_model = context.get('request').user
        return (
            HoaDonMuaHangTrongNuocModel.objects.for_entity(
                entity_slug=entity_slug, user_model=user_model
            )
            .prefetch_related(
                'chi_tiet_hoa_don',
                'chi_phi_hoa_don',
                'chi_phi_chi_tiet_hoa_don',
                'thue_hoa_don',
            )
            .select_related(
                'entity_model',
                'ma_nvmh',
                'tk',
                'ma_tt',
                'unit_id',
                'ma_kh',
                # ChungTu fields from ChungTuMixIn
                'chung_tu_item',
                'chung_tu_item__ma_nk',
                'chung_tu',
                'ma_nk_pn',
                'ma_nt',
            )
        )

    def get_serializer_context(self):  # noqa: C901
        """
        Returns the serializer context.
        """
        return {
            'request': self.request,
            'entity_slug': self.kwargs['entity_slug'],
        }

    def get_serializer(self, *args, **kwargs):  # noqa: C901
        """
        Returns the serializer instance.
        """
        kwargs['context'] = self.get_serializer_context()
        if self.action in ["create", "update", "partial_update"]:
            return HoaDonMuaHangTrongNuocModelCreateUpdateSerializer(*args, **kwargs)
        return HoaDonMuaHangTrongNuocModelSerializer(*args, **kwargs)

    @api_exception_handler
    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        Lists HoaDonMuaHangTrongNuocModel instances with related UUIDs mapped.

        Parameters
        ----------
        request : Request
            The request object.

        Returns
        -------
        Response
            A response containing the serialized HoaDonMuaHangTrongNuocModel instances.
        """
        entity_slug = self.kwargs.get("entity_slug")

        # Build filter kwargs from query parameters
        filter_kwargs = {}
        status_filter = request.query_params.get("status")
        if status_filter:
            filter_kwargs["status"] = status_filter
        vendor_id = request.query_params.get("vendor_id")
        if vendor_id:
            filter_kwargs["ma_ncc_id"] = vendor_id

        # Use service method that includes related UUID mapping
        hoa_don_list = self.service.list_with_related_uuids(
            entity_slug=entity_slug, user_model=request.user, **filter_kwargs
        )

        # Use the standard paginate_queryset method provided by DRF
        page = self.paginate_queryset(hoa_don_list)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(hoa_don_list, many=True)
        return Response(serializer.data)

    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new HoaDonMuaHangTrongNuocModel instance with details.

        Parameters
        ----------
        request : Request
            The request object.

        Returns
        -------
        Response
            The response object.
        """
        entity_slug = self.kwargs.get("entity_slug")

        # Get entity_model for validation
        from django_ledger.models import EntityModel

        entity_model = EntityModel.objects.get(slug=entity_slug)

        # Validate data using serializer with entity_model context
        serializer = self.get_serializer(
            data=request.data,
            context={'entity_slug': entity_slug, 'entity_model': entity_model},
        )
        serializer.is_valid(raise_exception=True)

        # Use service to create instance
        instance = self.service.create(
            entity_slug=entity_slug, data=serializer.validated_data
        )

        # Refetch instance with proper prefetch_related for nested data
        instance_with_nested = self.get_queryset().filter(uuid=instance.uuid).first()

        # Return the created instance
        response_serializer = HoaDonMuaHangTrongNuocModelSerializer(
            instance_with_nested
        )
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update an existing HoaDonMuaHangTrongNuocModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        args : tuple
            Additional arguments.
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        entity_slug = self.kwargs.get("entity_slug")

        # Get entity_model for validation
        from django_ledger.models import EntityModel

        entity_model = EntityModel.objects.get(slug=entity_slug)

        # For PUT requests, ensure required BillModel fields are present
        if not partial:
            required_fields = [
                'cash_account',
                'prepaid_account',
                'unearned_account',
            ]
            for field in required_fields:
                if field not in request.data:
                    # Get current value from instance
                    current_value = getattr(instance, field, None)
                    if current_value:
                        request.data[field] = (
                            str(current_value.uuid)
                            if hasattr(current_value, 'uuid')
                            else current_value
                        )

        # Validate data using serializer with entity_model context
        serializer = self.get_serializer(
            instance,
            data=request.data,
            partial=partial,
            context={'entity_slug': entity_slug, 'entity_model': entity_model},
        )
        serializer.is_valid(raise_exception=True)

        # Use service to update instance
        updated_instance = self.service.update_with_details(
            instance=instance, data=serializer.validated_data
        )

        # Return the updated instance
        response_serializer = HoaDonMuaHangTrongNuocModelSerializer(updated_instance)
        return Response(response_serializer.data)

    @api_exception_handler
    def retrieve(self, request, *args, **kwargs):  # noqa: C901
        """
        Retrieve a HoaDonMuaHangTrongNuocModel instance with all its related details.

        Parameters
        ----------
        request : Request
            The request object.
        args : tuple
            Additional arguments.
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object containing the invoice with its details.
        """
        entity_slug = self.kwargs.get('entity_slug')
        uuid = kwargs.get('uuid')

        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=uuid)

        if not instance:
            return Response(
                {'detail': 'HoaDonMuaHangTrongNuoc not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a HoaDonMuaHangTrongNuocModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        args : tuple
            Additional arguments.
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        instance = self.get_object()
        entity_slug = kwargs.get('entity_slug')
        self.service.delete(entity_slug=entity_slug, uuid=instance.uuid)
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=["get"])
    def chi_tiet(self, request, **kwargs):  # noqa: C901
        """
        Get the details of a HoaDonMuaHangTrongNuocModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        instance = self.get_object()
        # Get the details using service
        details = self.service.get_invoice_details(instance.uuid)
        # Serialize the details
        chi_tiet_serializer = ChiTietHoaDonMuaHangTrongNuocModelSerializer(
            details["chi_tiet"], many=True
        )
        chi_phi_serializer = ChiPhiHoaDonMuaHangTrongNuocModelSerializer(
            details["chi_phi"], many=True
        )
        chi_phi_chi_tiet_serializer = (
            ChiPhiChiTietHoaDonMuaHangTrongNuocModelSerializer(
                details["chi_phi_chi_tiet"], many=True
            )
        )
        thue_serializer = ThueHoaDonMuaHangTrongNuocModelSerializer(
            details["thue"], many=True
        )

        # Return the details
        return Response(
            {
                "chi_tiet_hoa_don": chi_tiet_serializer.data,
                "chi_phi_hoa_don": chi_phi_serializer.data,
                "chi_phi_chi_tiet_hoa_don": chi_phi_chi_tiet_serializer.data,
                "thue_hoa_don": thue_serializer.data,
            }
        )

    @action(detail=False, methods=["post"])
    @api_exception_handler
    def create_with_ledger(self, request, **kwargs):  # noqa: C901
        """
        Create a new HoaDonMuaHangTrongNuocModel with a ledger but without items.

        This endpoint implements the first step of the workflow:
        1. Create the bill with an empty items list
        2. Create the ledger and attach the bill_id to it

        Parameters
        ----------
        request : Request
            The request object containing the invoice data without items.
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object containing the created invoice with its ledger.
        """
        entity_slug = self.kwargs.get("entity_slug")
        # Validate data using serializer
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        # Use service to create invoice with ledger
        instance = self.service.create_with_ledger(
            entity_slug=entity_slug, data=serializer.validated_data
        )

        # Return the created instance
        response_serializer = HoaDonMuaHangTrongNuocModelSerializer(instance)
        return Response(
            {
                "message": "Invoice created with ledger successfully",
                "invoice": response_serializer.data,
                "ledger_uuid": str(instance.ledger.uuid),
            },
            status=status.HTTP_201_CREATED,
        )

    @action(detail=True, methods=["post"])
    @api_exception_handler
    def add_items(self, request, **kwargs):  # noqa: C901
        """
        Add items to an existing HoaDonMuaHangTrongNuocModel instance.

        This endpoint implements the second step of the workflow:
        3. Process transactions and journal entries
        4. Map the items back to the ledger

        Parameters
        ----------
        request : Request
            The request object containing the items to add.
            Expected format:
            {
                "chi_tiet_hoa_don": [...],  # Required
                "thue_hoa_don": [...],      # Optional
                "chi_phi_hoa_don": [...],   # Optional
                "chi_phi_chi_tiet_hoa_don": [...]    # Optional
            }
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object containing the added items.
        """
        instance = self.get_object()
        entity_slug = self.kwargs.get("entity_slug")
        # Extract items data from request
        items_data = {
            "chi_tiet_data": request.data.get("chi_tiet_hoa_don", []),
            "thue_data": request.data.get("thue_hoa_don", []),
            "chi_phi_data": request.data.get("chi_phi_hoa_don", []),
            "chi_phi_chi_tiet_data": request.data.get("chi_phi_chi_tiet_hoa_don", []),
        }

        # Validate that chi_tiet_data is provided
        if not items_data["chi_tiet_data"]:
            return Response(
                {"error": "chi_tiet_hoa_don is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Use service to add items and process accounting
        result = self.service.add_items_and_process_accounting(
            entity_slug=entity_slug,
            uuid=instance.uuid,
            items_data=items_data,
        )

        # Return the result
        return Response(
            {
                "message": "Items added and accounting processed successfully",
                "added_items": result["added_items"],
                "journal_entry_uuid": result["journal_entry_uuid"],
                "transaction_count": result["transaction_count"],
            },
            status=status.HTTP_201_CREATED,
        )

    # ✅ DEPRECATED: process_accounting endpoint is no longer needed
    # Accounting is now automatically handled in create/update/delete operations
    # following the HoaDonDieuChinhGiaHangBanService pattern

    # @action(detail=True, methods=["post"])
    # def process_accounting(self, request, **kwargs):
    #     """
    #     DEPRECATED: Accounting is now automatically processed in create/update operations.
    #     This endpoint is kept for backward compatibility but will return an error.
    #     """
    #     return Response(
    #         {
    #             "message": "This endpoint is deprecated. Accounting is now automatically processed during create/update operations.",
    #             "recommendation": "Use the standard create/update endpoints instead."
    #         },
    #         status=status.HTTP_410_GONE
    #     )

    @action(detail=False, methods=["get"])
    def search(self, request, **kwargs):  # noqa: C901
        """
        Search for HoaDonMuaHangTrongNuocModel instances.

        Parameters
        ----------
        request : Request
            The request object.
        kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        entity_slug = self.kwargs.get("entity_slug")
        query = request.query_params.get("q", "")
        # Use service to search for invoices
        queryset = self.service.search(entity_slug, query)
        # Paginate results
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
