"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietHoaDon model.
"""

from rest_framework import serializers

# Import all the serializers we need
from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc import (
    KheUocModelSerializer,
)
from django_ledger.api.serializers.danh_muc.ke_toan.chi_phi_khong_hop_le.chi_phi_khong_hop_le import (
    ChiPhiKhongHopLeSerializer,
)
from django_ledger.api.serializers.dich_vu import DichVuModelSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.tax import TaxModelSerializer
from django_ledger.api.serializers.tien_do_thanh_toan import DotThanhToanModelSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu import (
    ChiTietHoaDonModel,
)


class ChiTietHoaDonSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietHoaDonModel.
    """

    class Meta:
        model = ChiTietHoaDonModel
        fields = [
            'uuid',
            # 'hoa_don',
            'line',
            'ma_dv',
            'tk_dt',
            'dvt',
            'so_luong',
            'gia_nt2',
            'tien_nt2',
            'dien_giai',
            'tl_ck',
            'ck_nt',
            'tk_ck',
            'ten_tk_ck',
            'ma_thue',
            'thue_suat',
            'tk_thue_co',
            'ten_tk_thue_co',
            'thue_nt',
            'gia2',
            'tien2',
            'ck',
            'thue',
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_lsx',
            'ma_cp0',
            'created',
            'updated',
        ]
        read_only_fields = ['uuid', 'created', 'updated']
        swagger_schema_fields = {
            'title': 'ChiTietHoaDon',
            'description': 'Chi tiết hóa đơn dịch vụ model serializer',
        }


class ChiTietHoaDonNestedSerializer(serializers.ModelSerializer):
    """
    Nested serializer for ChiTietHoaDonModel.
    """

    # Reference data fields
    ma_dv_data = serializers.SerializerMethodField(read_only=True)
    tk_dt_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    tk_ck_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_co_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)
    hoa_don_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietHoaDonModel
        fields = [
            'uuid',
            'line',
            # 'hoa_don',
            'hoa_don_data',
            'ma_dv',
            'ma_dv_data',
            'tk_dt',
            'tk_dt_data',
            'dvt',
            'dvt_data',
            'so_luong',
            'gia_nt2',
            'tien_nt2',
            'dien_giai',
            'tl_ck',
            'ck_nt',
            'tk_ck',
            'tk_ck_data',
            'ten_tk_ck',
            'ma_thue',
            'ma_thue_data',
            'thue_suat',
            'tk_thue_co',
            'tk_thue_co_data',
            'ten_tk_thue_co',
            'thue_nt',
            'gia2',
            'tien2',
            'ck',
            'thue',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_lsx',
            'ma_cp0',
            'ma_cp0_data',
            'created',
            'updated',
        ]
        read_only_fields = ['uuid', 'created', 'updated']

    def get_ma_dv_data(self, obj):  # noqa: C901
        """Return service data if available"""
        if obj.ma_dv:
            return DichVuModelSerializer(obj.ma_dv).data
        return None

    def get_tk_dt_data(self, obj):  # noqa: C901
        """Return revenue account data if available"""
        if obj.tk_dt:
            return AccountModelSerializer(obj.tk_dt).data
        return None

    def get_dvt_data(self, obj):  # noqa: C901
        """Return unit data if available"""
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_tk_ck_data(self, obj):  # noqa: C901
        """Return discount account data if available"""
        if obj.tk_ck:
            return AccountModelSerializer(obj.tk_ck).data
        return None

    def get_ma_thue_data(self, obj):  # noqa: C901
        """Return tax data if available"""
        if obj.ma_thue:
            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_tk_thue_co_data(self, obj):  # noqa: C901
        """Return tax account data if available"""
        if obj.tk_thue_co:
            return AccountModelSerializer(obj.tk_thue_co).data
        return None

    def get_ma_bp_data(self, obj):  # noqa: C901
        """Return department data if available"""
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):  # noqa: C901
        """Return case data if available"""
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):  # noqa: C901
        """Return contract data if available"""
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_dtt_data(self, obj):  # noqa: C901
        """Return payment period data if available"""
        if obj.ma_dtt:
            # Handle case where ma_dtt is a string instead of object
            if isinstance(obj.ma_dtt, str):
                return {
                    'ma_dtt': obj.ma_dtt,
                    'ten_dtt': obj.ma_dtt,  # Use the string value as display name
                }
            return DotThanhToanModelSerializer(obj.ma_dtt).data
        return None

    def get_ma_ku_data(self, obj):  # noqa: C901
        """Return agreement data if available"""
        if obj.ma_ku:
            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):  # noqa: C901
        """Return fee data if available"""
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_sp_data(self, obj):  # noqa: C901
        """Return product data if available"""
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):  # noqa: C901
        """Return invalid cost data if available"""
        if obj.ma_cp0:
            return ChiPhiKhongHopLeSerializer(obj.ma_cp0).data
        return None

    def get_hoa_don_data(self, obj):  # noqa: C901
        if obj.hoa_don:
            so_ct = None
            if hasattr(obj.hoa_don, 'so_ct'):
                so_ct = obj.hoa_don.so_ct
            elif hasattr(obj.hoa_don, 'chung_tu_item') and obj.hoa_don.chung_tu_item:
                so_ct = getattr(obj.hoa_don.chung_tu_item, 'so_ct', None)

            return {'uuid': obj.hoa_don.uuid, 'so_ct': so_ct}
        return None
