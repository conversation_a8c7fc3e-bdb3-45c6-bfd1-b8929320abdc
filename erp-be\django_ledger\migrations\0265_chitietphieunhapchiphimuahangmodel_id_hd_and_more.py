# Generated by Django 4.2.10 on 2025-08-27 01:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('django_ledger', '0264_alter_bankfeedetailmodel_tk_cpnh_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='chitietphieunhapchiphimuahangmodel',
            name='id_hd',
            field=models.CharField(blank=True, help_text='UUID of HoaDonMuaHangTrongNuocModel', max_length=36, null=True, verbose_name='ID hóa đơn'),
        ),
        migrations.AddField(
            model_name='chitietphieunhapchiphimuahangmodel',
            name='line_hd',
            field=models.CharField(blank=True, help_text='UUID of ChiTietHoaDonMuaHangTrongNuocModel', max_length=36, null=True, verbose_name='Line hóa đơn'),
        ),
        migrations.AddField(
            model_name='chitietphieunhapdieuchinhgiahangmuamodel',
            name='id_hd',
            field=models.CharField(blank=True, help_text='UUID of HoaDonMuaHangTrongNuocModel', max_length=36, null=True, verbose_name='ID hóa đơn'),
        ),
        migrations.AddField(
            model_name='chitietphieuxuattralainhacungcapmodel',
            name='id_hd',
            field=models.CharField(blank=True, help_text='UUID of HoaDonMuaHangTrongNuocModel', max_length=36, null=True, verbose_name='ID hóa đơn'),
        ),
        migrations.AddField(
            model_name='chitietphieuxuattralainhacungcapmodel',
            name='line_hd',
            field=models.CharField(blank=True, help_text='UUID of ChiTietHoaDonMuaHangTrongNuocModel', max_length=36, null=True, verbose_name='Line hóa đơn'),
        ),
        migrations.AlterField(
            model_name='chitiethoadonbanhangmodel',
            name='id_dh',
            field=models.CharField(blank=True, help_text='UUID of DonHangModel', max_length=36, null=True, verbose_name='ID đơn hàng'),
        ),
        migrations.AlterField(
            model_name='chitiethoadonbanhangmodel',
            name='line_dh',
            field=models.CharField(blank=True, help_text='UUID of ChiTietDonHangModel', max_length=36, null=True, verbose_name='Line chi tiết đơn hàng'),
        ),
        migrations.AlterField(
            model_name='chitiethoadondichvutralaigiamgiamodel',
            name='id_hd',
            field=models.CharField(blank=True, help_text='UUID of HoaDonDichVuModel', max_length=36, null=True, verbose_name='ID hóa đơn'),
        ),
        migrations.AlterField(
            model_name='chitiethoadondichvutralaigiamgiamodel',
            name='line_hd',
            field=models.CharField(blank=True, help_text='UUID of ChiTietHoaDonDichVuModel', max_length=36, null=True, verbose_name='Line hóa đơn'),
        ),
        migrations.AlterField(
            model_name='chitiethoadondieuchinhgiahangbanmodel',
            name='id_hd',
            field=models.CharField(blank=True, help_text='UUID of HoaDonBanHangModel', max_length=36, null=True, verbose_name='ID hóa đơn'),
        ),
        migrations.AlterField(
            model_name='chitiethoadondieuchinhgiahangbanmodel',
            name='line_hd',
            field=models.CharField(blank=True, help_text='UUID of ChiTietHoaDonBanHangModel', max_length=36, null=True, verbose_name='Line hóa đơn'),
        ),
        migrations.AlterField(
            model_name='chitiethoadonmuahangtrongnuocmodel',
            name='line_dh',
            field=models.CharField(blank=True, help_text='Purchase order line UUID (ChiTietDonMuaHangModel.uuid)', max_length=36, null=True, verbose_name='Line đơn hàng'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhapdieuchinhgiahangmuamodel',
            name='line_hd',
            field=models.CharField(blank=True, help_text='UUID of ChiTietHoaDonMuaHangTrongNuocModel', max_length=36, null=True, verbose_name='Line hóa đơn'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='id_hd',
            field=models.CharField(blank=True, help_text='UUID of HoaDonBanHangModel', max_length=36, null=True, verbose_name='ID hóa đơn'),
        ),
        migrations.AlterField(
            model_name='chitietphieunhaphangbantralaimodel',
            name='line_hd',
            field=models.CharField(blank=True, help_text='UUID of ChiTietHoaDonBanHangModel', max_length=36, null=True, verbose_name='Line hóa đơn'),
        ),
    ]
