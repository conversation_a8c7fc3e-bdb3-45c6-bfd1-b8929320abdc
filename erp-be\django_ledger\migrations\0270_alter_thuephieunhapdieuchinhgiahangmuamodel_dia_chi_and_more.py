# Generated by Django 4.2.10 on 2025-08-29 02:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('django_ledger', '0269_alter_thuephieunhapdieuchinhgiahangmuamodel_so_ct0_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='thuephieunhapdieuchinhgiahangmuamodel',
            name='dia_chi',
            field=models.TextField(blank=True, help_text='Customer address', null=True, verbose_name='Địa chỉ'),
        ),
        migrations.AlterField(
            model_name='thuephieunhapdieuchinhgiahangmuamodel',
            name='ma_mau_bc',
            field=models.CharField(blank=True, help_text='Report template code', max_length=50, null=True, verbose_name='Mã mẫu báo cáo'),
        ),
        migrations.AlterField(
            model_name='thuephieunhapdieuchinhgiahangmuamodel',
            name='ma_mau_ct',
            field=models.Char<PERSON><PERSON>(blank=True, help_text='Document template code', max_length=50, null=True, verbose_name='<PERSON>ã mẫu chứng từ'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='thuephieunhapdieuchinhgiahangmuamodel',
            name='ma_so_thue',
            field=models.CharField(blank=True, help_text='Tax identification number', max_length=50, null=True, verbose_name='Mã số thuế'),
        ),
        migrations.AlterField(
            model_name='thuephieunhapdieuchinhgiahangmuamodel',
            name='ma_tc_thue',
            field=models.CharField(blank=True, help_text='Tax criteria code', max_length=50, null=True, verbose_name='Mã tiêu chí thuế'),
        ),
        migrations.AlterField(
            model_name='thuephieunhapdieuchinhgiahangmuamodel',
            name='ten_kh9',
            field=models.CharField(blank=True, help_text='Customer 9 name', max_length=255, null=True, verbose_name='Tên khách hàng 9'),
        ),
        migrations.AlterField(
            model_name='thuephieunhapdieuchinhgiahangmuamodel',
            name='ten_kh_thue',
            field=models.CharField(blank=True, help_text='Tax customer name', max_length=255, null=True, verbose_name='Tên khách hàng thuế'),
        ),
        migrations.AlterField(
            model_name='thuephieunhapdieuchinhgiahangmuamodel',
            name='ten_tk_du',
            field=models.CharField(blank=True, help_text='Balance account name', max_length=255, null=True, verbose_name='Tên tài khoản dư'),
        ),
        migrations.AlterField(
            model_name='thuephieunhapdieuchinhgiahangmuamodel',
            name='ten_tk_thue_no',
            field=models.CharField(blank=True, help_text='Tax debit account name', max_length=255, null=True, verbose_name='Tên tài khoản thuế nợ'),
        ),
        migrations.AlterField(
            model_name='thuephieunhapdieuchinhgiahangmuamodel',
            name='ten_tt',
            field=models.CharField(blank=True, help_text='Status name', max_length=255, null=True, verbose_name='Tên trạng thái'),
        ),
        migrations.AlterField(
            model_name='thuephieunhapdieuchinhgiahangmuamodel',
            name='ten_vt_thue',
            field=models.CharField(blank=True, help_text='Tax material name', max_length=255, null=True, verbose_name='Tên vật tư thuế'),
        ),
    ]
